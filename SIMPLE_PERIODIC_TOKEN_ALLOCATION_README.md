# Simple Periodic Token Allocation System

This is a simplified implementation of the periodic token allocation system that focuses on direct purchase flow without complex webhook handling.

## Overview

The simplified system provides:
- **Direct Purchase Flow**: Users complete purchases through Paddle checkout
- **Frontend-Initiated Subscription Creation**: Subscriptions are created when checkout completes
- **Automatic Token Allocation**: Periodic cron job allocates tokens based on active subscriptions
- **Simple Admin Controls**: Manual triggers for testing and monitoring

## How It Works

### 1. Purchase Flow
```
User selects plan → Paddle checkout → Frontend detects completion → Backend creates subscription → Tokens allocated
```

### 2. Periodic Allocation
```
Cron job (hourly) → Check active subscriptions → Allocate tokens for due subscriptions → Update billing periods
```

## Key Components

### Backend

**PaddleService** (Simplified)
- Removed webhook signature verification
- Added simple purchase verification
- Focuses on plan management

**SubscriptionService** 
- `processPurchase()` - Creates subscription from completed purchase
- `processPeriodicTokenAllocation()` - Handles scheduled token allocation
- Removed webhook event handlers

**PaddleRouter**
- `/api/paddle/complete-purchase` - Process completed purchases
- Removed `/api/paddle/webhook` endpoint
- Kept subscription management endpoints

### Frontend

**SimplePurchaseFlow Component**
- Plan selection with monthly/yearly options
- Direct Paddle checkout integration
- Automatic subscription creation on purchase completion
- Real-time feedback to users

**Enhanced PaddleService**
- `completePurchase()` - Calls backend when checkout completes
- Simplified event handling
- Better user feedback

## Usage Example

### 1. User Purchase Flow

```typescript
// User selects plan and completes purchase
const purchaseFlow = (
  <SimplePurchaseFlow 
    organizationId="org-123"
    userId="user-456" 
    userEmail="<EMAIL>"
  />
);

// When Paddle checkout completes, frontend automatically calls:
await PaddleApi.completePurchase({
  transactionId: "paddle_transaction_123",
  planId: "plan_pro_monthly",
  organizationId: "org-123",
  billingInterval: "monthly"
});
```

### 2. Backend Processing

```typescript
// Backend creates subscription and allocates initial tokens
const subscription = await subscriptionService.processPurchase({
  transactionId: "paddle_transaction_123",
  planId: "plan_pro_monthly", 
  organizationId: "org-123",
  billingInterval: "monthly"
});

// Immediate token allocation
await tokenService.addTokensToOrganization(
  "org-123",
  50000, // tokens for pro plan
  TokenSource.SUBSCRIPTION,
  expirationDate,
  "Initial allocation for subscription",
  subscription.id
);
```

### 3. Periodic Allocation

```typescript
// Cron job runs hourly
scheduledJobService.schedulePeriodicTokenAllocation();

// Finds subscriptions due for renewal
const dueSubscriptions = await Subscription.findAll({
  where: {
    status: 'active',
    nextBillingDate: { [Op.lte]: new Date() }
  }
});

// Allocates tokens for each subscription
for (const subscription of dueSubscriptions) {
  await allocateTokensForSubscription(subscription);
  await updateSubscriptionBillingPeriod(subscription);
}
```

## Configuration

### Plans Configuration

Plans are defined in `paddleService.ts`:

```typescript
const plans = [
  {
    id: 'plan_starter_monthly',
    name: 'Starter Monthly',
    tokens: '10000',
    price: '29.00',
    interval: 'month'
  },
  {
    id: 'plan_pro_monthly', 
    name: 'Pro Monthly',
    tokens: '50000',
    price: '99.00',
    interval: 'month'
  }
  // ... yearly variants
];
```

### Environment Setup

```bash
# Optional: Paddle configuration for production
PADDLE_API_KEY=your_paddle_api_key
PADDLE_ENVIRONMENT=sandbox  # or 'production'
```

## Testing

### 1. Manual Purchase Test

```bash
# 1. Start the application
npm run dev

# 2. Navigate to purchase flow
# 3. Select a plan and complete mock checkout
# 4. Verify subscription creation:
curl http://localhost:3000/api/paddle/subscriptions/org-123

# 5. Check token allocation:
curl http://localhost:3000/api/tokens/available/org-123
```

### 2. Periodic Allocation Test

```bash
# Manually trigger token allocation
curl -X POST http://localhost:3000/admin/subscriptions/trigger-token-allocation

# Check job status
curl http://localhost:3000/admin/subscriptions/jobs/status
```

## Advantages of Simplified Approach

1. **Easier to Implement**: No complex webhook handling
2. **Faster Development**: Direct frontend-to-backend communication
3. **Easier Testing**: No need for webhook simulation
4. **Better User Experience**: Immediate feedback on purchase completion
5. **Simpler Debugging**: Clear flow from frontend to backend

## Limitations

1. **Less Robust**: Relies on frontend to complete purchase flow
2. **No Automatic Updates**: Won't handle subscription changes from Paddle dashboard
3. **Manual Verification**: Purchase verification is simplified
4. **No Failed Payment Handling**: Doesn't automatically handle payment failures

## Production Considerations

1. **Purchase Verification**: Implement proper Paddle API verification
2. **Error Handling**: Add comprehensive error handling for failed purchases
3. **Monitoring**: Set up monitoring for subscription creation and token allocation
4. **Backup Strategy**: Ensure subscription data is included in backups
5. **Rate Limiting**: Add rate limiting to purchase endpoints

## Migration to Full Webhook System

If you later want to implement full webhook support:

1. Add webhook signature verification back to `paddleService.ts`
2. Implement webhook event handlers in `subscriptionService.ts`
3. Add `/api/paddle/webhook` endpoint back to `paddleRouter.ts`
4. Configure webhook URL in Paddle dashboard
5. Update frontend to rely less on purchase completion events

The current simplified system provides a solid foundation that can be enhanced with webhooks when needed.

## File Structure

```
Backend:
├── src/models/subscription.ts              # Subscription model
├── src/services/paddleService.ts           # Simplified Paddle integration  
├── src/services/subscriptionService.ts     # Subscription management
├── src/services/scheduledJobService.ts     # Cron job management
├── src/routes/PaddleRouter.ts              # Purchase and subscription APIs
└── src/migrations/                         # Database migrations

Frontend:
├── web/api/PaddleApi.ts                    # API client
├── web/services/PaddleService.ts           # Paddle checkout integration
├── web/views/platform/SimplePurchaseFlow.tsx    # Purchase UI
└── web/views/platform/SubscriptionManagement.tsx # Subscription management UI
```
