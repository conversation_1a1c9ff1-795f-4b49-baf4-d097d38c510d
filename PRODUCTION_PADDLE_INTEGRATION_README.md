# Production-Grade Paddle Integration

This document describes the production-grade implementation of Paddle integration for periodic token allocation using real Paddle APIs.

## Overview

The production implementation provides:
- **Real Paddle API Integration**: Uses official Paddle REST API v1
- **Production-Grade Verification**: Verifies transactions and subscriptions with Paddle
- **Dynamic Plan Management**: Fetches real plan data from Paddle
- **Robust Error Handling**: Comprehensive error handling and fallbacks
- **Type Safety**: Full TypeScript support with Paddle API types

## Architecture

### Backend Components

#### 1. Paddle API Client (`paddleApiClient.ts`)

**Features:**
- Full TypeScript types for Paddle API responses
- Automatic error handling and logging
- Support for sandbox and production environments
- Rate limiting and timeout handling
- Comprehensive API coverage (products, prices, subscriptions, transactions)

**Key Methods:**
```typescript
// Get all products and prices
await paddleClient.getProducts({ status: 'active' });
await paddleClient.getPrices({ status: 'active', recurring: true });

// Subscription management
await paddleClient.getSubscription(subscriptionId);
await paddleClient.cancelSubscription(subscriptionId, { effective_from: 'next_billing_period' });

// Transaction verification
await paddleClient.getTransaction(transactionId);
```

#### 2. Enhanced Paddle Service (`paddleService.ts`)

**Real API Integration:**
- Fetches actual plans from Paddle with product details
- Verifies purchases using Paddle transaction API
- Handles subscription lifecycle with real Paddle data
- Supports plan metadata for token allocation

**Key Features:**
```typescript
// Get real plans from Paddle
const plans = await paddleService.getPlans();

// Verify purchase with full transaction details
const verification = await paddleService.verifyPurchase(transactionId, organizationId);

// Cancel subscription in Paddle
await paddleService.cancelSubscriptionInPaddle(subscriptionId, immediately);
```

#### 3. Enhanced Subscription Service (`subscriptionService.ts`)

**Production Features:**
- Real purchase verification before subscription creation
- Dynamic token allocation based on Paddle plan metadata
- Billing period synchronization with Paddle
- Comprehensive error handling and logging

### Frontend Components

#### 1. Real Data Integration

**Dynamic Plan Loading:**
- Fetches real plans from Paddle API
- Handles plan metadata for token display
- Graceful fallback to mock data if API fails
- Real-time plan updates

**Enhanced UI:**
- Displays actual plan data from Paddle
- Shows real pricing and token allocations
- Handles plan variations and features
- Better error messaging

## Configuration

### Environment Variables

```bash
# Required Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_ENVIRONMENT=sandbox  # or 'production'

# Optional Webhook Configuration (for future webhook implementation)
PADDLE_WEBHOOK_SECRET=your_webhook_secret_here
```

### Paddle Dashboard Setup

1. **Create Products in Paddle:**
   ```json
   {
     "name": "Pro Monthly Subscription",
     "description": "50,000 tokens per month",
     "custom_data": {
       "tokens": "50000",
       "features": ["50,000 tokens/month", "Priority support", "API access"]
     }
   }
   ```

2. **Create Prices for Products:**
   ```json
   {
     "product_id": "pro_product_id",
     "name": "Pro Monthly",
     "billing_cycle": {
       "interval": "month",
       "frequency": 1
     },
     "unit_price": {
       "amount": "9900",
       "currency_code": "USD"
     }
   }
   ```

3. **Configure Custom Data:**
   - Add `tokens` field for token allocation
   - Add `features` array for plan features
   - Add `popular` boolean for highlighting plans

## API Endpoints

### Plan Management

```bash
# Get all available plans
GET /api/paddle/plans
Response: Array of PaddlePlan objects with real Paddle data

# Complete purchase
POST /api/paddle/complete-purchase
Body: {
  "transactionId": "txn_123",
  "planId": "pri_123",
  "organizationId": "org_123",
  "billingInterval": "monthly"
}
```

### Subscription Management

```bash
# Get organization subscriptions
GET /api/paddle/subscriptions/:organizationId

# Cancel subscription
POST /api/paddle/subscriptions/:subscriptionId/cancel
Body: { "cancelAtPeriodEnd": true }
```

## Usage Examples

### 1. Setting Up Plans in Paddle

```typescript
// Create a product in Paddle Dashboard
const product = {
  name: "Starter Plan",
  description: "Perfect for small projects",
  custom_data: {
    tokens: "10000",
    features: ["10,000 tokens/month", "Basic support", "API access"]
  }
};

// Create monthly price
const monthlyPrice = {
  product_id: product.id,
  name: "Starter Monthly",
  billing_cycle: { interval: "month", frequency: 1 },
  unit_price: { amount: "2900", currency_code: "USD" }
};

// Create yearly price
const yearlyPrice = {
  product_id: product.id,
  name: "Starter Yearly",
  billing_cycle: { interval: "year", frequency: 1 },
  unit_price: { amount: "29000", currency_code: "USD" }
};
```

### 2. Frontend Integration

```typescript
// Fetch real plans
const paddleService = getPaddleService();
const plans = await paddleService.fetchPlans();

// Plans now contain real Paddle data
plans.forEach(plan => {
  console.log(`Plan: ${plan.name}`);
  console.log(`Price: $${plan.unit_price.amount} ${plan.unit_price.currency_code}`);
  console.log(`Tokens: ${plan.custom_data?.tokens}`);
  console.log(`Features: ${plan.custom_data?.features?.join(', ')}`);
});
```

### 3. Purchase Verification

```typescript
// Backend automatically verifies with Paddle
const verification = await paddleService.verifyPurchase(transactionId, organizationId);

if (verification.isValid) {
  const { transaction, subscription } = verification;
  
  // Create subscription with real Paddle data
  const localSubscription = await subscriptionService.processPurchase({
    transactionId,
    planId: transaction.items[0].price_id,
    organizationId,
    billingInterval: subscription.billing_cycle.interval
  });
}
```

## Error Handling

### API Error Handling

```typescript
try {
  const plans = await paddleService.getPlans();
} catch (error) {
  if (error.response?.data?.error) {
    // Paddle API error
    logger.error('Paddle API Error:', {
      type: error.response.data.error.type,
      code: error.response.data.error.code,
      detail: error.response.data.error.detail
    });
  }
  
  // Fallback behavior
  throw new Error('Failed to fetch plans from Paddle');
}
```

### Frontend Error Handling

```typescript
const fetchPlans = async () => {
  try {
    const plans = await PaddleApi.fetchPlans();
    setPlans(plans.data);
  } catch (error) {
    console.error('Error fetching plans:', error);
    
    // Show user-friendly error
    alert('Failed to load subscription plans. Please refresh the page.');
    
    // Optional: Fall back to cached/mock data
    setPlans(getCachedPlans());
  }
};
```

## Testing

### 1. Sandbox Testing

```bash
# Set environment to sandbox
export PADDLE_ENVIRONMENT=sandbox
export PADDLE_API_KEY=your_sandbox_api_key

# Test plan fetching
curl http://localhost:3000/api/paddle/plans

# Test purchase completion
curl -X POST http://localhost:3000/api/paddle/complete-purchase \
  -H "Content-Type: application/json" \
  -d '{
    "transactionId": "txn_sandbox_123",
    "planId": "pri_sandbox_123",
    "organizationId": "org_test",
    "billingInterval": "monthly"
  }'
```

### 2. Production Testing

```bash
# Use Paddle's test mode for production testing
# Create test transactions in Paddle Dashboard
# Verify with real API calls but test data
```

## Monitoring and Logging

### Key Metrics to Monitor

1. **API Response Times**: Paddle API call latencies
2. **Error Rates**: Failed API calls and their reasons
3. **Plan Fetch Success**: Successful plan data retrieval
4. **Purchase Verification**: Success rate of transaction verification
5. **Subscription Creation**: Successful subscription creation rate

### Logging Examples

```typescript
// API call logging
logger.info('Fetching plans from Paddle', { 
  environment: this.environment,
  requestId: generateRequestId() 
});

// Error logging
logger.error('Paddle API Error', {
  endpoint: '/prices',
  error: error.response?.data,
  requestId: error.response?.headers?.['paddle-request-id']
});

// Success logging
logger.info('Purchase verified successfully', {
  transactionId,
  organizationId,
  planId: transaction.items[0].price_id,
  amount: transaction.details.totals.total
});
```

## Security Considerations

1. **API Key Security**: Store Paddle API keys securely
2. **Transaction Verification**: Always verify transactions with Paddle
3. **Rate Limiting**: Implement rate limiting for API calls
4. **Error Information**: Don't expose sensitive error details to frontend
5. **Audit Logging**: Log all subscription and payment operations

## Production Deployment

### 1. Environment Setup

```bash
# Production environment variables
PADDLE_API_KEY=your_production_api_key
PADDLE_ENVIRONMENT=production
PADDLE_WEBHOOK_SECRET=your_production_webhook_secret

# Database migrations
npm run migrate

# Install dependencies
npm install
```

### 2. Health Checks

```typescript
// Add health check endpoint
app.get('/health/paddle', async (req, res) => {
  try {
    const paddleClient = getPaddleApiClient();
    await paddleClient.getProducts({ per_page: 1 });
    res.json({ status: 'healthy', paddle: 'connected' });
  } catch (error) {
    res.status(503).json({ status: 'unhealthy', paddle: 'disconnected' });
  }
});
```

### 3. Monitoring Setup

- Set up alerts for Paddle API failures
- Monitor subscription creation success rates
- Track token allocation job performance
- Monitor plan data freshness

## Migration from Mock Data

If you have existing mock data, migrate gradually:

1. **Phase 1**: Deploy with fallback to mock data
2. **Phase 2**: Test with real Paddle data in staging
3. **Phase 3**: Switch to real data in production
4. **Phase 4**: Remove mock data fallbacks

The implementation provides seamless fallback mechanisms to ensure zero downtime during migration.
