// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		05A7F6E52C3FC585007E880C /* FireworkBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05A7F6E42C3FC585007E880C /* FireworkBridge.swift */; };
		05B753832C7C5FA600ED6576 /* splash.png in Resources */ = {isa = PBXBuildFile; fileRef = 05B753812C7C5FA500ED6576 /* splash.png */; };
		05B753842C7C5FA600ED6576 /* splash.gif in Resources */ = {isa = PBXBuildFile; fileRef = 05B753822C7C5FA600ED6576 /* splash.gif */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		3692D98F280DAD2100A116EE /* RNDynamicBundle.m in Sources */ = {isa = PBXBuildFile; fileRef = 3692D98D280DAD2100A116EE /* RNDynamicBundle.m */; };
		369895582868D28F0052FFB3 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 369895572868D28F0052FFB3 /* GoogleService-Info.plist */; };
		369A35E62B462B8500468E10 /* RNApptile.m in Sources */ = {isa = PBXBuildFile; fileRef = 369A35E52B462B8500468E10 /* RNApptile.m */; };
		36C404B9281CDA1000E43516 /* RNGetValues.m in Sources */ = {isa = PBXBuildFile; fileRef = 36C404B8281CDA1000E43516 /* RNGetValues.m */; };
		36D8310D28F9731900197481 /* ApptileApplePay.m in Sources */ = {isa = PBXBuildFile; fileRef = 36D8310728F9731900197481 /* ApptileApplePay.m */; };
		36D8310E28F9731900197481 /* ApplePayService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36D8310828F9731900197481 /* ApplePayService.swift */; };
		36D8310F28F9731900197481 /* Mappers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36D8310928F9731900197481 /* Mappers.swift */; };
		36D8311128F9731900197481 /* PaymentModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36D8310C28F9731900197481 /* PaymentModels.swift */; };
		36E05979286B831700D8010E /* Fontisto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05969286B831700D8010E /* Fontisto.ttf */; };
		36E0597A286B831700D8010E /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596A286B831700D8010E /* Octicons.ttf */; };
		36E0597B286B831700D8010E /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596B286B831700D8010E /* Feather.ttf */; };
		36E0597C286B831700D8010E /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596C286B831700D8010E /* Entypo.ttf */; };
		36E0597D286B831700D8010E /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596D286B831700D8010E /* FontAwesome5_Brands.ttf */; };
		36E0597E286B831700D8010E /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596E286B831700D8010E /* MaterialCommunityIcons.ttf */; };
		36E0597F286B831700D8010E /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E0596F286B831700D8010E /* AntDesign.ttf */; };
		36E05980286B831700D8010E /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05970286B831700D8010E /* Foundation.ttf */; };
		36E05981286B831700D8010E /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05971286B831700D8010E /* Ionicons.ttf */; };
		36E05982286B831700D8010E /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05972286B831700D8010E /* FontAwesome5_Solid.ttf */; };
		36E05983286B831700D8010E /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05973286B831700D8010E /* FontAwesome5_Regular.ttf */; };
		36E05984286B831700D8010E /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05974286B831700D8010E /* FontAwesome.ttf */; };
		36E05985286B831700D8010E /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05975286B831700D8010E /* Zocial.ttf */; };
		36E05986286B831700D8010E /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05976286B831700D8010E /* EvilIcons.ttf */; };
		36E05987286B831700D8010E /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05977286B831700D8010E /* SimpleLineIcons.ttf */; };
		36E05988286B831700D8010E /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 36E05978286B831700D8010E /* MaterialIcons.ttf */; };
		44F00B1C010056A27952F71A /* Pods_ReactNativeTSProject.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 183080CDD757CE1DDAF36695 /* Pods_ReactNativeTSProject.framework */; };
		6132EF182BDFF13200BBE14D /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 6132EF172BDFF13200BBE14D /* PrivacyInfo.xcprivacy */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		BA7F5D8935D370B1FAF70493 /* Pods_ImageNotification.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F367C66F23470DE9155C038E /* Pods_ImageNotification.framework */; };
		BD10C50C2AE05E5A0083B551 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDD0191C2ADFFE2C009138E5 /* UserNotifications.framework */; };
		BD10C50D2AE05E680083B551 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDD0191C2ADFFE2C009138E5 /* UserNotifications.framework */; };
		BD3EA0C9289398E800D71A05 /* icon.png in Resources */ = {isa = PBXBuildFile; fileRef = BD3EA0C8289398E800D71A05 /* icon.png */; };
		BD7BB6A6291E0A9E00DAF3F1 /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = BD7BB6A5291E0A9E00DAF3F1 /* NotificationService.m */; };
		BD7BB6AA291E0A9E00DAF3F1 /* ImageNotification.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = BD7BB6A2291E0A9E00DAF3F1 /* ImageNotification.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		BD7BB6B0291E0AE000DAF3F1 /* (null) in Embed Frameworks */ = {isa = PBXBuildFile; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		BD7BB6B3291E0C2900DAF3F1 /* (null) in Embed Frameworks */ = {isa = PBXBuildFile; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		BD9A0F7D2AE22A1800C663D1 /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = BD9A0F7C2AE22A1800C663D1 /* main.jsbundle */; };
		BDC10CE32A12325E00BCA800 /* appConfig.json in Resources */ = {isa = PBXBuildFile; fileRef = BDC10CE22A12325E00BCA800 /* appConfig.json */; };
		BDC10CE42A12325E00BCA800 /* appConfig.json in Resources */ = {isa = PBXBuildFile; fileRef = BDC10CE22A12325E00BCA800 /* appConfig.json */; };
		BDD0191D2ADFFE2C009138E5 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDD0191C2ADFFE2C009138E5 /* UserNotifications.framework */; };
		BDD0191F2ADFFE2C009138E5 /* UserNotificationsUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDD0191E2ADFFE2C009138E5 /* UserNotificationsUI.framework */; };
		BDD019222ADFFE2C009138E5 /* NotificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD019212ADFFE2C009138E5 /* NotificationViewController.swift */; };
		BDD019252ADFFE2C009138E5 /* MainInterface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BDD019232ADFFE2C009138E5 /* MainInterface.storyboard */; };
		BDD019292ADFFE2C009138E5 /* NotificationContentExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = BDD0191B2ADFFE2C009138E5 /* NotificationContentExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		BDFBBE8C2AE3C793007FC822 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = BDFBBE8B2AE3C793007FC822 /* assets */; };
		FBBB6644074DFEDC7BBDFD59 /* Pods_NotificationContentExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 28BF1B9CBFCB43D60054EFA6 /* Pods_NotificationContentExtension.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		BD7BB6A8291E0A9E00DAF3F1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BD7BB6A1291E0A9E00DAF3F1;
			remoteInfo = ImageNotification;
		};
		BDD019272ADFFE2C009138E5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = BDD0191A2ADFFE2C009138E5;
			remoteInfo = NotificationContentExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		BD7BB6AB291E0A9E00DAF3F1 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				BD7BB6AA291E0A9E00DAF3F1 /* ImageNotification.appex in Embed Foundation Extensions */,
				BDD019292ADFFE2C009138E5 /* NotificationContentExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD7BB6B1291E0AE000DAF3F1 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				BD7BB6B0291E0AE000DAF3F1 /* (null) in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD7BB6B4291E0C2900DAF3F1 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				BD7BB6B3291E0C2900DAF3F1 /* (null) in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		05A7F6E42C3FC585007E880C /* FireworkBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FireworkBridge.swift; sourceTree = "<group>"; };
		05B753812C7C5FA500ED6576 /* splash.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = splash.png; sourceTree = "<group>"; };
		05B753822C7C5FA600ED6576 /* splash.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = splash.gif; sourceTree = "<group>"; };
		0A920E058A106B058048D26C /* Pods-NotificationContentExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationContentExtension.release.xcconfig"; path = "Target Support Files/Pods-NotificationContentExtension/Pods-NotificationContentExtension.release.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* ReactNativeTSProject.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ReactNativeTSProject.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = ReactNativeTSProject/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = ReactNativeTSProject/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ReactNativeTSProject/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ReactNativeTSProject/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = ReactNativeTSProject/main.m; sourceTree = "<group>"; };
		183080CDD757CE1DDAF36695 /* Pods_ReactNativeTSProject.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ReactNativeTSProject.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		28BF1B9CBFCB43D60054EFA6 /* Pods_NotificationContentExtension.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NotificationContentExtension.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3692D98D280DAD2100A116EE /* RNDynamicBundle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RNDynamicBundle.m; path = ReactNativeTSProject/RNDynamicBundle.m; sourceTree = "<group>"; };
		3692D98E280DAD2100A116EE /* RNDynamicBundle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RNDynamicBundle.h; path = ReactNativeTSProject/RNDynamicBundle.h; sourceTree = "<group>"; };
		369895572868D28F0052FFB3 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		3698955A2868EE610052FFB3 /* ReactNativeTSProject.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = ReactNativeTSProject.entitlements; path = ReactNativeTSProject/ReactNativeTSProject.entitlements; sourceTree = "<group>"; };
		369A35E42B462B6800468E10 /* RNApptile.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNApptile.h; sourceTree = "<group>"; };
		369A35E52B462B8500468E10 /* RNApptile.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNApptile.m; sourceTree = "<group>"; };
		36A8C1FD28F9815C009BDD86 /* ReactNativeTSProjectRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = ReactNativeTSProjectRelease.entitlements; path = ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements; sourceTree = "<group>"; };
		36C404B7281CDA1000E43516 /* RNGetValues.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RNGetValues.h; path = ReactNativeTSProject/RNGetValues.h; sourceTree = "<group>"; };
		36C404B8281CDA1000E43516 /* RNGetValues.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RNGetValues.m; path = ReactNativeTSProject/RNGetValues.m; sourceTree = "<group>"; };
		36D8310728F9731900197481 /* ApptileApplePay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ApptileApplePay.m; sourceTree = "<group>"; };
		36D8310828F9731900197481 /* ApplePayService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ApplePayService.swift; sourceTree = "<group>"; };
		36D8310928F9731900197481 /* Mappers.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Mappers.swift; sourceTree = "<group>"; };
		36D8310B28F9731900197481 /* ApptileApplePay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ApptileApplePay.h; sourceTree = "<group>"; };
		36D8310C28F9731900197481 /* PaymentModels.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PaymentModels.swift; sourceTree = "<group>"; };
		36D8311228F9733100197481 /* ReactNativeTSProject-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ReactNativeTSProject-Bridging-Header.h"; sourceTree = "<group>"; };
		36E05969286B831700D8010E /* Fontisto.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Fontisto.ttf; sourceTree = "<group>"; };
		36E0596A286B831700D8010E /* Octicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Octicons.ttf; sourceTree = "<group>"; };
		36E0596B286B831700D8010E /* Feather.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Feather.ttf; sourceTree = "<group>"; };
		36E0596C286B831700D8010E /* Entypo.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Entypo.ttf; sourceTree = "<group>"; };
		36E0596D286B831700D8010E /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Brands.ttf; sourceTree = "<group>"; };
		36E0596E286B831700D8010E /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialCommunityIcons.ttf; sourceTree = "<group>"; };
		36E0596F286B831700D8010E /* AntDesign.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = AntDesign.ttf; sourceTree = "<group>"; };
		36E05970286B831700D8010E /* Foundation.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Foundation.ttf; sourceTree = "<group>"; };
		36E05971286B831700D8010E /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Ionicons.ttf; sourceTree = "<group>"; };
		36E05972286B831700D8010E /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Solid.ttf; sourceTree = "<group>"; };
		36E05973286B831700D8010E /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Regular.ttf; sourceTree = "<group>"; };
		36E05974286B831700D8010E /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome.ttf; sourceTree = "<group>"; };
		36E05975286B831700D8010E /* Zocial.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Zocial.ttf; sourceTree = "<group>"; };
		36E05976286B831700D8010E /* EvilIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = EvilIcons.ttf; sourceTree = "<group>"; };
		36E05977286B831700D8010E /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = SimpleLineIcons.ttf; sourceTree = "<group>"; };
		36E05978286B831700D8010E /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialIcons.ttf; sourceTree = "<group>"; };
		6132EF172BDFF13200BBE14D /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = ReactNativeTSProject/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = ReactNativeTSProject/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8FB9B77C537E9D225AC3766D /* Pods-NotificationContentExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationContentExtension.debug.xcconfig"; path = "Target Support Files/Pods-NotificationContentExtension/Pods-NotificationContentExtension.debug.xcconfig"; sourceTree = "<group>"; };
		950802BB9B6241E074FBAEF2 /* Pods-ReactNativeTSProject.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ReactNativeTSProject.debug.xcconfig"; path = "Target Support Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject.debug.xcconfig"; sourceTree = "<group>"; };
		AD8CB37B96A35A0DED9F02D1 /* Pods-ReactNativeTSProject.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ReactNativeTSProject.release.xcconfig"; path = "Target Support Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject.release.xcconfig"; sourceTree = "<group>"; };
		BD3EA0C8289398E800D71A05 /* icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = icon.png; sourceTree = "<group>"; };
		BD7BB6A2291E0A9E00DAF3F1 /* ImageNotification.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ImageNotification.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		BD7BB6A4291E0A9E00DAF3F1 /* NotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationService.h; sourceTree = "<group>"; };
		BD7BB6A5291E0A9E00DAF3F1 /* NotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationService.m; sourceTree = "<group>"; };
		BD7BB6A7291E0A9E00DAF3F1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		BD9A0F7C2AE22A1800C663D1 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		BDC10CE22A12325E00BCA800 /* appConfig.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = appConfig.json; sourceTree = "<group>"; };
		BDD019162ADFFDE6009138E5 /* ImageNotification.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ImageNotification.entitlements; sourceTree = "<group>"; };
		BDD0191B2ADFFE2C009138E5 /* NotificationContentExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationContentExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		BDD0191C2ADFFE2C009138E5 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		BDD0191E2ADFFE2C009138E5 /* UserNotificationsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotificationsUI.framework; path = System/Library/Frameworks/UserNotificationsUI.framework; sourceTree = SDKROOT; };
		BDD019212ADFFE2C009138E5 /* NotificationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationViewController.swift; sourceTree = "<group>"; };
		BDD019242ADFFE2C009138E5 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/MainInterface.storyboard; sourceTree = "<group>"; };
		BDD019262ADFFE2C009138E5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		BDD0192D2ADFFE31009138E5 /* NotificationContentExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NotificationContentExtension.entitlements; sourceTree = "<group>"; };
		BDFBBE8B2AE3C793007FC822 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		E0673C639EA34C55A9775F85 /* Pods-ImageNotification.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ImageNotification.debug.xcconfig"; path = "Target Support Files/Pods-ImageNotification/Pods-ImageNotification.debug.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		EE6062495A7E5A7E2E602FF6 /* Pods-ImageNotification.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ImageNotification.release.xcconfig"; path = "Target Support Files/Pods-ImageNotification/Pods-ImageNotification.release.xcconfig"; sourceTree = "<group>"; };
		F367C66F23470DE9155C038E /* Pods_ImageNotification.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ImageNotification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BD10C50C2AE05E5A0083B551 /* UserNotifications.framework in Frameworks */,
				44F00B1C010056A27952F71A /* Pods_ReactNativeTSProject.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD7BB69F291E0A9E00DAF3F1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BD10C50D2AE05E680083B551 /* UserNotifications.framework in Frameworks */,
				BA7F5D8935D370B1FAF70493 /* Pods_ImageNotification.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BDD019182ADFFE2C009138E5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BDD0191F2ADFFE2C009138E5 /* UserNotificationsUI.framework in Frameworks */,
				BDD0191D2ADFFE2C009138E5 /* UserNotifications.framework in Frameworks */,
				FBBB6644074DFEDC7BBDFD59 /* Pods_NotificationContentExtension.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* ReactNativeTSProject */ = {
			isa = PBXGroup;
			children = (
				36A8C1FD28F9815C009BDD86 /* ReactNativeTSProjectRelease.entitlements */,
				3698955A2868EE610052FFB3 /* ReactNativeTSProject.entitlements */,
				369895572868D28F0052FFB3 /* GoogleService-Info.plist */,
				36C404B7281CDA1000E43516 /* RNGetValues.h */,
				36C404B8281CDA1000E43516 /* RNGetValues.m */,
				3692D98E280DAD2100A116EE /* RNDynamicBundle.h */,
				3692D98D280DAD2100A116EE /* RNDynamicBundle.m */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				369A35E42B462B6800468E10 /* RNApptile.h */,
				369A35E52B462B8500468E10 /* RNApptile.m */,
				05A7F6E42C3FC585007E880C /* FireworkBridge.swift */,
			);
			name = ReactNativeTSProject;
			sourceTree = "<group>";
		};
		2413C9202BF5EA59009C6D0C /* NotificationServiceExtension */ = {
			isa = PBXGroup;
			children = (
			);
			path = NotificationServiceExtension;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				BDD0191C2ADFFE2C009138E5 /* UserNotifications.framework */,
				BDD0191E2ADFFE2C009138E5 /* UserNotificationsUI.framework */,
				F367C66F23470DE9155C038E /* Pods_ImageNotification.framework */,
				28BF1B9CBFCB43D60054EFA6 /* Pods_NotificationContentExtension.framework */,
				183080CDD757CE1DDAF36695 /* Pods_ReactNativeTSProject.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		36D8310628F9731900197481 /* ApplePayService */ = {
			isa = PBXGroup;
			children = (
				36D8310728F9731900197481 /* ApptileApplePay.m */,
				36D8310828F9731900197481 /* ApplePayService.swift */,
				36D8310928F9731900197481 /* Mappers.swift */,
				36D8310B28F9731900197481 /* ApptileApplePay.h */,
				36D8310C28F9731900197481 /* PaymentModels.swift */,
			);
			path = ApplePayService;
			sourceTree = "<group>";
		};
		36E05968286B831700D8010E /* Fonts */ = {
			isa = PBXGroup;
			children = (
				36E05969286B831700D8010E /* Fontisto.ttf */,
				36E0596A286B831700D8010E /* Octicons.ttf */,
				36E0596B286B831700D8010E /* Feather.ttf */,
				36E0596C286B831700D8010E /* Entypo.ttf */,
				36E0596D286B831700D8010E /* FontAwesome5_Brands.ttf */,
				36E0596E286B831700D8010E /* MaterialCommunityIcons.ttf */,
				36E0596F286B831700D8010E /* AntDesign.ttf */,
				36E05970286B831700D8010E /* Foundation.ttf */,
				36E05971286B831700D8010E /* Ionicons.ttf */,
				36E05972286B831700D8010E /* FontAwesome5_Solid.ttf */,
				36E05973286B831700D8010E /* FontAwesome5_Regular.ttf */,
				36E05974286B831700D8010E /* FontAwesome.ttf */,
				36E05975286B831700D8010E /* Zocial.ttf */,
				36E05976286B831700D8010E /* EvilIcons.ttf */,
				36E05977286B831700D8010E /* SimpleLineIcons.ttf */,
				36E05978286B831700D8010E /* MaterialIcons.ttf */,
			);
			name = Fonts;
			path = "../node_modules/react-native-vector-icons/Fonts";
			sourceTree = "<group>";
		};
		4B33D0F33FB2435B9BEE035A /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				05B753822C7C5FA600ED6576 /* splash.gif */,
				05B753812C7C5FA500ED6576 /* splash.png */,
				36D8311228F9733100197481 /* ReactNativeTSProject-Bridging-Header.h */,
				BDC10CE22A12325E00BCA800 /* appConfig.json */,
				BD3EA0C8289398E800D71A05 /* icon.png */,
				BD9A0F7C2AE22A1800C663D1 /* main.jsbundle */,
				BDFBBE8B2AE3C793007FC822 /* assets */,
				36D8310628F9731900197481 /* ApplePayService */,
				36E05968286B831700D8010E /* Fonts */,
				6132EF172BDFF13200BBE14D /* PrivacyInfo.xcprivacy */,
				13B07FAE1A68108700A75B9A /* ReactNativeTSProject */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				BD7BB6A3291E0A9E00DAF3F1 /* ImageNotification */,
				BDD019202ADFFE2C009138E5 /* NotificationContentExtension */,
				2413C9202BF5EA59009C6D0C /* NotificationServiceExtension */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				E233CBF5F47BEE60B243DCF8 /* Pods */,
				4B33D0F33FB2435B9BEE035A /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* ReactNativeTSProject.app */,
				BD7BB6A2291E0A9E00DAF3F1 /* ImageNotification.appex */,
				BDD0191B2ADFFE2C009138E5 /* NotificationContentExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BD7BB6A3291E0A9E00DAF3F1 /* ImageNotification */ = {
			isa = PBXGroup;
			children = (
				BDD019162ADFFDE6009138E5 /* ImageNotification.entitlements */,
				BD7BB6A4291E0A9E00DAF3F1 /* NotificationService.h */,
				BD7BB6A5291E0A9E00DAF3F1 /* NotificationService.m */,
				BD7BB6A7291E0A9E00DAF3F1 /* Info.plist */,
			);
			path = ImageNotification;
			sourceTree = "<group>";
		};
		BDD019202ADFFE2C009138E5 /* NotificationContentExtension */ = {
			isa = PBXGroup;
			children = (
				BDD0192D2ADFFE31009138E5 /* NotificationContentExtension.entitlements */,
				BDD019212ADFFE2C009138E5 /* NotificationViewController.swift */,
				BDD019232ADFFE2C009138E5 /* MainInterface.storyboard */,
				BDD019262ADFFE2C009138E5 /* Info.plist */,
			);
			path = NotificationContentExtension;
			sourceTree = "<group>";
		};
		E233CBF5F47BEE60B243DCF8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E0673C639EA34C55A9775F85 /* Pods-ImageNotification.debug.xcconfig */,
				EE6062495A7E5A7E2E602FF6 /* Pods-ImageNotification.release.xcconfig */,
				8FB9B77C537E9D225AC3766D /* Pods-NotificationContentExtension.debug.xcconfig */,
				0A920E058A106B058048D26C /* Pods-NotificationContentExtension.release.xcconfig */,
				950802BB9B6241E074FBAEF2 /* Pods-ReactNativeTSProject.debug.xcconfig */,
				AD8CB37B96A35A0DED9F02D1 /* Pods-ReactNativeTSProject.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* ReactNativeTSProject */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ReactNativeTSProject" */;
			buildPhases = (
				46FA0314395FC5D921FE6F8C /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				BD7BB6AB291E0A9E00DAF3F1 /* Embed Foundation Extensions */,
				BD7BB6B1291E0AE000DAF3F1 /* Embed Frameworks */,
				D7948117D3567B56D0CBCC3E /* [CP] Embed Pods Frameworks */,
				AF228128E0A21D94F09F6831 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				BD7BB6A9291E0A9E00DAF3F1 /* PBXTargetDependency */,
				BDD019282ADFFE2C009138E5 /* PBXTargetDependency */,
			);
			name = ReactNativeTSProject;
			productName = ReactNativeTSProject;
			productReference = 13B07F961A680F5B00A75B9A /* ReactNativeTSProject.app */;
			productType = "com.apple.product-type.application";
		};
		BD7BB6A1291E0A9E00DAF3F1 /* ImageNotification */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BD7BB6AE291E0A9E00DAF3F1 /* Build configuration list for PBXNativeTarget "ImageNotification" */;
			buildPhases = (
				8CBE5AC5C18010AC3CD2D8A8 /* [CP] Check Pods Manifest.lock */,
				BD7BB69E291E0A9E00DAF3F1 /* Sources */,
				BD7BB69F291E0A9E00DAF3F1 /* Frameworks */,
				BD7BB6A0291E0A9E00DAF3F1 /* Resources */,
				BD7BB6B4291E0C2900DAF3F1 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ImageNotification;
			productName = ImageNotification;
			productReference = BD7BB6A2291E0A9E00DAF3F1 /* ImageNotification.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		BDD0191A2ADFFE2C009138E5 /* NotificationContentExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BDD0192A2ADFFE2C009138E5 /* Build configuration list for PBXNativeTarget "NotificationContentExtension" */;
			buildPhases = (
				4F25962405FEE9CE301423A8 /* [CP] Check Pods Manifest.lock */,
				BDD019172ADFFE2C009138E5 /* Sources */,
				BDD019182ADFFE2C009138E5 /* Frameworks */,
				BDD019192ADFFE2C009138E5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationContentExtension;
			productName = NotificationContentExtension;
			productReference = BDD0191B2ADFFE2C009138E5 /* NotificationContentExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1330;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
					BD7BB6A1291E0A9E00DAF3F1 = {
						CreatedOnToolsVersion = 14.1;
					};
					BDD0191A2ADFFE2C009138E5 = {
						CreatedOnToolsVersion = 14.3.1;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ReactNativeTSProject" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* ReactNativeTSProject */,
				BD7BB6A1291E0A9E00DAF3F1 /* ImageNotification */,
				BDD0191A2ADFFE2C009138E5 /* NotificationContentExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				36E05981286B831700D8010E /* Ionicons.ttf in Resources */,
				36E0597B286B831700D8010E /* Feather.ttf in Resources */,
				BDFBBE8C2AE3C793007FC822 /* assets in Resources */,
				36E05984286B831700D8010E /* FontAwesome.ttf in Resources */,
				369895582868D28F0052FFB3 /* GoogleService-Info.plist in Resources */,
				05B753842C7C5FA600ED6576 /* splash.gif in Resources */,
				36E0597D286B831700D8010E /* FontAwesome5_Brands.ttf in Resources */,
				36E05979286B831700D8010E /* Fontisto.ttf in Resources */,
				36E05986286B831700D8010E /* EvilIcons.ttf in Resources */,
				6132EF182BDFF13200BBE14D /* PrivacyInfo.xcprivacy in Resources */,
				05B753832C7C5FA600ED6576 /* splash.png in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				BD3EA0C9289398E800D71A05 /* icon.png in Resources */,
				36E05982286B831700D8010E /* FontAwesome5_Solid.ttf in Resources */,
				36E0597E286B831700D8010E /* MaterialCommunityIcons.ttf in Resources */,
				36E05987286B831700D8010E /* SimpleLineIcons.ttf in Resources */,
				36E05988286B831700D8010E /* MaterialIcons.ttf in Resources */,
				BDC10CE32A12325E00BCA800 /* appConfig.json in Resources */,
				36E0597C286B831700D8010E /* Entypo.ttf in Resources */,
				36E0597A286B831700D8010E /* Octicons.ttf in Resources */,
				BD9A0F7D2AE22A1800C663D1 /* main.jsbundle in Resources */,
				36E05980286B831700D8010E /* Foundation.ttf in Resources */,
				36E05983286B831700D8010E /* FontAwesome5_Regular.ttf in Resources */,
				36E0597F286B831700D8010E /* AntDesign.ttf in Resources */,
				36E05985286B831700D8010E /* Zocial.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD7BB6A0291E0A9E00DAF3F1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BDC10CE42A12325E00BCA800 /* appConfig.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BDD019192ADFFE2C009138E5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BDD019252ADFFE2C009138E5 /* MainInterface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		46FA0314395FC5D921FE6F8C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ReactNativeTSProject-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4F25962405FEE9CE301423A8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationContentExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8CBE5AC5C18010AC3CD2D8A8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ImageNotification-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		AF228128E0A21D94F09F6831 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		D7948117D3567B56D0CBCC3E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				36C404B9281CDA1000E43516 /* RNGetValues.m in Sources */,
				05A7F6E52C3FC585007E880C /* FireworkBridge.swift in Sources */,
				36D8310E28F9731900197481 /* ApplePayService.swift in Sources */,
				369A35E62B462B8500468E10 /* RNApptile.m in Sources */,
				36D8310F28F9731900197481 /* Mappers.swift in Sources */,
				36D8311128F9731900197481 /* PaymentModels.swift in Sources */,
				3692D98F280DAD2100A116EE /* RNDynamicBundle.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				36D8310D28F9731900197481 /* ApptileApplePay.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BD7BB69E291E0A9E00DAF3F1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BD7BB6A6291E0A9E00DAF3F1 /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BDD019172ADFFE2C009138E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BDD019222ADFFE2C009138E5 /* NotificationViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		BD7BB6A9291E0A9E00DAF3F1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BD7BB6A1291E0A9E00DAF3F1 /* ImageNotification */;
			targetProxy = BD7BB6A8291E0A9E00DAF3F1 /* PBXContainerItemProxy */;
		};
		BDD019282ADFFE2C009138E5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = BDD0191A2ADFFE2C009138E5 /* NotificationContentExtension */;
			targetProxy = BDD019272ADFFE2C009138E5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		BDD019232ADFFE2C009138E5 /* MainInterface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BDD019242ADFFE2C009138E5 /* Base */,
			);
			name = MainInterface.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 950802BB9B6241E074FBAEF2 /* Pods-ReactNativeTSProject.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ReactNativeTSProject/ReactNativeTSProject.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8SQ493N52G;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = ReactNativeTSProject/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.0.1;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo;
				PRODUCT_NAME = ReactNativeTSProject;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AD8CB37B96A35A0DED9F02D1 /* Pods-ReactNativeTSProject.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 8SQ493N52G;
				INFOPLIST_FILE = ReactNativeTSProject/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.0.1;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo;
				PRODUCT_NAME = ReactNativeTSProject;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "ReactNativeTSProject-Bridging-Header.h";
				USE_HERMES = false;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "ReactNativeTSProject-Bridging-Header.h";
				USE_HERMES = false;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		BD7BB6AC291E0A9E00DAF3F1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E0673C639EA34C55A9775F85 /* Pods-ImageNotification.debug.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = ImageNotification/ImageNotification.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 8SQ493N52G;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ImageNotification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ImageNotification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo.ImageNotification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BD7BB6AD291E0A9E00DAF3F1 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EE6062495A7E5A7E2E602FF6 /* Pods-ImageNotification.release.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = ImageNotification/ImageNotification.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8SQ493N52G;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ImageNotification/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ImageNotification;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo.ImageNotification;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		BDD0192B2ADFFE2C009138E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8FB9B77C537E9D225AC3766D /* Pods-NotificationContentExtension.debug.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationContentExtension/NotificationContentExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 8SQ493N52G;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationContentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationContentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo.NotificationContentExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		BDD0192C2ADFFE2C009138E5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0A920E058A106B058048D26C /* Pods-NotificationContentExtension.release.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = NotificationContentExtension/NotificationContentExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8SQ493N52G;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationContentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationContentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.apptile.apptilepreviewdemo.NotificationContentExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ReactNativeTSProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ReactNativeTSProject" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BD7BB6AE291E0A9E00DAF3F1 /* Build configuration list for PBXNativeTarget "ImageNotification" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BD7BB6AC291E0A9E00DAF3F1 /* Debug */,
				BD7BB6AD291E0A9E00DAF3F1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BDD0192A2ADFFE2C009138E5 /* Build configuration list for PBXNativeTarget "NotificationContentExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BDD0192B2ADFFE2C009138E5 /* Debug */,
				BDD0192C2ADFFE2C009138E5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
