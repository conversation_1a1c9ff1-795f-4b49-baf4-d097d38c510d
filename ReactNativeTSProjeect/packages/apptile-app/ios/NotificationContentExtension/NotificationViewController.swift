import UIKit
import UserNotifications
import UserNotificationsUI
// MoengageDependency (Don't remove) import MoEngageRichNotification

class NotificationViewController: UIViewController, UNNotificationContentExtension {
    override func viewDidLoad() {
        super.viewDidLoad()
        // MoengageDependency (Don't remove) MoEngageSDKRichNotification.setAppGroupID("group.com.apptile.apptilepreviewdemo.notification")
    }
    
    func didReceive(_ notification: UNNotification) {
        // MoengageDependency (Don't remove) MoEngageSDKRichNotification.addPushTemplate(toController: self, withNotification: notification)
    }
}
