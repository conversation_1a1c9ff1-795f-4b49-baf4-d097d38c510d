//
//  ApplePayService.swift
//  ApplePayService
//
//  Created by <PERSON><PERSON> on 25/09/22.
//
#if canImport(PassKit)

import PassKit

enum ApplePayEvents: String, CaseIterable {
  case onShippingContactUpdated = "onShippingContactUpdated"
  case onShippingMethodUpdated = "onShippingMethodUpdated"
  case onAuthorizePayment = "onAuthorizePayment"
}

@objc(ApplePayService)
open class ApplePayService: RCTEventEmitter {
  
  public static var emitter: RCTEventEmitter!
  
  override init() {
    super.init()
    ApplePayService.emitter = self
  }
  
  open override func supportedEvents() -> [String] {
    [ApplePayEvents.onShippingContactUpdated.rawValue, ApplePayEvents.onShippingMethodUpdated.rawValue, ApplePayEvents.onAuthorizePayment.rawValue]
  }
  
  
  private var rootViewController: UIViewController = UIApplication.shared.keyWindow!.rootViewController!
  private var request: PKPaymentRequest = PKPaymentRequest()
  private var resolve: RCTPromiseResolveBlock?
  private var apptileSession: ApptileSession?
  
  private var paymentAuthrizationCompletion: ((PKPaymentAuthorizationStatus) -> Void)? = nil
  private var shippingUpdateCompletion: ((PKPaymentRequestShippingContactUpdate) -> Void)? = nil
  private var shippingMethodUpdateCompletion: ((PKPaymentRequestShippingMethodUpdate) -> Void)? = nil
  
  
  public var paymentNetworks: [PKPaymentNetwork] = [.visa, .masterCard, .amex]
  
  
  //TODO: Create an typesafe interface to createSession
  //TODO: Error Handling
  //  @objc(createSession::)
  @objc private func createSession(_ merchantIdentifier: String, payCheckout: NSDictionary) -> Void {
    
    guard PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: self.paymentNetworks) else {
      print("Can not make payment")
      return
    }
    self.apptileSession = Mappers.mapToApptileSession(rawCheckout: payCheckout)
    self.request = self.apptileSession?.paymentRequest() ?? self.request
    self.request.merchantIdentifier = merchantIdentifier
    self.request.merchantCapabilities = PKMerchantCapability.capability3DS
    self.request.supportedNetworks = self.paymentNetworks
  }
  
  //TODO: Error Handelling
  @objc(initApplePay:withRejecter:)
  func initApplePay(resolve: @escaping RCTPromiseResolveBlock,reject:RCTPromiseRejectBlock) -> Void {
    guard PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: paymentNetworks) else {
      print("Can not make payment")
      return
    }
    self.resolve = resolve
    if let controller = PKPaymentAuthorizationViewController(paymentRequest: self.request) {
      controller.delegate = self
      DispatchQueue.main.async {
        self.rootViewController.present(controller, animated: true, completion: nil)
      }
      resolve(true)
    }
  }
  
  @objc(abort:withRejecter:)
  func abort(resolve: @escaping RCTPromiseResolveBlock,reject:RCTPromiseRejectBlock) -> Void {
    DispatchQueue.main.async {
      self.rootViewController.dismiss(animated: true, completion: nil)
    }
  }
  
  
  @objc(complete:withRejecter:)
  func complete(resolve: @escaping RCTPromiseResolveBlock,reject:RCTPromiseRejectBlock) -> Void {
    let paymentStatus=true
    if((self.paymentAuthrizationCompletion) != nil){
      print("paymentAuthrizationCompletion")
      let status: PKPaymentAuthorizationStatus = paymentStatus ? .success: .failure
      self.paymentAuthrizationCompletion!(status)
    }
  }
  
  
  
  @objc(updateContactCallback:)
  func updateContactCallback(_ params:NSDictionary) -> Void {
    
    print("updateContactCallback")
    
    if ((self.shippingUpdateCompletion == nil)) {
      print("No shippingUpdateCompletion found")
      return
    }
    
    if(self.shippingUpdateCompletion != nil){
      
      print("starting updateContactCallback")
      let dictLineItems = (params["lineItems"] ?? [] ) as! [NSDictionary]
      let lineItems = dictLineItems.map{
        Mappers.mapToApptileLineItem(rawLineItem: $0)
      } as [ApptileLineItem]
      
      let dictShippingMethods = (params["shippingMethods"] ?? []) as! [NSDictionary]
      let shippingMethods = dictShippingMethods.map{
        Mappers.mapToShippingMethod(apptileShippingMethod: $0)
      }
      dump(lineItems.summaryItems)
      dump(shippingMethods)
     
      let result  = PKPaymentRequestShippingContactUpdate.init(errors: nil, paymentSummaryItems: lineItems.summaryItems, shippingMethods: shippingMethods)
      result.status = PKPaymentAuthorizationStatus.success
      
      var errors = [Error]()
      if (shippingMethods.isEmpty && ((self.apptileSession?.requiresShipping) != nil)) {
          let pickupError = PKPaymentRequest.paymentShippingAddressUnserviceableError(withLocalizedDescription: "Shipping Address is not serviceable at the moment!")
           errors.append(pickupError)
           result.status = .failure
           result.errors = errors
     }
      
      self.shippingUpdateCompletion!(result)
      self.shippingUpdateCompletion = nil
      print("done updateContactCallback")
    }
  }
  
  @objc(updateShippingMethodCallback:)
  func updateShippingMethodCallback(_ params:NSDictionary) -> Void {
    
    print("updateShippingMethodCallback")
    
    if ((self.shippingMethodUpdateCompletion == nil)) {
      print("No complete found")
      return
    }
    
    if((self.shippingMethodUpdateCompletion) != nil){
      print("shippingMethodUpdateCompletion")
      let dictLineItems = (params["lineItems"] ?? [] ) as! [NSDictionary]
      let lineItems = dictLineItems.map{
        Mappers.mapToApptileLineItem(rawLineItem: $0)
      } as [ApptileLineItem]
      
      let result = PKPaymentRequestShippingMethodUpdate(paymentSummaryItems: lineItems.summaryItems)
      result.status = .success
      print("Payment PKShippingMethod")
      self.shippingMethodUpdateCompletion!(result)
      self.shippingMethodUpdateCompletion = nil
    }
  }
  
  
  @objc(canMakePayments:withRejecter:)
  func canMakePayments(resolve: RCTPromiseResolveBlock,reject:RCTPromiseRejectBlock) -> Void {
    if PKPaymentAuthorizationViewController.canMakePayments(usingNetworks: paymentNetworks) {
      resolve(true)
    } else {
      resolve(false)
    }
  }
  
  @objc
  public override static func requiresMainQueueSetup() -> Bool {
    return true
  }
  
}

//TODO: Export events for react native to interact to.
extension ApplePayService: PKPaymentAuthorizationViewControllerDelegate {
  public func paymentAuthorizationViewControllerDidFinish(_ controller: PKPaymentAuthorizationViewController) {
    print("Apple Pay view Controller dismissed!")
    
    controller.dismiss(animated: true, completion: nil)
  }
  
  
  public func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didAuthorizePayment payment: PKPayment, completion: @escaping (PKPaymentAuthorizationStatus) -> Void) {
    
    //  TODO: Verify Shipping, Billing and Shipping Rate
    //  TODO: Send generated token to PG to complete payment
    print("Payment Authorized")
    self.paymentAuthrizationCompletion = completion;
    let paymentDict = Mappers.mapFromPaymentAuthorization(payment: payment)
    ApplePayService.emitter.sendEvent(withName: "onAuthorizePayment", body: paymentDict)
    
    //    let token = String(decoding: payment.token.paymentData, as: UTF8.self)
    //    print("Payment token generated")
    //    if token != nil {
    //      completion(.success)
    //    } else {
    //      completion(.failure)
    //    }
  }
  
  
  /**
   TODO1.  Export callback with Params
   TODO2.  Export the above method closure as completion so that on result from react-native it should trigger completion method
   */
  public func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didSelectShippingContact contact: PKContact, handler completion: @escaping (PKPaymentRequestShippingContactUpdate) -> Void) {
    let shippingContact = Mappers.mapFromShippingContact(shippingContact:contact)
    ApplePayService.emitter.sendEvent(withName: "onShippingContactUpdated", body: shippingContact)
    self.shippingUpdateCompletion = completion
  }
  
  
  public func paymentAuthorizationViewController(_ controller: PKPaymentAuthorizationViewController, didSelect shippingMethod: PKShippingMethod, handler completion: @escaping (PKPaymentRequestShippingMethodUpdate) -> Void) {
    
    //  TODO: Based on ShippingMethod selected call checkout API and get latest PaymentSummaryItem
    
    let shippingMethod = Mappers.mapFromShippingMethod(shippingMethod: shippingMethod)
    ApplePayService.emitter.sendEvent(withName: "onShippingMethodUpdated", body: shippingMethod)
    self.shippingMethodUpdateCompletion = completion
  }
  
}

#endif
