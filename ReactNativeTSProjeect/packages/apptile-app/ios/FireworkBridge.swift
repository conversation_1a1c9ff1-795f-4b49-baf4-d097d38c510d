//
//  FireworkBridge.swift
//  ReactNativeTSProject
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 11/07/24.
//

/* FireworkDependency (Don't remove)
import FireworkVideo
import FireworkVideoIVSSupport
import react_native_firework_sdk

@objc(FireworkBridge)
public class FireworkBridge: NSObject {
  @objc public static func initFireworkSDK() {
      print("Call init for firework sdk")
      FWReactNativeSDK.initializeSDK(nil)
      FireworkVideoSDK.enableIVSPlayback()
      print("Firework sdk initialization call finished")
  }
}
FireworkDependencyEnd */