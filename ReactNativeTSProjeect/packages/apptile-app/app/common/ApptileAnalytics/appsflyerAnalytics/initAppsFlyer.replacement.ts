import appsFlyer from 'react-native-appsflyer';

const initAppsFlyer = appsFlyer.initSdk(
  {
    isDebug: __DEV__,
    devKey: '<appsflyer_devKey>',
    appId: '<appsflyer_appId>',
    onInstallConversionDataListener: false,
    onDeepLinkListener: false,
    timeToWaitForATTUserAuthorization: 10,
  },
  result => {
    logger.info('appsFlyer.initSdk', result);
  },
  error => {
    logger.error('appsFlyer.initSdk', error);
  },
);

export default initAppsFlyer;
