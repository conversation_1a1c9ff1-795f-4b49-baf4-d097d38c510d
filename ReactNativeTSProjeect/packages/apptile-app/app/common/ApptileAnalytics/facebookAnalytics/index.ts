import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';

export class Facebook {
  static async prepareSdk() {}

  static async sendEvent(eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) {
    try {
      if (!eventName) return;
      eventProperty = eventProperty?.toJS ? eventProperty.toJS() : eventProperty ?? {};
      logger.info('sendToFacebook', eventName, eventProperty);
    } catch (err) {
      logger.error(err);
    }
  }
}
