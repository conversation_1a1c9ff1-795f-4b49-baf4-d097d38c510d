import _ from 'lodash';
import firebaseAnalyticsSdk from '@react-native-firebase/analytics';

import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';
import {transformers, defaultEventProcessor} from './transformers';

const gaEventsMap: Record<string, string> = {
  addToCart: 'add_to_cart',
  initiateCheckout: 'begin_checkout',
  purchase: 'purchase',
  pageView: 'screen_view',
  search: 'search',
  addToWishlist: 'add_to_wishlist',
  removeFromCart: 'remove_from_cart',
  viewCart: 'view_cart',
  productView: 'view_item',
  customerLogIn: 'login',
  customerRegistered: 'sign_up',
  selectProduct: 'select_item',
  collectionView: 'view_item_list',
};

export class Firebase {
  static analytics = firebaseAnalyticsSdk();

  static async getAppInstanceId() {
    return Firebase.analytics.getAppInstanceId();
  }

  static async setUser(userId: string) {
    await Firebase.analytics.setUserId(userId);
  }

  static async sendEvent(eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) {
    try {
      if (!eventName) return;
      eventProperty = eventProperty?.toJS ? eventProperty.toJS() : eventProperty ?? {};

      const payload = Firebase.transformData(eventName, eventProperty);
      const gaEventName = gaEventsMap[eventName] ?? eventName;

      logger.info('sendToFirebase', eventName, gaEventName, payload);
      await Firebase.analytics.logEvent(gaEventName, payload);
    } catch (err) {
      logger.error(err);
    }
  }

  static transformData(eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) {
    const transformer = _.get(transformers, eventName, defaultEventProcessor);
    return transformer(eventProperty);
  }
}
