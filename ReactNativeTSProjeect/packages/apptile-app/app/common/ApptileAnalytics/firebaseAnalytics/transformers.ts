import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';
import _ from 'lodash';

const transformAddToCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'price'),
    items: [
      {
        item_id: _.get(eventProperty, 'productId'),
        item_name: _.get(eventProperty, 'title'),
        item_variant: _.get(eventProperty, 'variantTitle'),
        price: _.get(eventProperty, 'price'),
        quantity: _.get(eventProperty, 'quantity'),
        item_brand: _.get(eventProperty, 'brand'),
        item_category: _.get(eventProperty, 'productType'),
        item_list_name: _.get(eventProperty, 'listName'),
        index: _.get(eventProperty, 'index'),
      },
    ],
  };

  return payload;
};

const transformInitiateCheckoutEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  let items = _.get(eventProperty, 'items', []);
  items = Array.isArray(items) ? items : [];

  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'totalValue'),
    items: _.map(items, itemEntry => {
      return {
        item_id: _.get(itemEntry, 'productId'),
        item_name: _.get(itemEntry, 'title'),
        item_variant: _.get(itemEntry, 'variantTitle'),
        price: _.get(itemEntry, 'price'),
        quantity: _.get(itemEntry, 'quantity'),
        item_brand: _.get(itemEntry, 'brand'),
        item_category: _.get(itemEntry, 'productType'),
        item_list_name: _.get(itemEntry, 'listName'),
        index: _.get(itemEntry, 'index'),
      };
    }),
  };
  return payload;
};

const transformPurchaseEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  let items = _.get(eventProperty, 'items', []);
  items = Array.isArray(items) ? items : [];

  const payload = {
    transaction_id: _.get(eventProperty, 'orderId'),
    value: _.get(eventProperty, 'totalValue'),
    tax: _.get(eventProperty, 'taxPrice'),
    shipping: Number(_.get(eventProperty, 'shippingAmount', 0)) - Number(_.get(eventProperty, 'shippingDiscount', 0)),
    currency: _.get(eventProperty, 'currency'),
    coupon: _.get(eventProperty, 'coupon'),
    items: _.map(items, itemEntry => {
      return {
        item_id: _.get(itemEntry, 'productId'),
        item_name: _.get(itemEntry, 'title'),
        item_variant: _.get(itemEntry, 'variantTitle'),
        price: _.get(itemEntry, 'price'),
        quantity: _.get(itemEntry, 'quantity'),
        item_name: _.get(itemEntry, 'title'),
        item_brand: _.get(itemEntry, 'brand'),
        item_category: _.get(itemEntry, 'productType'),
        item_list_name: _.get(itemEntry, 'listName'),
        index: _.get(itemEntry, 'index'),
      };
    }),
  };
  return payload;
};

const transformPageViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    screen_name: _.get(eventProperty, 'pageName'),
    screen_class: _.get(eventProperty, 'pageName'),
  };
  return payload;
};

const transformSearchEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    search_term: _.get(eventProperty, 'term'),
  };
  return payload;
};

const transformAddToWishlistEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'price'),
    items: [
      {
        item_id: _.get(eventProperty, 'productId'),
        item_name: _.get(eventProperty, 'title'),
        item_variant: _.get(eventProperty, 'variantTitle'),
        price: _.get(eventProperty, 'price'),
        quantity: _.get(eventProperty, 'quantity'),
        item_brand: _.get(eventProperty, 'brand'),
        item_category: _.get(eventProperty, 'productType'),
        item_list_name: _.get(eventProperty, 'listName'),
        index: _.get(eventProperty, 'index'),
      },
    ],
  };
  return payload;
};

const transformRemoveFromCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'price'),
    items: [
      {
        item_id: _.get(eventProperty, 'productId'),
        item_name: _.get(eventProperty, 'title'),
        item_variant: _.get(eventProperty, 'variantTitle'),
        price: _.get(eventProperty, 'price'),
        quantity: _.get(eventProperty, 'quantity'),
        item_brand: _.get(eventProperty, 'brand'),
        item_category: _.get(eventProperty, 'productType'),
        item_list_name: _.get(eventProperty, 'listName'),
        index: _.get(eventProperty, 'index'),
      },
    ],
  };
  return payload;
};

const transformViewCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  let items = _.get(eventProperty, 'items', []);
  items = Array.isArray(items) ? items : [];

  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'totalValue'),
    items: _.map(items, itemEntry => {
      return {
        item_id: _.get(itemEntry, 'productId'),
        item_name: _.get(itemEntry, 'title'),
        item_variant: _.get(itemEntry, 'variantTitle'),
        price: _.get(itemEntry, 'price'),
        quantity: _.get(itemEntry, 'quantity'),
        item_brand: _.get(itemEntry, 'brand'),
        item_category: _.get(itemEntry, 'productType'),
        item_list_name: _.get(itemEntry, 'listName'),
        index: _.get(itemEntry, 'index'),
      };
    }),
  };
  return payload;
};

const transformProductViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'price'),
    items: [
      {
        item_id: _.get(eventProperty, 'productId'),
        item_name: _.get(eventProperty, 'title'),
        price: _.get(eventProperty, 'price'),
        item_category: _.get(eventProperty, 'productType'),
        item_brand: _.get(eventProperty, 'brand'),
        item_list_name: _.get(eventProperty, 'listName'),
        index: _.get(eventProperty, 'index'),
      },
    ],
  };
  return payload;
};

const transformSelectProductEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    value: _.get(eventProperty, 'price'),
    item_list_id: _.get(eventProperty, 'listId'),
    item_list_name: _.get(eventProperty, 'listName'),
    items: [
      {
        item_id: _.get(eventProperty, 'productId'),
        item_name: _.get(eventProperty, 'title'),
        price: _.get(eventProperty, 'price'),
        item_category: _.get(eventProperty, 'productType'),
        item_brand: _.get(eventProperty, 'brand'),
        item_list_name: _.get(eventProperty, 'listName'),
        index: _.get(eventProperty, 'index'),
        item_variant: _.get(eventProperty, 'variantTitle'),
      },
    ],
  };
  return payload;
};

const transformCollectionViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    currency: _.get(eventProperty, 'currency'),
    item_list_id: _.get(eventProperty, 'listId'),
    item_list_name: _.get(eventProperty, 'listName'),
    items: _.map(_.get(eventProperty, 'items', []), itemEntry => {
      return {
        item_id: _.get(itemEntry, 'productId'),
        item_name: _.get(itemEntry, 'title'),
        item_variant: _.get(itemEntry, 'variantTitle'),
        price: _.get(itemEntry, 'price'),
        item_brand: _.get(itemEntry, 'brand'),
        item_category: _.get(itemEntry, 'productType'),
        item_list_name: _.get(itemEntry, 'listName'),
        index: _.get(itemEntry, 'index'),
      };
    }),
  };
  return payload;
};

const transformCustomerLoginEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    method: _.get(eventProperty, 'method') ?? 'email',
  };
  return payload;
};

const transformCustomerRegisteredEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    method: _.get(eventProperty, 'method') ?? 'email',
  };
  return payload;
};

export const defaultEventProcessor = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  return eventProperty;
};

export const transformers = {
  addToCart: transformAddToCartEvent,
  initiateCheckout: transformInitiateCheckoutEvent,
  purchase: transformPurchaseEvent,
  pageView: transformPageViewEvent,
  search: transformSearchEvent,
  addToWishlist: transformAddToWishlistEvent,
  removeFromCart: transformRemoveFromCartEvent,
  viewCart: transformViewCartEvent,
  productView: transformProductViewEvent,
  customerLogIn: transformCustomerLoginEvent,
  customerRegistered: transformCustomerRegisteredEvent,
  login: transformCustomerLoginEvent,
  signup: transformCustomerRegisteredEvent,
  selectProduct: transformSelectProductEvent,
  collectionView: transformCollectionViewEvent,
};
