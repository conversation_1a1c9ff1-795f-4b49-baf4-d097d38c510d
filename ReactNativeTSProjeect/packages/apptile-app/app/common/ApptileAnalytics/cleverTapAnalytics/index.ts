import _ from 'lodash';

const prevEvent: [any, any] = [null, null];
export const sendToCleverTap = (eventName: string, eventData: Record<string, any>) => {
  if (!eventName) return;
  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;
  if (_.isEqual(prevEvent, [eventName, _eventData])) return;
  prevEvent[0] = eventName;
  prevEvent[1] = _eventData;

  logger.info('sendToCleverTap', eventName, _eventData);
};
