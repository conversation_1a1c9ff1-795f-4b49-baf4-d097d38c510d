import {createClient, SegmentClient} from '@segment/analytics-react-native';
import _ from 'lodash';
import {transformers} from './transformers';
import {APPTILE_ANALYTICS_SEGMENT_KEY} from '../../../../.env.json';

export class Segment {
  static _segmentClient: SegmentClient;

  static get initialized() {
    return !!Segment._segmentClient;
  }

  static async initSegment() {
    const segmentWriteKey = APPTILE_ANALYTICS_SEGMENT_KEY;
    if (segmentWriteKey) {
      this._segmentClient = createClient({
        writeKey: segmentWriteKey,
      });
    }
  }

  static async setUser(userId: string, retry?: number) {
    if (this._segmentClient) {
      await this._segmentClient?.identify(userId);
    } else {
      await new Promise(res => setTimeout(res, 200));
      if (retry && retry < 20) this.setUser(userId, retry ? retry + 1 : 1);
    }
  }

  static async sendEvent(type: string, eventName: string, eventProperty: any) {
    try {
      if (this._segmentClient) {
        if (!eventName) return;
        logger.info('sendingToSegment', eventName, eventProperty);
        if (type == 'page') this._segmentClient?.screen(eventName, eventProperty);
        else
          this._segmentClient?.track(
            eventName,
            transformers[eventName] ? transformers[eventName](eventProperty) : eventProperty,
          );
      } else {
        const segmentWriteKey = APPTILE_ANALYTICS_SEGMENT_KEY;
        if (segmentWriteKey) {
          logger.info('Segment is not setup for this build');
        } else {
          logger.info('Segment is not yet initialised');
        }
      }
    } catch (err) {
      console.warn(err);
    }
  }
}
