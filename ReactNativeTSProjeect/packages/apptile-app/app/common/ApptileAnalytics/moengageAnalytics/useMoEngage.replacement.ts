import {useEffect} from 'react';
import {Platform} from 'react-native';
import ReactMoE, {MoEInitConfig, MoEPushConfig, MoEngageLogConfig, MoEngageLogLevel} from 'react-native-moengage';
import {triggerCustomEventListener} from 'apptile-core';

const handleDeeplink = (deeplink: string) => {
  if (!deeplink) return;
  try {
    triggerCustomEventListener('deeplink_request', deeplink);
  } catch (e) {
    toast.show('Failed to navigate to requested screen', {type: 'error', placement: 'top', duration: 1340});
  }
};

const useMoEngage = () => {
  useEffect(() => {
    ReactMoE.setEventListener('pushTokenGenerated', payload => {
      logger.info('pushTokenGenerated', payload);
    });
    ReactMoE.setEventListener('pushClicked', notificationPayload => {
      logger.info('pushClicked', notificationPayload);
      handleDeeplink(
        notificationPayload?.data?.payload?.moe_webUrl || notificationPayload?.data?.payload?.app_extra?.moe_deeplink,
      );
    });
    ReactMoE.setEventListener('inAppCampaignShown', inAppInfo => {
      logger.info('inAppCampaignShown', inAppInfo);
    });
    ReactMoE.setEventListener('inAppCampaignClicked', inAppInfo => {
      logger.info('inAppCampaignClicked', inAppInfo);
      if (inAppInfo?.action.actionType === 'navigation' && inAppInfo?.action?.navigationType === 'deep_linking') {
        handleDeeplink(inAppInfo?.action?.navigationUrl);
      }
    });
    ReactMoE.setEventListener('inAppCampaignDismissed', selfHandledInAppInfo => {
      logger.info('inAppCampaignDismissed', selfHandledInAppInfo);
    });
    ReactMoE.setEventListener('inAppCampaignCustomAction', selfHandledInAppInfo => {
      logger.info('inAppCampaignCustomAction', selfHandledInAppInfo);
    });

    const moEInitConfig = new MoEInitConfig(
      MoEPushConfig.defaultConfig(),
      new MoEngageLogConfig(MoEngageLogLevel.VERBOSE, __DEV__),
    );
    ReactMoE.initialize('<MoEngageAppId>', moEInitConfig);
    ReactMoE.showInApp();

    return () => {
      ReactMoE.removeEventListener('pushTokenGenerated');
      ReactMoE.removeEventListener('pushClicked');
      ReactMoE.removeEventListener('inAppCampaignShown');
      ReactMoE.removeEventListener('inAppCampaignClicked');
      ReactMoE.removeEventListener('inAppCampaignDismissed');
      ReactMoE.removeEventListener('inAppCampaignCustomAction');
    };
  }, []);
};

export const registerForMoengageNotification = (pushToken: string) => {
  Platform.select({
    ios: ReactMoE.registerForPush,
    android: () => {
      ReactMoE.requestPushPermissionAndroid();
      ReactMoE.passFcmPushToken(pushToken);
    },
    default: () => {},
  })();
};

export default useMoEngage;
