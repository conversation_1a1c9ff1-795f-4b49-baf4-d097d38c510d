import {LogLevel, OneSignal} from 'react-native-onesignal';

import {triggerCustomEventListener} from 'apptile-core';
import {Platform} from 'react-native';

const handleDeeplink = (deeplink?: string) => {
  if (!deeplink) return;
  try {
    triggerCustomEventListener('deeplink_request', deeplink);
  } catch (e) {
    toast.show('Failed to navigate to requested screen', {type: 'error', placement: 'top', duration: 1340});
  }
};

OneSignal.Debug.setLogLevel(LogLevel.Verbose);
OneSignal.initialize('<OneSignalAppId>');

OneSignal.Notifications.addEventListener('click', event => {
  logger.info('OneSignal: notification clicked:', event);
  new Promise(resolve => {
    setTimeout(() => {
      resolve(null);
    }, 1000);
  }).then(() => {
    if (Platform.OS === 'ios') handleDeeplink(event.notification.launchURL);
    else handleDeeplink(event.notification.launchUrl);
  });
});

const initOneSignal = async () => {
  await OneSignal.Notifications.canRequestPermission().then(can => {
    if (can) OneSignal.Notifications.requestPermission(false)
  })
};

export default initOneSignal;
