import ApptileAnalytics from './ApptileAnalytics';

export interface CampaignParams {
  source: string | null;
  medium: string | null;
  campaign: string | null;
  dynamicLink: boolean;
}

export const ApptileCoreEvents = {
  FIRST_OPEN: 'apptile_first_open',
  APP_OPEN: 'apptile_app_open',
};

class DefaultEventManager {
  async sendFirstOpenEvent(campaignParams: CampaignParams) {
    ApptileAnalytics.sendEvent('track', ApptileCoreEvents.FIRST_OPEN, campaignParams);
  }

  async sendAppOpenEvent(campaignParams: CampaignParams) {
    ApptileAnalytics.sendEvent('track', ApptileCoreEvents.APP_OPEN, campaignParams);
  }
}

const defaultEventManager = new DefaultEventManager();
export default defaultEventManager;