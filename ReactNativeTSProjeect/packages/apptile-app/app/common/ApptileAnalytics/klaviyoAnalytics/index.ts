import _ from 'lodash';

import {transformers, defaultEventTransformer} from './transformers';
import {IApptileAnalyticsEvent} from 'apptile-core';
import {AjaxQueryRunner} from 'apptile-core';

// Klaviyo Public Key / Company ID
export const COMPANY_ID: string = '<KLAVIYO_COMPANY_ID>';

const prevEvent: [any, any] = [null, null];

export const sendToKlaviyo = (eventName: string, eventData: Record<string, any>) => {
  if (COMPANY_ID === '<KLAVIYO_COMPANY_ID>') return;

  if (!eventName) return;

  const _eventData = eventData?.toJS ? eventData.toJS() : eventData;

  logger.info('sendToKlaviyo', eventName, _eventData);

  try {
    switch (eventName) {
      case 'back-in-stock-subscription':
        addToBackInStockSubscription(_eventData);
        break;
      default:
        if (_.isEqual(prevEvent, [eventName, _eventData])) return;
        prevEvent[0] = eventName;
        prevEvent[1] = _eventData;
        transformData(eventName, _eventData);
        break;
    }
  } catch (err) {
    logger.error(err);
  }
};

const getIdFromShopifyGid = (gid: string) => String(gid.split('/').pop());

const addToBackInStockSubscription = async (eventProperty: IApptileAnalyticsEvent['properties']) => {
  if (!eventProperty.emailId || !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(eventProperty.emailId)) {
    return;
  }

  const url = `https://a.klaviyo.com/client/back-in-stock-subscriptions/?company_id=${COMPANY_ID}`;
  const headers = {
    accept: 'application/json',
    revision: '2024-07-15',
    'content-type': 'application/json',
  };
  const body = {
    data: {
      type: 'back-in-stock-subscription',
      attributes: {
        profile: {
          data: {
            type: 'profile',
            attributes: {email: eventProperty.emailId},
          },
        },
        channels: ['EMAIL'],
      },
      relationships: {
        variant: {
          data: {
            type: 'catalog-variant',
            id: `$shopify:::$default:::${getIdFromShopifyGid(eventProperty.variantId)}`,
          },
        },
      },
    },
  };

  try {
    const queryRunner = AjaxQueryRunner();

    const response = await queryRunner.runQuery('post', url, body, {
      headers,
    });

    logger.info('addToBackInStockSubscription', response.data);
    toast.show(eventProperty.successMessage || 'You will be notified when this item is back in stock.', {
      type: 'success',
    });
  } catch (error) {
    logger.error(error);
    toast.show(eventProperty.errorMessage || 'Please try again later.', {type: 'error'});
  }
};

const transformData = (eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) => {
  const transformer = _.get(transformers, eventName);
  if (transformer) return transformer(eventProperty);

  return defaultEventTransformer(eventName, eventProperty);
};

// Note: for reference: https://developers.klaviyo.com/en/reference/create_client_event
