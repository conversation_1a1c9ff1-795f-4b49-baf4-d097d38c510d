import _ from 'lodash';
import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';
import Klaviyo from 'react-native-klaviyo';

const getItems = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const tempItems = _.get(eventProperty, 'items', []);
  return Array.isArray(tempItems) ? tempItems : [];
};

const transformAddToCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const price = _.get(eventProperty, 'price');

  Klaviyo.sendEvent('Add To Cart', {
    value: price ? _.toNumber(_.get(eventProperty, 'price')) : undefined,
    properties: _.clone(eventProperty),
  });
};

const transformInitiateCheckoutEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const totalValue = _.get(eventProperty, 'totalValue');

  Klaviyo.sendEvent('Initiate Checkout', {
    value: totalValue ? _.toNumber(totalValue) : undefined,
    properties: {
      ...eventProperty,
      items: getItems(eventProperty),
    },
  });
};

const transformPurchaseEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const totalValue = _.get(eventProperty, 'totalValue');

  Klaviyo.sendEvent('Purchase', {
    value: totalValue ? _.toNumber(totalValue) : undefined,
    properties: {
      ...eventProperty,
      items: getItems(eventProperty),
    },
  });
};

const transformPageViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  Klaviyo.sendEvent('Page View', {
    properties: _.clone(eventProperty),
  });
};

const transformSearchEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  Klaviyo.sendEvent('Search', {
    properties: {
      term: _.get(eventProperty, 'term'),
      totalItemsSearch: _.get(eventProperty, 'totalItemsSearch'),
    },
  });
};

const transformAddToWishlistEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const price = _.get(eventProperty, 'price');

  Klaviyo.sendEvent('Add To Wishlist', {
    value: price ? _.toNumber(price) : undefined,
    properties: _.clone(eventProperty),
  });
};

const transformRemoveFromCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const price = _.get(eventProperty, 'price');

  Klaviyo.sendEvent('Remove From Cart', {
    value: price ? _.toNumber(price) : undefined,
    properties: _.clone(eventProperty),
  });
};

const transformViewCartEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const totalValue = _.get(eventProperty, 'totalValue');

  Klaviyo.sendEvent('View Cart', {
    value: totalValue ? _.toNumber(totalValue) : undefined,
    properties: {
      ...eventProperty,
      items: getItems(eventProperty),
    },
  });
};

const transformProductViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const price = _.get(eventProperty, 'price');

  Klaviyo.sendEvent('Product View', {
    value: price ? _.toNumber(price) : undefined,
    properties: _.clone(eventProperty),
  });
};

const transformCollectionViewEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  Klaviyo.sendEvent('Collection View', {
    properties: {
      ..._.clone(eventProperty),
      items: getItems(eventProperty),
    },
  });
};

const transformLoginEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const userDetails: Record<string, string> = {};

  const email = _.get(eventProperty, 'emailId');
  if (!_.isEmpty(email)) userDetails.email = email;

  const phone_number = _.get(eventProperty, 'contactNumber');
  if (!_.isEmpty(phone_number)) userDetails.phone_number = phone_number;

  const first_name = _.get(eventProperty, 'firstName');
  if (!_.isEmpty(first_name)) userDetails.first_name = first_name;

  const last_name = _.get(eventProperty, 'lastName');
  if (!_.isEmpty(last_name)) userDetails.last_name = last_name;

  Klaviyo.identify(userDetails);

  Klaviyo.sendEvent('Login', {
    properties: _.clone(eventProperty),
  });
};

const transformRegisterEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const userDetails: Record<string, string> = {};

  const email = _.get(eventProperty, 'emailId');
  if (!_.isEmpty(email)) userDetails.email = email;

  const phoneNumber = _.get(eventProperty, 'contactNumber');
  if (!_.isEmpty(phoneNumber)) userDetails.phone_number = phoneNumber;

  const firstName = _.get(eventProperty, 'firstName');
  if (!_.isEmpty(firstName)) userDetails.first_name = firstName;

  const lastName = _.get(eventProperty, 'lastName');
  if (!_.isEmpty(lastName)) userDetails.last_name = lastName;

  Klaviyo.identify(userDetails);

  Klaviyo.sendEvent('Registered', {
    properties: _.clone(eventProperty),
  });
};

const transformLogoutEvent = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  Klaviyo.sendEvent('Logout', {});
  setTimeout(() => {
    Klaviyo.resetIdentity();
  }, 1000);
};

export const defaultEventTransformer = (eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) => {
  Klaviyo.sendEvent(eventName, {
    properties: _.clone(eventProperty),
  });
};

export const transformers = {
  addToCart: transformAddToCartEvent,
  initiateCheckout: transformInitiateCheckoutEvent,
  purchase: transformPurchaseEvent,
  pageView: transformPageViewEvent,
  search: transformSearchEvent,
  addToWishlist: transformAddToWishlistEvent,
  removeFromCart: transformRemoveFromCartEvent,
  viewCart: transformViewCartEvent,
  productView: transformProductViewEvent,
  collectionView: transformCollectionViewEvent,
  login: transformLoginEvent,
  signup: transformRegisterEvent,
  logout: transformLogoutEvent,
};
