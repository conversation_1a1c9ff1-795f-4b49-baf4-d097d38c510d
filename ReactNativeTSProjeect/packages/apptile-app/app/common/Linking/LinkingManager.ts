import {Linking} from 'react-native';
import queryString from 'query-string';

import { LocalStorage as localStorage} from 'apptile-core';
import { getConfigValue as RNGetValues} from 'apptile-core';
import {setAppConstants, ApptileConstantsType} from 'apptile-core';
import {CampaignParams} from '../ApptileAnalytics/DefaultEvents';

export const processPreviewURL = async (url: string) => {
  const isDistributedApp = await RNGetValues('APPTILE_IS_DISTRIBUTED_APP');
  if (url && !isDistributedApp) {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      const parsedURL = queryString.parseUrl(url);

      const appConstants = {} as ApptileConstantsType;
      const APPTILE_APP_ID = parsedURL.query.appId as string;
      if (APPTILE_APP_ID) {
        appConstants.APPTILE_APP_ID = APPTILE_APP_ID;
        localStorage.setNamespace(APPTILE_APP_ID);
      }
      const APPTILE_UPDATE_ENDPOINT = parsedURL.query.appApi as string;
      if (APPTILE_UPDATE_ENDPOINT) {
        appConstants.APPTILE_API_ENDPOINT = APPTILE_UPDATE_ENDPOINT;
        appConstants.APPTILE_UPDATE_ENDPOINT = APPTILE_UPDATE_ENDPOINT;
      }
      const DEV_FORK_ID = parsedURL.query.forkId as string;
      const DEV_BRANCH_NAME = parsedURL.query.branchName as string;
      appConstants.DEV_FORK_ID = DEV_FORK_ID;
      appConstants.DEV_BRANCH_NAME = DEV_BRANCH_NAME || 'main';
      setAppConstants(appConstants);
    } else {
      logger.error(`Don't know how to open this URL: ${url}`);
    }
  }
};

export const processCampaignParameters = async (url: string | null) => {
  const campaignParams: CampaignParams = {
    source: null,
    medium: null,
    campaign: null,
    dynamicLink: false,
  };

  if (url) {
    const parsedURL = queryString.parseUrl(url);
    if (parsedURL && parsedURL.query) {
      campaignParams.source = parsedURL.query.utm_source as string;
      campaignParams.medium = parsedURL.query.utm_medium as string;
      campaignParams.campaign = parsedURL.query.utm_campaign as string;
      campaignParams.dynamicLink = true;
    }
  }
  return campaignParams;
};
