import {BuildManagerApi} from '@/root/web/api/BuildApi';

export const getLinkingPrefixesInDevAndWeb = async (appId: string) => {
  const linkingData = [];
  try {
    const {data} = (await BuildManagerApi.getAppSettings(appId)) as {data: {bundleUrlScheme: string; appHost: string}};
    if (data.bundleUrlScheme) linkingData.push(data.bundleUrlScheme);
    if (data.appHost) linkingData.push(data.appHost);

    if (!linkingData.length) logger.warn('Please add bundleUrlScheme and appHost in build settings');
  } catch (e) {
    logger.error(e);
  }
  return linkingData;
};
