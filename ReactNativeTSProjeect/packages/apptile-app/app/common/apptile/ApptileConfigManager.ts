import {Platform} from 'react-native';
import {ApptileConstantsType, LocalStorage, setAppConstants} from 'apptile-core';
import {getConfigValue} from 'apptile-core';
import {APPTILE_APP_ID, APPTILE_API_ENDPOINT, APPTILE_UPDATE_ENDPOINT, DEV_FORK_ID, DEV_BRANCH_NAME} from '../../../.env.json';

export const initApptileConfig = async () => {
  const appConstants = {
    APPTILE_APP_ID: null,
    APPTILE_API_ENDPOINT: null,
    ANALYTICS_API_ENDPOINT: null,
    APPTILE_UPDATE_ENDPOINT: null,
    APPTILE_APP_FORK: null,
    DEV_FORK_ID: null,
    DEV_BRANCH_NAME: null,
    CONSTANTS_LOADED: false,
  } as ApptileConstantsType;
  if (Platform.OS === 'web') {
    appConstants.APPTILE_API_ENDPOINT = APPTILE_API_ENDPOINT;
  } else {
    // IMPORTANT!!!
    // Never change this. If you want to change these use .env.json to change them for development.
    // these need to be what they are for apps to work when they are built !!!
    // Your ease of development is not the primary concern here.
    // Treat this piece of code like the bios of a computer. DON'T MESS WITH IT !!!!
    appConstants.APPTILE_APP_ID = APPTILE_APP_ID || (await getConfigValue('APPTILE_APP_ID'));
    appConstants.APPTILE_API_ENDPOINT = APPTILE_API_ENDPOINT || (await getConfigValue('APPTILE_API_ENDPOINT'));
    appConstants.APPTILE_UPDATE_ENDPOINT = APPTILE_UPDATE_ENDPOINT || (await getConfigValue('APPTILE_UPDATE_ENDPOINT'));
    appConstants.ANALYTICS_API_ENDPOINT = await getConfigValue('ANALYTICS_API_ENDPOINT');
    appConstants.APPTILE_APP_FORK =
      ((await LocalStorage.getValue('activeForkName')) as string) || (await getConfigValue('APPTILE_APP_FORK'));
    appConstants.DEV_FORK_ID = DEV_FORK_ID;
    appConstants.DEV_BRANCH_NAME = DEV_BRANCH_NAME;
  }
  appConstants.CONSTANTS_LOADED = true;
  setAppConstants(appConstants);
};
