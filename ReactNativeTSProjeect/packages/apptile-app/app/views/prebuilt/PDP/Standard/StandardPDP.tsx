import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import {SafeAreaView} from 'react-native-safe-area-context';
import tinycolor from 'tinycolor2';

import {
  CurrentScreenContext,
  ApptileScrollView,
  datasourceTypeModelSel,
  selectPluginStageModel,
  shopifyProductCacheSelector ,
  useTheme,
  generateTypographyByPlatform,
  useLoadedFonts,
  EmbeddedAppPageContainer,
  selectPluginConfig,
  selectPlugin, 
  triggerAction, 
  sendAnalyticsEvent,
  navigateToScreen,
  createDeepEqualSelector,
  MaterialCommunityIcons,
  performHapticFeedback,
  selectAppSettingsForKey,
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY,
  SettingsConfig
} from 'apptile-core';

import {registerNativePage} from '../../prebuilt';

import {
  getActiveVariantImages,
  getProductDefaultVariantOptions,
  getProductDerivedData,
} from 'apptile-shopify';
// import FastImage from 'react-native-fast-image';

const localWishlistModelSel = state => datasourceTypeModelSel(state, 'LocalWishlist');
const localWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');
const wishlistItemsSel = createDeepEqualSelector(
  localWishlistModelSel,
  (LWModel): Array<Record<string, any>> | undefined => {
    return LWModel?.get('products');
  },
);
function getWishListItemFromProduct(product: Record<string, any>): Record<string, any> {
  if (!_.isEmpty(product)) {
    const {featuredImage, ...restProduct} = product;
    return {image: featuredImage, ...restProduct};
  }
}

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');
const shopifyProductCacheSel = state => shopifyProductCacheSelector(state, 'shopifyV_22_10');
const makePDPHelperModelSel = pageKey => {
  return state => selectPluginStageModel(state, pageKey, 'PDP');
};
const defaultPageParams = {};
const emptyProduct = undefined;

const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, (shopifyDS): string | unknown => {
  return shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']);
});

const initialState = {
  product: undefined,
  variantCount: 1,
  productOptions: [],
  optionNames: [],
  selectedOptions: {},
  displayOptions: {},
  activeVariant: {},
  variantImages: [],
  productHandle: '',
  selectVariantOption: 'action',
};

function PDPReducer(state, action) {
  switch (action.type) {
    case 'SET_PRODUCT':
      const product = action.product;
      if (product) {
        const derivedData = getProductDerivedData(product);
        const defaultOptionsData = getProductDefaultVariantOptions(product);
        return {...state, ...derivedData, ...defaultOptionsData, product};
      }
      return {...state, product};
    case 'SET_ACTIVE_VARIANT':
      const variant = action.variant;
      if (variant && variant.id && state.product) {
        const variantImages = getActiveVariantImages(state.product, variant);
        return {...state, activeVariant: variant, variantImages};
      }
      return state;
    default:
      return state;
  }
}

const StandardPDP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const buttonTheme = themeEvaluator('tile.button.primary');
  const textColor = themeEvaluator('colors.onBackground');
  const tinyTextColor = tinycolor(textColor);
  const primaryColor = themeEvaluator('colors.primary');
  const secondaryColor = themeEvaluator('colors.secondary');
  const {
    typography: buttonTypo,
    color,
    disabledColor,
    disabledBackgroundColor,
    backgroundColor,
    ...buttonStyles
  } = buttonTheme;
  const buttonTextStyles = generateTypographyByPlatform(buttonTypo, loadedFonts);

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);
  const [product, setProduct] = useState({});
  const productCacheByHandle = useSelector(shopifyProductCacheSel);
  const PDPModelSel = useRef(makePDPHelperModelSel(pageKey));
  const PDPHelperModel = useSelector(PDPModelSel.current);
  const [PDP, PDPDispatch] = useReducer(PDPReducer, initialState);
  const [cartItems, setCartItems] = useState([]);
  const [isInCart, setIsInCart] = useState(false);
  const [cartQuantity, setCartQuantity] = useState(0);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const currentAspectRatio = Number(pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY) ?? '0.9');
  const pdpImageResize = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY) ?? 'cover';
  const pdpHideWishlist = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius = Number(pdpSettings.getSettingValue(PDP_BORDER_RADIUS) ?? '0');
  const storeCurrency = useSelector(shopifyStoreCurrencySel);

  const currProduct =
    (PDPHelperModel?.get('product') || (productCacheByHandle && productCacheByHandle[params.productHandle])) ??
    emptyProduct;
  const currVariant =
    PDPHelperModel?.get('activeVariant') ||
    (productCacheByHandle &&
      productCacheByHandle[params.productHandle] &&
      productCacheByHandle[params.productHandle]?.activeVariant);
  useEffect(() => {
    if (product !== currProduct) {
      setProduct(currProduct);
      PDPDispatch({type: 'SET_PRODUCT', product: currProduct});
      if (currProduct?.title) navigation.setOptions({title: currProduct?.title});
    }
  }, [currProduct, navigation, product]);
  useEffect(() => {
    if (PDP.activeVariant !== currVariant) {
      PDPDispatch({type: 'SET_ACTIVE_VARIANT', variant: currVariant});
    }
  }, [PDP.activeVariant, currVariant]);
  // useEffect(() => {
  //   if (PDP.variantImages?.length) {
  //     FastImage.preload(
  //       PDP.variantImages?.map(img => {
  //         return {
  //           uri: img,
  //         };
  //       }),
  //     );
  //   }
  // }, [PDP.variantImages]);

  /****************************************
   * Handle Cart.
   ***********************************/
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);
  useEffect(() => {
    const cartEntry = cartItems?.filter(cartItem => cartItem?.merchandiseId === PDP.activeVariant?.id);
    if (!_.isEmpty(cartEntry)) {
      const cq = _.first(cartEntry)?.displayQuantity;
      if (cq !== 0) setIsInCart(true);
      else setIsInCart(false);
      setCartQuantity(cq);
    } else {
      setIsInCart(false);
      setCartQuantity(0);
    }
  }, [PDP.activeVariant?.id, cartItems, ShopifyDSModel]);

  /****************************************
   * Handle Wishlist.
   ***********************************/

  const LWModel = useSelector(localWishlistModelSel);
  const LWConfig = useSelector(localWishlistConfigSel);
  const LocalWishListItems = useSelector(wishlistItemsSel);
  const [wishlistItems, setWishlistItems] = useState([]);
  const [isInWishlist, setIsInWishlist] = useState(false);
  useEffect(() => {
    if (_.isArray(LocalWishListItems) && !_.isEqual(wishlistItems, LocalWishListItems)) {
      setWishlistItems(LocalWishListItems);
    }
  }, [LocalWishListItems, wishlistItems]);
  useEffect(() => {
    const wlEntry = wishlistItems?.filter(wlItem => wlItem?.id == product?.id?.split('/')?.pop());
    if (!_.isEmpty(wlEntry)) {
      setIsInWishlist(true);
    } else {
      setIsInWishlist(false);
    }
  }, [product?.id, wishlistItems]);

  /******************************
   * CALLBACKS
   *******************************/

  const addToCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product added to Cart', {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP?.activeVariant?.salePrice,
          productId: PDP?.product?.id,
          productType: PDP?.product?.productType,
          quantity: 1,
          title: PDP?.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP?.product?.vendor,
        }),
      );
    }
  }, [PDP.activeVariant, PDP.product, ShopifyDSConfig, ShopifyDSModel, dispatch, storeCurrency]);

  const increaseCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'addToCart', {
          currency: storeCurrency,
          price: PDP?.activeVariant?.salePrice,
          productId: PDP?.product?.id,
          productType: PDP?.product?.productType,
          quantity: 1,
          title: PDP?.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP?.product?.vendor,
        }),
      );

      performHapticFeedback('tick');
    }
  }, [PDP, ShopifyDSConfig, ShopifyDSModel, dispatch, storeCurrency]);
  const decreaseCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'decreaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
            },
          },
        }),
      );

      dispatch(
        sendAnalyticsEvent('track', 'removeFromCart', {
          currency: storeCurrency,
          price: PDP?.activeVariant?.salePrice,
          productId: PDP?.product?.id,
          productType: PDP?.product?.productType,
          quantity: 1,
          title: PDP?.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP?.product?.vendor,
        }),
      );

      performHapticFeedback('tick');
    }
  }, [PDP.activeVariant, PDP.product, ShopifyDSConfig, ShopifyDSModel, dispatch, storeCurrency]);

  const goToCartCallback = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const addToWishList = useCallback(() => {
    const productObj = getWishListItemFromProduct(product);
    if (productObj) {
      dispatch(
        triggerAction({
          pluginConfig: LWConfig,
          pluginModel: LWModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'addProductToWishlist',
            params: {
              productId: product?.id,
              productHandle: product?.handle,
            },
          },
        }),
      );
      setIsInWishlist(true);
      performHapticFeedback('tap');
      toast.show('Product added to Wishlist', {
        type: 'info',
        placement: 'top',
        duration: 2000,
        style: {marginTop: 20},
      });

      dispatch(
        sendAnalyticsEvent('track', 'addToWishlist', {
          productId: PDP?.product?.id?.split('/')?.pop(),
          productHandle: PDP?.product?.handle,
          productType: PDP?.product?.productType,
          currency: storeCurrency,
          price: PDP?.product?.variants[0]?.salePrice,
          quantity: 1,
          title: PDP?.product?.title,
          variantId: PDP?.activeVariant?.id,
          variantTitle: PDP?.activeVariant?.title,
          brand: PDP?.product?.vendor,
        }),
      );
    }
  }, [LWConfig, LWModel, PDP, dispatch, product, storeCurrency]);

  const removeFromWishList = useCallback(() => {
    dispatch(
      triggerAction({
        pluginConfig: LWConfig,
        pluginModel: LWModel,
        pluginSelector: ['localWishlist'],
        eventModelJS: {
          value: 'removeProductFromWishlist',
          params: {
            productId: product?.id,
            productHandle: product?.handle,
          },
        },
      }),
    );
    performHapticFeedback('tap');
    toast.show('Product removed from Wishlist', {
      type: 'info',
      placement: 'top',
      duration: 2000,
      style: {marginTop: 20},
    });
  }, [LWConfig, LWModel, dispatch, product]);

  const cardBackgroundColor = themeEvaluator('colors.background');

  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1}} edges={['bottom', 'left', 'right']}>
        <ApptileScrollView
          contentInsetAdjustmentBehavior="automatic"
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: cardBackgroundColor, flex: 1}}>
          <View>
            <FlatListSlider
              styles={{width: '100%'}}
              data={PDP.variantImages}
              timer={0}
              autoscroll={false}
              height={400}
              aspectRatio={currentAspectRatio}
              resizeMode={pdpImageResize}
            />
            <View style={[fixedStyles.wishlistContainer]}>
              {!pdpHideWishlist && (
                <>
                  {isInWishlist ? (
                    <Pressable onPress={removeFromWishList}>
                      <MaterialCommunityIcons size={24} name="heart" color={primaryColor} />
                    </Pressable>
                  ) : (
                    <Pressable onPress={addToWishList}>
                      <MaterialCommunityIcons size={24} name="heart-outline" color={primaryColor} />
                    </Pressable>
                  )}
                </>
              )}
            </View>
          </View>

          <View style={[fixedStyles.titleContainer]}>
            <Text style={[headingStyles, {color: textColor}]}>{product?.title}</Text>
          </View>
          <View style={[fixedStyles.priceContainer]}>
            <Text style={[subHeadingStyles, fixedStyles.mainPrice, {color: textColor}]}>
              {PDP?.activeVariant?.displaySalePrice}
            </Text>
            {PDP?.activeVariant?.displaySalePrice !== PDP?.activeVariant?.displayPrice ? (
              <Text
                style={[
                  bodyStyles,
                  fixedStyles.strikeOffPrice,
                  {
                    color: tinyTextColor.isDark()
                      ? tinyTextColor.lighten(25).toHex8String()
                      : tinyTextColor.darken(25).toHex8String(),
                    textDecorationLine: 'line-through',
                  },
                ]}>
                {PDP?.activeVariant?.displayPrice}
              </Text>
            ) : (
              <></>
            )}
          </View>
          <EmbeddedAppPageContainer {...screenProps} />
        </ApptileScrollView>
        <View style={[fixedStyles.bottomBar]}>
          {PDP.activeVariant && PDP.activeVariant?.availableForSale === false ? (
            <View style={[fixedStyles.ctaButton]}>
              <Text style={[headingStyles, {color: textColor}]}>Out of Stock</Text>
            </View>
          ) : isInCart ? (
            <View style={[fixedStyles.bottomPanel]}>
              <Pressable
                style={[fixedStyles.bottomControls, buttonStyles, {backgroundColor, borderRadius: pdpBorderRadius}]}
                onPress={goToCartCallback}>
                <Text style={[buttonTextStyles, {color}]}>Go to cart</Text>
              </Pressable>
              <View style={[fixedStyles.bottomControls]}>
                <Pressable style={[fixedStyles.bottomPanel]} onPress={decreaseCartCallback}>
                  <Text style={[buttonTextStyles, {color: textColor}]}>-</Text>
                </Pressable>
                <View style={[fixedStyles.bottomPanel]}>
                  <Text style={[buttonTextStyles, {color: textColor}]}>{cartQuantity}</Text>
                </View>
                <Pressable style={[fixedStyles.bottomPanel]} onPress={increaseCartCallback}>
                  <Text style={[buttonTextStyles, {color: textColor}]}>+</Text>
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              style={[fixedStyles.ctaButton, buttonStyles, {backgroundColor, borderRadius: pdpBorderRadius}]}
              onPress={addToCartCallback}>
              <Text style={[buttonTextStyles, {color}]}>Add to cart</Text>
            </Pressable>
          )}
        </View>
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const fixedStyles = StyleSheet.create({
  titleContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  priceContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 28,
    lineHeight: 34,
    color: '#e65a4c',
    // marginHorizontal: 4,
  },
  strikeOffPrice: {
    marginLeft: 10,
    marginRight: 4,
  },
  detailsSectionContainer: {
    paddingHorizontal: 12,
  },
  detailTextContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  carouselContainer: {
    marginVertical: 8,
    paddingVertical: 4,
    backgroundColor: '#fff',
  },
  carouselTitleText: {
    marginVertical: 8,
    marginHorizontal: 12,
  },
  bottomBar: {
    height: 60,
    flexBasis: 60,
    flex: 0,
    padding: 6,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wishlistContainer: {
    width: 36,
    height: 36,
    flex: 0,
    position: 'absolute',
    bottom: 35,
    right: 30,
    borderRadius: 36,
    backgroundColor: '#fff6',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default registerNativePage('StandardPDP', StandardPDP, {}, {});
