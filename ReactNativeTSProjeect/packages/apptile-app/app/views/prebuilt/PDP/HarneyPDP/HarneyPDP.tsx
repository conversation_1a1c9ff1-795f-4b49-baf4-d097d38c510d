import _ from 'lodash';
import React, {useCallback, useEffect, useReducer, useRef, useState} from 'react';
import {ActivityIndicator, Pressable, StyleSheet, Text, View} from 'react-native';
import {getShadowStyle} from 'apptile-core';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';

import {
  navigateToScreen, 
  sendAnalyticsEvent, 
  triggerAction,
  createDeepEqualSelector,
  useLoadedFonts,
  MaterialCommunityIcons,
  GetRegisteredPluginInfo,
  performHapticFeedback,
  selectPluginConfig,
  datasourceTypeModelSel,
  selectPluginStageModel,
  shopifyProductCacheSelector,
  selectAppSettingsForKey,
  generateTypographyByPlatform,
  useTheme,
  SettingsConfig,
  CurrentScreenContext,
  EmbeddedAppPageContainer,
  ApptileScrollView,
  PDP_BORDER_RADIUS,
  PDP_HIDE_WISHLIST,
  PDP_IMAGE_ASPECT_RATIO_KEY,
  PDP_IMAGE_RESIZE_KEY,
  PDP_SETTINGS_KEY 
} from 'apptile-core';

import HarneyHeader from '../../custom/Harney/HarneyHeader';
import {registerNativePage} from '../../prebuilt';

import {processShopifyGraphqlQueryResponse} from 'apptile-core';
import {
  getActiveVariantImages,
  getProductDefaultVariantOptions,
  getProductDerivedData,
} from 'apptile-shopify';
import {FlatListSlider} from 'apptile-plugins';
import {ImageComponent} from 'apptile-core';

const isValidColor = (colorString: string) => {
  return /^#[0-9A-F]{3,6}$/i.test(colorString);
};

const shopifyQueryExecutor = async (model: any, queryName: string, inputVariables: any) => {
  const queryRunner = model.get('queryRunner');
  const shopConfig = model.get('shop');

  const shopifyDatasouceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  const queries = shopifyDatasouceConfig?.plugin?.getQueries();
  const querySchema = _.get(queries, queryName);
  if (!querySchema) return {};

  const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
  const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
  const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(
    response,
    querySchema,
    shopConfig,
    model,
  );
  return {transformedData, transformedError};
};

const flitsWishlistModelSel = state => datasourceTypeModelSel(state, 'flitsWishlist');

const flitsWishlistConfigSel = state => selectPluginConfig(state, null, 'localWishlist');

const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
const shopifyCartSelector = createDeepEqualSelector(
  shopifyModelSel,
  (shopifyDS): Array<Record<string, any>> | undefined => {
    return shopifyDS?.get('currentCartLineItems');
  },
);
const shopifyConfigSel = state => selectPluginConfig(state, null, 'shopify');
const shopifyProductCacheSel = state => shopifyProductCacheSelector(state, 'shopifyV_22_10');
const makePDPHelperModelSel = pageKey => {
  return state => selectPluginStageModel(state, pageKey, 'PDP');
};

const makeUnitToServingModelSel = pageKey => {
  return state => {
    return selectPluginStageModel(state, pageKey, 'UnitToServingState');
  };
};

const defaultPageParams = {};
const emptyProduct = undefined;

const initialState = {
  product: undefined,
  variantCount: 1,
  productOptions: [],
  optionNames: [],
  selectedOptions: {},
  displayOptions: {},
  activeVariant: {},
  variantImages: [],
  productHandle: '',
  selectVariantOption: 'action',
  quickCheckoutLoading: false,
  quickCheckoutDetails: {},
};

function PDPReducer(state, action) {
  switch (action.type) {
    case 'SET_PRODUCT':
      const product = action.product;
      if (product) {
        const derivedData = getProductDerivedData(product);
        const defaultOptionsData = getProductDefaultVariantOptions(product);
        return {...state, ...derivedData, ...defaultOptionsData, product};
      }
      return {...state, product};
    case 'SET_ACTIVE_VARIANT':
      let variant = action.variant;
      if (variant && state.product) {
        const variantImages = getActiveVariantImages(state.product, variant);
        return {...state, activeVariant: variant, variantImages};
      }
      return state;
    case 'CREATE_CHECKOUT_INIT':
      return {...state, quickCheckoutLoading: true};
    case 'CREATE_CHECKOUT_FAILED':
      return {...state, quickCheckoutLoading: false};
    case 'CREATE_CHECKOUT_COMPLETED':
      return {...state, quickCheckoutLoading: false, quickCheckoutDetails: action.checkout};
    default:
      return state;
  }
}

const HarneyPDP: React.FC = screenProps => {
  const {navigation, route, isEditable, screen} = screenProps;
  const dispatch = useDispatch();

  const pageId = screen?.screen;
  const pageKey = route.key;
  const pageParams = route.params ?? defaultPageParams;
  const [params, setParams] = useState(pageParams);
  useEffect(() => {
    if (!_.isEqual(pageParams, params)) {
      setParams(pageParams);
    }
  }, [pageParams, params]);

  const {themeEvaluator} = useTheme();
  const {loadedFonts} = useLoadedFonts();
  const headingStyles = generateTypographyByPlatform(themeEvaluator('tile.text.heading.typography'), loadedFonts);
  const subHeadingStyles = generateTypographyByPlatform(themeEvaluator('typography.subHeading'), loadedFonts);
  const bodyStyles = generateTypographyByPlatform(themeEvaluator('typography.body'), loadedFonts);
  const buttonTheme = themeEvaluator('tile.button.primary');
  const primaryOutlineButtonTheme = themeEvaluator('tile.button.primaryOutline');
  const textColor = themeEvaluator('colors.onBackground');
  const primaryColor = themeEvaluator('colors.primary');
  const quinaryColor = isValidColor(themeEvaluator('colors.quinary')) ? themeEvaluator('colors.quinary') : textColor;
  const {
    typography: buttonTypo,
    color,
    disabledColor,
    disabledBackgroundColor,
    backgroundColor,
    ...buttonStyles
  } = buttonTheme;

  const {
    typography: buttonOutlineTypo,
    color: colorOutline,
    disabledColor: disabledColorOutline,
    disabledBackgroundColor: disabledBackgroundColorOutline,
    backgroundColor: backgroundColorOutline,
    ...outlineButtonStyles
  } = primaryOutlineButtonTheme;

  const buttonTextFontStyles = generateTypographyByPlatform(buttonTypo, loadedFonts);
  const buttonTextStyles = [buttonTextFontStyles, subHeadingStyles];

  const smallShadowStyles = _.merge({shadowColor: '#000'}, getShadowStyle(1));

  const ShopifyDSModel = useSelector(shopifyModelSel);
  const ShopifyDSConfig = useSelector(shopifyConfigSel);
  const [product, setProduct] = useState({});
  const productCacheByHandle = useSelector(shopifyProductCacheSel);
  const PDPModelSel = useRef(makePDPHelperModelSel(pageKey));
  const PDPHelperModel = useSelector(PDPModelSel.current);
  const [PDP, PDPDispatch] = useReducer(PDPReducer, initialState);
  const [cartItems, setCartItems] = useState([]);
  const [isInCart, setIsInCart] = useState(false);

  const [ratingVal, setRatingVal] = useState(0);
  const [ratingCount, setRatingCount] = useState('');

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const pdpSettings: SettingsConfig = useSelector(settingsSelector(PDP_SETTINGS_KEY));
  const currentAspectRatio = Number(pdpSettings.getSettingValue(PDP_IMAGE_ASPECT_RATIO_KEY) ?? '0.9');
  const pdpImageResize = pdpSettings.getSettingValue(PDP_IMAGE_RESIZE_KEY) ?? 'cover';
  const pdpHideWishlist = pdpSettings.getSettingValue(PDP_HIDE_WISHLIST) ?? false;
  const pdpBorderRadius = Number(pdpSettings.getSettingValue(PDP_BORDER_RADIUS) ?? '5');

  const currProduct =
    (PDPHelperModel?.get('product') || (productCacheByHandle && productCacheByHandle[params.productHandle])) ??
    emptyProduct;
  const currVariant =
    PDPHelperModel?.get('activeVariant') ||
    (productCacheByHandle &&
      productCacheByHandle[params.productHandle] &&
      productCacheByHandle[params.productHandle]?.activeVariant);

  useEffect(() => {
    if (product !== currProduct && !_.isEmpty(currProduct)) {
      setProduct(currProduct);
      const ratingMetaF = _.find(currProduct?.metafields, mField => mField?.key == 'rating')?.value;
      setRatingVal(_.round(ratingMetaF?.value, 2));
      setRatingCount(_.find(currProduct?.metafields, mField => mField?.key == 'rating_count')?.value);

      PDPDispatch({type: 'SET_PRODUCT', product: currProduct});
      if (currProduct?.title) navigation.setOptions({title: currProduct?.title});
    }
  }, [currProduct, navigation, product]);

  useEffect(() => {
    if (!_.isEmpty(PDP.activeVariant) && !_.isEmpty(currVariant) && PDP.activeVariant?.id !== currVariant?.id) {
      PDPDispatch({
        type: 'SET_ACTIVE_VARIANT',
        variant: currVariant,
      });
    }
  }, [PDP.activeVariant, currVariant]);

  /****************************************
   * Handle Cart.
   ***********************************/
  const ShopifyCartLineItems = useSelector(shopifyCartSelector);
  useEffect(() => {
    if (_.isArray(ShopifyCartLineItems) && !_.isEqual(cartItems, ShopifyCartLineItems)) {
      setCartItems(ShopifyCartLineItems);
    }
  }, [cartItems, ShopifyDSModel, ShopifyCartLineItems]);
  useEffect(() => {
    const cartEntry = cartItems?.filter(cartItem => cartItem?.merchandiseId === PDP.activeVariant?.id);
    if (_.isEmpty(cartEntry)) {
      // const cq = _.first(cartEntry)?.displayQuantity;
      // if (cq !== 0) setIsInCart(true);
      setIsInCart(false);
      // setCartQuantity(cq);
    } else {
      setIsInCart(true);
      // setCartQuantity(0);
    }
  }, [PDP.activeVariant?.id, cartItems, ShopifyDSModel]);

  /****************************************
   * Handle Wishlist.
   ***********************************/
  const unitToServingModelSel = useRef(makeUnitToServingModelSel(pageKey));
  const UnitToServingModel = useSelector(unitToServingModelSel.current);

  const flitsModel = useSelector(flitsWishlistModelSel);
  const flitsConfig = useSelector(flitsWishlistConfigSel);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [isWishlistOperationInTransit, setIsInWishlistOperationInTransit] = useState(false);

  useEffect(() => {
    const checkWishlist = flitsModel?.get('checkWishlist');
    const isLoading = flitsModel?.get('loading');
    console.log('wishlistLoading', isLoading);
    let inWishlist = false;
    if (checkWishlist && PDP.product?.id) {
      inWishlist = checkWishlist(PDP.product?.id);
    }
    setIsInWishlistOperationInTransit(isLoading);
    setIsInWishlist(inWishlist);
  }, [PDP.product?.id, flitsModel]);

  /******************************
   * CALLBACKS
   *******************************/

  const dispatchAddToCartEvent = useCallback(() => {
    dispatch(
      sendAnalyticsEvent('track', 'addToCart', {
        currency: PDP.product?._raw?.priceRange?.maxVariantPrice?.currencyCode || 'USD',
        price: PDP.activeVariant.salePrice,
        productId: PDP.product.id,
        productType: PDP.product.productType,
        quantity: '1',
        title: PDP.product.title,
        variantId: PDP.activeVariant.id,
        variantTitle: PDP.activeVariant.title,
      }),
    );
  }, [PDP, dispatch]);

  const addToCartCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        triggerAction({
          pluginConfig: ShopifyDSConfig,
          pluginModel: ShopifyDSModel,
          pluginSelector: ['shopify'],
          eventModelJS: {
            value: 'increaseCartLineItemQuantity',
            params: {
              merchandiseId: PDP.activeVariant.id,
              quantity: 1,
              syncWithShopify: false,
              sellingPlanId: null,
            },
          },
        }),
      );
      performHapticFeedback('tap');
      toast.show('Product added to cart.', {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 90},
      });
      dispatchAddToCartEvent();
    }
  }, [PDP.activeVariant, ShopifyDSConfig, ShopifyDSModel, dispatch, dispatchAddToCartEvent]);

  const subscribeCallback = useCallback(() => {
    if (PDP.activeVariant && PDP.activeVariant?.availableForSale) {
      dispatch(
        navigateToScreen('ProductBuy', {
          isSubscriptionPurchase: true,
          productHandle: PDP.product?.handle,
          activeVariantId: PDP.activeVariant.id,
        }),
      );

      performHapticFeedback('tap');
    }
  }, [PDP.activeVariant, PDP.product?.handle, dispatch]);

  const goToCartCallback = useCallback(() => {
    performHapticFeedback('tap');
    dispatch(navigateToScreen('Cart', {}));
  }, [dispatch]);

  const addToWishList = useCallback(() => {
    const loggedInUserAccessToken = ShopifyDSModel.get('loggedInUserAccessToken');
    if (PDP.product) {
      dispatch(
        triggerAction({
          pluginConfig: flitsConfig,
          pluginModel: flitsModel,
          pluginSelector: ['localWishlist'],
          eventModelJS: {
            value: 'addProductToWishlist',
            params: {
              productId: PDP.product?.id,
              productHandle: PDP.product?.handle,
              customerAccessToken: loggedInUserAccessToken?.accessToken,
            },
          },
        }),
      );

      if (loggedInUserAccessToken?.accessToken) {
        setIsInWishlist(true);
        setIsInWishlistOperationInTransit(true);
        console.log('Setting true');
      }
      performHapticFeedback('tap');
    }
  }, [flitsConfig, flitsModel, dispatch, PDP.product, ShopifyDSModel]);

  const removeFromWishList = useCallback(() => {
    const loggedInUserAccessToken = ShopifyDSModel.get('loggedInUserAccessToken');
    dispatch(
      triggerAction({
        pluginConfig: flitsConfig,
        pluginModel: flitsModel,
        pluginSelector: ['localWishlist'],
        eventModelJS: {
          value: 'removeProductFromWishlist',
          params: {
            productId: PDP.product?.id,
            productHandle: PDP.product?.handle,
            customerAccessToken: loggedInUserAccessToken?.accessToken,
          },
        },
      }),
    );
    performHapticFeedback('tap');
  }, [flitsConfig, flitsModel, dispatch, PDP.product, ShopifyDSModel]);

  const salePrice = PDP.activeVariant?.salePrice;
  const unitValue = PDP.selectedOptions?.Unit?.value;

  const currencyFormatter = ShopifyDSModel?.get('formatCurrency');
  const unitToServingMap = UnitToServingModel?.get('value', {});

  const perCupPrice =
    salePrice && unitValue && _.get(unitToServingMap, unitValue?.toLowerCase(), 0) > 0
      ? _.round(salePrice / _.get(unitToServingMap, unitValue?.toLowerCase(), 0), 2)
      : 0;
  const perCupPriceWithCurrency = currencyFormatter && perCupPrice ? currencyFormatter(perCupPrice) : null;

  const [isSubscriptionAvailable, setIsSubscriptionAvailable] = useState(false);
  const [subscriptionPrice, setSubscriptionPrice] = useState(null);

  useEffect(() => {
    const subscriptionPlanExists = PDP?.activeVariant
      ? _.find(PDP.product?._raw?.variants, v => v?.id == PDP.activeVariant?.id)?.sellingPlanAllocations
      : [];

    if (subscriptionPlanExists?.length > 0) {
      const subscriptionPriceVal = _.first(_.first(subscriptionPlanExists)?.priceAdjustments)?.perDeliveryPrice?.amount;
      const subscriptionDisplayPrice = currencyFormatter
        ? currencyFormatter(subscriptionPriceVal)
        : subscriptionPriceVal;
      setSubscriptionPrice(subscriptionDisplayPrice);
      setIsSubscriptionAvailable(true);
    } else {
      setIsSubscriptionAvailable(false);
    }
  }, [PDP.activeVariant, PDP.product?._raw, currencyFormatter]);

  // Both Harney PDP and checkoutCreate API is deprecated now
  // const quickCheckoutCallback = useCallback(() => {
  //   const createCheckout = async () => {
  //     try {
  //       const {transformedData, transformedError} = await shopifyQueryExecutor(ShopifyDSModel, 'CreateCheckoutV1', {
  //         lineItems: [
  //           {
  //             quantity: 1,
  //             variationId: PDP.activeVariant?.id,
  //           },
  //         ],
  //       });

  //       if (transformedData?.id) {
  //         PDPDispatch({type: 'CREATE_CHECKOUT_COMPLETED', checkout: transformedData});
  //         dispatch(
  //           navigateToScreen('CheckoutPaymentMethod', {
  //             checkoutId: transformedData?.id,
  //           }),
  //         );
  //       }
  //     } catch (error) {
  //       console.log(`Error while creating checkout`, error);
  //       toast.show('Error while creating checkout. Please try again later!', {
  //         type: 'error',
  //         placement: 'bottom',
  //         duration: 2000,
  //         style: {marginBottom: 90},
  //       });
  //       PDPDispatch({type: 'CREATE_CHECKOUT_FAILED'});
  //     }
  //   };
  //   PDPDispatch({type: 'CREATE_CHECKOUT_INIT'});
  //   createCheckout();
  //   return () => {};
  // }, [ShopifyDSModel, PDP.activeVariant?.id, dispatch]);

  return (
    <CurrentScreenContext.Provider value={screen}>
      <SafeAreaView style={{flex: 1}} edges={['top', 'bottom', 'left', 'right']}>
        <HarneyHeader />
        <ApptileScrollView
          contentInsetAdjustmentBehavior="automatic"
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: '#f9f9f9', flex: 1}}>
          <View>
            <FlatListSlider
              styles={{width: '100%'}}
              data={PDP.variantImages}
              timer={0}
              autoscroll={false}
              height={445}
              aspectRatio={currentAspectRatio}
              resizeMode={pdpImageResize}
              indicatorContainerStyle={{position: 'absolute', bottom: 18}}
              indicatorActiveColor={'#888'}
              indicatorStyle={{width: 8, height: 8, borderRadius: 8}}
              indicatorActiveWidth={8}
            />
            {!pdpHideWishlist && (
              <View style={[fixedStyles.wishlistContainer]}>
                {!isWishlistOperationInTransit ? (
                  isInWishlist ? (
                    <Pressable onPress={removeFromWishList}>
                      <MaterialCommunityIcons size={24} name="heart" color={primaryColor} />
                    </Pressable>
                  ) : (
                    <Pressable onPress={addToWishList}>
                      <MaterialCommunityIcons size={24} name="heart-outline" color={primaryColor} />
                    </Pressable>
                  )
                ) : (
                  <ActivityIndicator size={24} color={primaryColor} />
                )}
              </View>
            )}

            {ratingVal ? (
              <View style={[fixedStyles.ratingBadgeContainer, smallShadowStyles]}>
                <View style={[fixedStyles.ratingBadgeStartCont]}>
                  <Text style={[bodyStyles, fixedStyles.ratingBadgeTitle]}>{ratingVal}</Text>
                  <MaterialCommunityIcons style={[{color: primaryColor}, fixedStyles.ratingBadgeTitle]} name={'star'} />
                </View>
                <Text style={[bodyStyles, fixedStyles.ratingBadgeText]}>({ratingCount} Ratings)</Text>
              </View>
            ) : (
              <></>
            )}
          </View>
          <View style={fixedStyles.titleCardOuterCont}>
            <View style={fixedStyles.titleCardCont}>
              <View style={[fixedStyles.priceContainer, {flex: 1}]}>
                <Text style={[subHeadingStyles, fixedStyles.mainPrice, {color: textColor}]}>
                  {PDP?.activeVariant?.displaySalePrice}
                </Text>
                {PDP?.activeVariant?.displaySalePrice !== PDP?.activeVariant?.displayPrice ? (
                  <Text
                    style={[
                      bodyStyles,
                      fixedStyles.strikeOffPrice,
                      {color: '#757575', textDecorationLine: 'line-through'},
                    ]}>
                    {PDP?.activeVariant?.displayPrice}
                  </Text>
                ) : (
                  <></>
                )}
              </View>

              {perCupPriceWithCurrency ? (
                <View style={[fixedStyles.perCupPriceCont]}>
                  <ImageComponent
                    style={[{width: 20, height: 10}, fixedStyles.pricePerCupText]}
                    source={{
                      uri: 'https://cdn.apptile.io/2aca332b-5107-4666-81d1-4e6c08e42d46/e5ae2c3b-dd5f-4dff-aa68-c86366ad7e78/original.png',
                    }}
                    resizeMode={'contain'}
                  />
                  <Text style={fixedStyles.pricePerCupText}>{perCupPriceWithCurrency}</Text>
                  <Text style={fixedStyles.pricePerCupText}>per cup</Text>
                </View>
              ) : (
                <></>
              )}
            </View>

            <View style={[fixedStyles.titleContainer]}>
              <Text style={[headingStyles, {color: textColor}]}>{product?.title}</Text>
            </View>
          </View>

          <EmbeddedAppPageContainer {...screenProps} />
        </ApptileScrollView>

        <View style={[fixedStyles.bottomBar]}>
          {!PDP?.activeVariant?.id ? (
            <ActivityIndicator size={20} color={primaryColor} />
          ) : (
            <>
              {PDP?.activeVariant?.availableForSale ? (
                <View style={[fixedStyles.bottomPanel]}>
                  {/* One Time Purchase Starts */}

                  {isSubscriptionAvailable ? (
                    <View style={{flexDirection: 'row', flex: 1}}>
                      {!isInCart ? (
                        <Pressable
                          style={[fixedStyles.bottomControls, outlineButtonStyles, {borderRadius: pdpBorderRadius}]}
                          onPress={addToCartCallback}>
                          <Text style={[buttonTextStyles, {color: colorOutline}]}>Add to Cart</Text>
                        </Pressable>
                      ) : (
                        <Pressable
                          style={[
                            fixedStyles.bottomControls,
                            outlineButtonStyles,
                            {borderColor: quinaryColor, borderRadius: pdpBorderRadius},
                          ]}
                          onPress={goToCartCallback}>
                          <Text style={[buttonTextStyles, {color: quinaryColor}]}>Go to Cart</Text>
                        </Pressable>
                      )}

                      <Pressable
                        style={[fixedStyles.bottomControls, buttonStyles, {backgroundColor, flexDirection: 'column'}]}
                        onPress={subscribeCallback}>
                        <Text style={[buttonTextStyles, {color}]}>Subscribe Now</Text>

                        <View style={{flexDirection: 'row'}}>
                          <Text style={[bodyStyles, {color}]}> {subscriptionPrice}</Text>
                          {PDP?.activeVariant?.displaySalePrice !== PDP?.activeVariant?.displayPrice ? (
                            <Text
                              style={[
                                bodyStyles,
                                fixedStyles.strikeOffPrice,
                                {color, textDecorationLine: 'line-through'},
                              ]}>
                              {PDP?.activeVariant?.displaySalePrice}
                            </Text>
                          ) : (
                            <></>
                          )}
                        </View>
                      </Pressable>
                      {/* One Time Purchase Ends */}
                    </View>
                  ) : (
                    <View style={{flexDirection: 'row', flex: 1}}>
                      {!isInCart ? (
                        <Pressable
                          style={[fixedStyles.bottomControls, outlineButtonStyles, {borderRadius: pdpBorderRadius}]}
                          onPress={addToCartCallback}>
                          <Text style={[buttonTextStyles, {color: colorOutline}]}>Add to Cart</Text>
                        </Pressable>
                      ) : (
                        <Pressable
                          style={[
                            fixedStyles.bottomControls,
                            outlineButtonStyles,
                            {borderColor: quinaryColor, borderRadius: pdpBorderRadius},
                          ]}
                          onPress={goToCartCallback}>
                          <Text style={[buttonTextStyles, {color: quinaryColor}]}>Go to Cart</Text>
                        </Pressable>
                      )}

                      {/* <Pressable
                        style={[fixedStyles.bottomControls, buttonStyles, {backgroundColor}]}
                        onPress={quickCheckoutCallback}>
                        {PDP.quickCheckoutLoading ? (
                          <ActivityIndicator size={24} color={color} />
                        ) : (
                          <Text style={[buttonTextStyles, {color}]}>Buy Now</Text>
                        )}
                      </Pressable> */}
                      {/* One Time Purchase Ends */}
                    </View>
                  )}
                </View>
              ) : (
                <View style={[fixedStyles.ctaButton]}>
                  <Text style={[headingStyles, {color: quinaryColor}]}>Out of Stock</Text>
                </View>
              )}
            </>
          )}
        </View>
      </SafeAreaView>
    </CurrentScreenContext.Provider>
  );
};

const fixedStyles = StyleSheet.create({
  titleCardOuterCont: {backgroundColor: '#FFFFFF', paddingBottom: 8},
  titleCardCont: {flexDirection: 'row', alignItems: 'center'},
  perCupPriceCont: {
    alignItems: 'center',
    padding: 2,
    flexDirection: 'row',
  },
  pricePerCupText: {paddingRight: 4, color: '#8d8d8d'},
  titleContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  priceContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPrice: {
    fontSize: 28,
    lineHeight: 34,
    color: '#e65a4c',
    // marginHorizontal: 4,
  },
  strikeOffPrice: {
    marginHorizontal: 4,
  },
  detailsSectionContainer: {
    paddingHorizontal: 12,
  },
  detailTextContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  carouselContainer: {
    marginVertical: 8,
    paddingVertical: 4,
    backgroundColor: '#fff',
  },
  carouselTitleText: {
    marginVertical: 8,
    marginHorizontal: 12,
  },
  bottomBar: {
    height: 60,
    flexBasis: 60,
    flex: 0,
    padding: 6,
  },
  ctaButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomPanel: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'stretch',
  },
  bottomControls: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
  },
  wishlistContainer: {
    width: 36,
    height: 36,
    flex: 0,
    position: 'absolute',
    bottom: 35,
    right: 30,
    borderRadius: 36,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingBadgeContainer: {
    position: 'absolute',
    bottom: 35,
    padding: 4,
    paddingTop: 8,
    paddingBottom: 8,
    left: 10,
    backgroundColor: '#ffffff',
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: 4,
  },
  ratingBadgeText: {paddingRight: 4, fontSize: 12},
  ratingBadgeTitle: {fontSize: 16, lineHeight: 20},
  ratingBadgeStartCont: {flexDirection: 'row', alignItems: 'center'},
});

export default registerNativePage('HarneyPDP', HarneyPDP, {}, {});
