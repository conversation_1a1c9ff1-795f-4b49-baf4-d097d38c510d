import {getOptimalImageSize, selectAppConfig} from 'apptile-core';
import React, {useState, forwardRef, useImperativeHandle, useCallback} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, Animated, Easing, Dimensions, Image, Platform} from 'react-native';
import {useSelector} from 'react-redux';

const ReactionEmojis = forwardRef((props, ref) => {
  const { dimensions } = props;

  const [emojis, setEmojis] = useState([]);
  let screenWidth = dimensions?.width ?? Dimensions.get('window').width;
  let screenHeight = dimensions?.height ?? Dimensions.get('window').height

  const generateRandomPosition = () => {
    return Math.random() * (screenWidth - 50); // Random horizontal position
  };
  const appConfig = useSelector(selectAppConfig);

  const getDeviceImage = useCallback(
    (assetId: string) => {
      const imageRecord = appConfig.getImageId(assetId);

      const getOptimalImage = (layoutSize: string) => {
        return getOptimalImageSize(imageRecord, layoutSize);
      };
      return {imageRecord, getOptimalImage};
    },
    [appConfig],
  );
  const addEmoji = async (reactionUrl: string) => {
    // Just in case, the reactionUrl is an asset
    if (reactionUrl.startsWith('asset:') && Platform.OS != 'web') {
      const reactionAssetId = reactionUrl.replace('asset:', '');
      const {getOptimalImage} = getDeviceImage(reactionAssetId);
      reactionUrl = getOptimalImage(`700x700`)?.fileUrl;
    }
    try {
      await Image.prefetch(reactionUrl);
    } catch (error) {
      console.log(error)
      return;
    }
    const newEmojis = Array.from({length: 10}, () => {
      const animationValue = new Animated.Value(0);
      const randomLeft = generateRandomPosition();
      const randomBottom = Math.random() * 30;
      const emojiId = Math.random().toString();
      return {id: emojiId, animationValue, left: randomLeft, bottom: randomBottom, reactionImage: reactionUrl};
    });

    setEmojis(prevEmojis => [...prevEmojis, ...newEmojis]);

    newEmojis.forEach(emoji => {
      Animated.timing(emoji.animationValue, {
        toValue: 1,
        duration: 4000,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start(() => {
        setEmojis(prevEmojis => prevEmojis.filter(e => e.id !== emoji.id));
      });
    });
  };

  useImperativeHandle(ref, () => ({
    triggerAddEmoji: addEmoji,
  }));

  return (
    <View style={[styles.container, {position: 'absolute'}]}>
      <View style={[styles.container]}>
        {emojis.map(emoji => (
          <Animated.View
            key={emoji.id}
            style={[
              styles.emoji,
              {
                left: emoji.left,
                bottom: `${emoji.bottom}%`, // Apply random starting height
                transform: [
                  {
                    translateY: emoji.animationValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -screenHeight], // Move from bottom to top
                    }),
                  },
                ],
                opacity: emoji.animationValue.interpolate({
                  inputRange: [0, 0.8, 1],
                  outputRange: [1, 1, 0], // Fade out at the end
                }),
              },
            ]}>
            <Image source={{uri: emoji.reactionImage}} style={{width: 50, height: 50}} />
          </Animated.View>
        ))}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  emoji: {
    position: 'absolute',
    bottom: 50,
    zIndex: 1,
  },
  emojiText: {
    fontSize: 32,
  },
});

export default ReactionEmojis;
