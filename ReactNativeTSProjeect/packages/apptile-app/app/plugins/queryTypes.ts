import {AppPageQueryConfigParams} from 'apptile-core';

export const defaultQueryConfig: AppPageQueryConfigParams = {
  runWhenPageLoads: false,
  runOnPageFocus: false,
  runWhenModelUpdates: false,
  errors: null,
  hasError: false,
  data: null,
  metadata: null,
  rawData: null,
  loading: false,
  timestamp: 0,
  // queryRefreshTime: '',
  // queryThrottleTime: '750',
  // queryTimeout: '10000',
  // queryTriggerDelay: '0',
  // transformer: '',
  // enableTransformer: false,
  // errorTransformer: '',
  // enableErrorTransformer: false,
};
