/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * Generated with the TypeScript template
 * https://github.com/react-native-community/react-native-template-typescript
 *
 * @format
 */
import loadDatasourcePlugins from 'apptile-datasource';
import {initPlugins as initCustomPlugins} from 'apptile-plugins';
import {loadDatasourcePlugins as loadShopifyPlugins} from 'apptile-shopify';
import {store} from 'apptile-core';

import React from 'react';
import * as ReactNativeSvg from 'react-native-svg';
import * as ReactNative from 'react-native';
import * as ApptileCore from 'apptile-core';
import * as ReactRedux from 'react-redux';
import * as ReactNativeSafeAreaContext from 'react-native-safe-area-context';
import * as GorhomPortal from '@gorhom/portal';
import * as ReactNativeReanimated from 'react-native-reanimated';
import * as ReactNativeGestureHandler from 'react-native-gesture-handler';
import * as ReactNavigationNative from '@react-navigation/native';
import * as ReactNativeWebview from 'react-native-webview';
import Jo<PERSON> from 'react-native-joi';
import * as GraphqlTag from 'graphql-tag';
import moment from 'moment';
import lodash from 'lodash';
import {TransformProductVariant, TransformMetafields, CollectionGqls} from 'apptile-shopify';

window.apptileSDK = window.apptileSDK || {};
window.apptileSDK.ApptileCore = ApptileCore;
window.apptileSDK.React = React;
window.apptileSDK.ReactNative = ReactNative;
window.apptileSDK.ReactNativeSvg = ReactNativeSvg;
window.apptileSDK.ReactRedux = ReactRedux;
window.apptileSDK.ReactNativeSafeAreaContext = ReactNativeSafeAreaContext;
window.apptileSDK.Joi = Joi;
window.apptileSDK.GorhomPortal = GorhomPortal;
window.apptileSDK.ReactNativeReanimated = ReactNativeReanimated;
window.apptileSDK.ReactNativeGestureHandler = ReactNativeGestureHandler;
window.apptileSDK.GraphqlTag = GraphqlTag;
window.apptileSDK.ReactNavNative = ReactNavigationNative;
window.apptileSDK.ReactNativeWebview = ReactNativeWebview;
window.apptileSDK.moment = moment;
window.apptileSDK.lodash = lodash;
window.apptileSDK.apptileShopify = {
  TransformProductVariant,
  TransformMetafields,
  CollectionGqls,
};

export const reloadExternalPlugins = manifest => {
  const codeArtefacts = manifest?.codeArtefacts;
  if (window.PLUGIN_SERVER_URL && !window.WEB_PREVIEW_MODE) {
    const appId = manifest.uuid;
    const externalPlugins = fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/list`)
      .then(res => res.json())
      .then(plugins => fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/getall?plugins=${plugins.join(',')}`))
      .then(res => res.text())
      .then(code => {
        const compiled = new Function(code);
        compiled();

        return window.apptileSDK.default;
      })
      .catch(err => {
        // alert("Could not reload plugins!");
        console.error('Error when loading plugins: ', err);
        return [];
      });
    return externalPlugins;
  } else if (Array.isArray(codeArtefacts)) {
    const pluginsUrl = codeArtefacts.find(it => it.type === 'plugins')?.cdnlink;
    if (pluginsUrl) {
      return fetch(pluginsUrl)
        .then(res => res.text())
        .then(code => {
          const compiled = new Function(code);
          compiled();

          return window.apptileSDK.default;
        })
        .catch(err => {
          alert('Could not reload deployed plugins!');
          console.error('Error when loading plugins: ', err);
          return [];
        });
    } else {
      logger.info('Not loading any external plugins as no published plugin bundle was found');
      return Promise.resolve([]);
    }
  } else {
    logger.info('Not loading any external plugins as neither cli nor deployed bundles were detected');
    return Promise.resolve([]);
  }
};

export const initPlugins = manifest => {
  // TODO(gaurav) the manifest based load only needs to happen in web
  const customPlugins = initCustomPlugins();
  let externalPlugins = reloadExternalPlugins(manifest);
  return Promise.all([loadDatasourcePlugins(manifest), externalPlugins]).then(
    ([datasourcePlugins, externalPlugins]) => {
      const shopifyPlugins = loadShopifyPlugins();
      return [...shopifyPlugins, ...externalPlugins, ...customPlugins, ...datasourcePlugins];
    },
  );
};
