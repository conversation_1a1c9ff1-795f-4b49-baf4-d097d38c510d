const path = require("path");
const fs = require("fs");

const webVitalsRegex = /[a-z].initializeWebVitals=function\([a-z]\)\{function [a-z]\([a-z]\)\{return function\(([a-z])\)\{/g;

function setDeplPaths(directoryPath, rootPath, isBeta, buildNumber) {
  var codeBuildNumber = "dummy";
  if (isBeta == "true") {
    codeBuildNumber = process.env.EDITOR_BETA;
  } else {
    codeBuildNumber = process.env.EDITOR;
  }

  if (buildNumber) {
    codeBuildNumber = buildNumber;
  }

  console.log("Making for build number ", codeBuildNumber, isBeta);
  javascript = `console.log('build number: ' + '${codeBuildNumber}'); window.PLATFORM_BUILD_NUMBER = '${codeBuildNumber}';window.BN_FOR_LOGROCKET = '${codeBuildNumber}';\n`;
  javascript += `//BUILDNUMBERSTART---${codeBuildNumber}---BUILDNUMBEREND` 
  if (isBeta == "true") {
    console.log("Adding javascript for tag");
    
    javascript += `
      window.CODEBUILD_BUILD_NUMBER = '${codeBuildNumber}';
      //   setTimeout(() => {
      //     const tag = document.createElement('div');
      //     document.body.appendChild(tag);
      //     tag.setAttribute('style', 'position: fixed; top: 0; right: 0; background: green; color: white;');
      //     tag.innerText = 'Beta build: ${codeBuildNumber}';
      //   }, 100);
    `;
  }

  if (rootPath == "/") {
    rootPath = "";
  }

  const htmlPrepared = new Promise((resolve, reject) => {
    htmlPath = path.resolve(path.join(directoryPath, "index.html"));
    fs.readFile(htmlPath, "utf8", (err, data) => {
      if (err) {
        console.error("Failed to read html file: ", err);
        process.exit(1);
      }

      console.log("Initial html: ", data);
      const updatedHtml = data.replace('console.log("postbuild placeholder")', javascript);
      console.log("Updated html: ", updatedHtml);
      fs.writeFile(htmlPath, updatedHtml, (err) => {
        if (err) {
          console.error("Failed to write html file: ", err);
          reject(err);
        }
        resolve();
      });
    });
  });

  htmlPrepared
    .then(() => {
    fs.readdir(directoryPath, (err, files) => {
      if (err) {
        console.error("Failed to list files in directory: ", err);
        process.exit(1);
      }

      for (let file of files) {
        if (file.includes("preparebuild")) {
          continue;
        }

        const filePath = path.resolve(path.join(directoryPath, file));
        const info = fs.lstatSync(filePath);
        if (!info.isFile()) {
          continue;
        }

        fs.readFile(filePath, "utf-8", (err, data) => {
          if (err) {
            console.error("Failure in trying to process file: ", err);
            return;
          }

          data = data.replaceAll("/__DEPLOYMENT_PATH__", rootPath);
          if (process.env.APPLY_MONKEYPATCHES == "true") {
            data = data.replace(webVitalsRegex, function(matchedString, captured) {
              const replacement = matchedString + 
                "console.log('Shopify data: ', " + captured + 
                ", " + captured + ".id" + 
                ", " + captured + ".name" +
                ", " + captured + ".value" +
                ");"; 
              return replacement;
            });
          }

          fs.writeFile(filePath, data, (err) => {
            if (err) {
              console.error("Failure when trying to write file: ", err);
            }
          });
        });
      }
    });
  })
  .catch(err => {
    console.error("Failure during preparation. See previous errors.", err);
  });
}

function updateWebpackConfig(directoryPath) {
  confFilePath = path.resolve(path.join(directoryPath, "webpack.config.js"));
  fs.readFile(confFilePath, "utf-8", (err, data) => {
    if (err) {
      console.error("Failure in reading webpack config ", err);
      process.exit(1);
    }

    data = data.replaceAll("publicPath: '/',", "publicPath: '/__DEPLOYMENT_PATH__/'");
    fs.writeFile(confFilePath, data, (err) => {
      if (err) {
        console.error("Failed to write webpack config", err);
        process.exit(1);
      }
    });
  });
}

function printUsageAndExit() {
  console.log(
    `
    Usage: 
    preparebuild prebuild <path_to_web_directory>
    preparebuild postbuild <path_to_dist_directory> <root_path_for_assets_on_server> <is_beta>
    `
  );
  process.exit(1);
}

function main() {
  if (process.argv.length < 3) {
    printUsageAndExit();
  }

  const feature = process.argv[2];
  if (feature == "postbuild") {
    if (process.argv.length < 6) {
      console.log("Supply the path of the dist folder and the root path for assets.");
      printUsageAndExit();
    }
    setDeplPaths(process.argv[3], process.argv[4], process.argv[5], process.argv[6]);
  } else if (feature == "prebuild") {
    if (process.argv.length < 4) {
      console.log("Supply the path of the web folder that contains webpack.config.js");
      printUsageAndExit();
    }
    updateWebpackConfig(process.argv[3]);
  } else {
    console.log("First commandline argument must be either prebuild or postbuild");
    printUsageAndExit();
  }

  console.log("done!");
}

main();
