#!/bin/bash -e
cd "$(dirname $0)/../"
project_path=$PWD
build_path="$PWD/build"


echo -e "\n⚙ Building framework bundles...\n"

watchman watch-del-all $project_path
npm i --no-audit

mv .env.json .env.json.backup
echo "{}" > .env.json
mkdir -p $build_path/
npx --yes react-native bundle --dev false --entry-file index.js --bundle-output $build_path/index.android.bundle --platform android
npx --yes react-native bundle --dev false --entry-file index.js --bundle-output $build_path/index.ios.bundle --platform ios
rm .env.json
mv .env.json.backup .env.json 

echo -e "\n\033[0;32mSuccessfully built in $build_path 🎉 🎉 🎉\033[0m"
