import React, { useEffect, useState } from "react";
import { parse } from "search-params";

import {
  StyleSheet,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  View,
  useColorScheme,
  Linking,
  Image,
  ScrollView,
} from "react-native";
import { Colors } from "react-native/Libraries/NewAppScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";

import { NavigatorProps } from "../types";

import HeaderImage from "../assets/header.png";
import HeaderImageLight from "../assets/header.light.png";
import QRIcon from "../assets/qr.icon.png";
import QRIconLight from "../assets/qr.icon.light.png";

const HomeScreen: React.FC<NavigatorProps> = ({ navigation, route }) => {
  const isDarkMode = useColorScheme() === "dark";

  type PrevScn = {
    url: string;
    appName: string;
    appId: string;
    orgName: string;
  };
  const [prevScans, setPrevScans] = useState<PrevScn[] | null>(null);

  const getData = async () => {
    try {
      const urls = JSON.parse(
        (await AsyncStorage.getItem("@prev_scan")) || "[]"
      );
      if (urls.length) {
        const prevScans = urls.reverse().map((url) => {
          const params = parse(url.split("?")[1] || "");
          return {
            url,
            appName: params.appName as string,
            appId: (url.split("?") || "")[0].split("/").pop() as string,
            orgName: params.orgName as string,
          };
        });
        setPrevScans(prevScans);
      }
    } catch (e) {}
  };

  useEffect(() => {
    getData();
  }, []);

  const backgroundStyle = {
    flex: 1,
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
  };
  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar barStyle={isDarkMode ? "light-content" : "dark-content"} />
      <View
        style={[
          styles.header,
          { borderColor: isDarkMode ? "#3E3C3C" : "#E5E5E5" },
        ]}
      >
        <Image source={isDarkMode ? HeaderImageLight : HeaderImage} resizeMode="contain" />
      </View>

      <View style={styles.colContainer}>
        <Image source={isDarkMode ? QRIconLight : QRIcon} />
        <Text
          style={[
            styles.sectionTitle,
            { color: isDarkMode ? Colors.white : Colors.black },
          ]}
        >
          Preview Your App Design
        </Text>
        <Text
          style={[
            styles.sectionBody,
            { color: isDarkMode ? Colors.white : Colors.black },
          ]}
        >
          Scan QR code on your {"\n"} Apptile Dashboard to {"\n"} preview your
          app design here.
        </Text>
        <TouchableOpacity
          style={styles.scanButton}
          onPress={() => navigation.navigate("Scan")}
        >
          <Text style={styles.scanButtonText}>Scan QR</Text>
        </TouchableOpacity>
      </View>

      {prevScans && (
        <View
          style={[
            styles.recentAppContainer,
            { borderColor: isDarkMode ? "#3E3C3C" : "#E5E5E5" },
          ]}
        >
          <Text
            style={[
              styles.recentAppHeading,
              { color: isDarkMode ? Colors.white : Colors.black },
            ]}
          >
            Recent App Designs
          </Text>
          <ScrollView
            contentContainerStyle={{ paddingHorizontal: 12 }}
            scrollEnabled={prevScans?.length > 2}
          >
            {prevScans.map((prevScan) => (
              <TouchableOpacity
                key={prevScan.appId}
                style={[styles.recentAppButton]}
                onPress={() => Linking.openURL(prevScan.url).catch((err) => {})}
              >
                <Text style={styles.appName}>
                  {prevScan.appName || "Previous Scan"}
                </Text>
                <Text
                  style={[{ color: isDarkMode ? Colors.white : Colors.black }]}
                >
                  @ {prevScan.orgName || prevScan.appId}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 60,
    borderBottomWidth: 1,
    justifyContent: "center",
    paddingLeft: 24,
  },
  colContainer: {
    flex: 1,
    flexBasis: "auto",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "500",
  },
  sectionBody: {
    marginTop: 4,
    fontSize: 16,
    fontWeight: "300",
    textAlign: "center",
  },
  scanButton: {
    marginTop: 24,
    paddingVertical: 12,
    paddingHorizontal: 36,
    backgroundColor: "#1060E0",
    borderRadius: 32,
  },
  scanButtonText: {
    fontSize: 18,
    color: "#ffffff",
  },
  recentAppContainer: {
    maxHeight: 240,
    borderTopWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  recentAppHeading: {
    fontSize: 17,
    fontWeight: "500",
    marginBottom: 12,
    paddingHorizontal: 12,
  },
  recentAppButton: {
    marginTop: 4,
    borderRadius: 12,
    borderColor: "#1060E0",
    borderWidth: 2,
    padding: 12,
  },
  appName: {
    fontSize: 17,
    fontWeight: "500",
    color: "#1060E0",
    marginBottom: 8,
  },
});

export default HomeScreen;
