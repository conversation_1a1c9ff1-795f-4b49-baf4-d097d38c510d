import React, { useCallback } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  Linking,
  SafeAreaView,
  View,
  StatusBar,
  Dimensions,
  Image,
  Alert,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import QRCodeScanner from "react-native-qrcode-scanner";
import { RNCamera } from "react-native-camera";

import BackIcon from "../assets/back.icon.png";

import { NavigatorProps } from "../types";

const ScanScreen: React.FC<NavigatorProps> = ({ navigation }) => {
  const screenWidth = Dimensions.get("screen").width;

  const storeData = async (url: string) => {
    try {
      const urls = (await AsyncStorage.getItem("@prev_scan")) || "[]";
      await AsyncStorage.setItem(
        "@prev_scan",
        JSON.stringify(Array.from(new Set([...JSON.parse(urls), url])))
      );
    } catch (e) {}
  };

  const onSuccess = useCallback(async (e) => {
    Linking.canOpenURL(e.data).then(async (canOpen) => {
      if (canOpen) {
        try {
          navigation.popToTop();
          await storeData(e.data);
          Linking.openURL(e.data);
        } catch (err: any) {
          Alert.alert("Error!", "Something went wrong");
        }
      } else Alert.alert("Snap!", "Can not open this link");
    });
  }, []);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "black" }}>
      <StatusBar barStyle={"light-content"} />

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate("Home")}
        >
          <Image source={BackIcon} />
        </TouchableOpacity>
      </View>

      <QRCodeScanner
        containerStyle={{
          backgroundColor: "black",
          justifyContent: "center",
          alignItems: "center",
          marginBottom: 60,
        }}
        cameraContainerStyle={{
          borderColor: "#0091bc",
          borderWidth: 1,
          width: screenWidth - 62,
          height: screenWidth - 62,
          overflow: "hidden",
        }}
        cameraStyle={{
          width: screenWidth - 64,
          height: screenWidth - 64,
        }}
        topViewStyle={{ height: 0 }}
        bottomViewStyle={{ height: 0 }}
        onRead={onSuccess}
        flashMode={RNCamera.Constants.FlashMode.off}
        reactivate={true}
        reactivateTimeout={1000}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    padding: 10,
  },
  button: {
    width: 40,
    height: 40,
    borderRadius: 40,
    marginBottom: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#ffffff",
    textAlign: "center",
    textTransform: "uppercase",
  },
});

export default ScanScreen;
