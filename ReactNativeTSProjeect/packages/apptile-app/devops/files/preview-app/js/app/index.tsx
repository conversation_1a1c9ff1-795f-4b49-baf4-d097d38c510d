import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";

import HomeScreen from "./screens/Home";
import ScanScreen from "./screens/Scan";

const App = () => {
  const Navigator = createNativeStackNavigator();

  return (
    <NavigationContainer>
      <Navigator.Navigator>
        <Navigator.Screen
          name="Home"
          key="Home"
          component={HomeScreen}
          options={{ headerShown: false }}
        />
        <Navigator.Screen
          name="Scan"
          key="Scan"
          component={ScanScreen}
          options={{ headerShown: false }}
        />
      </Navigator.Navigator>
    </NavigationContainer>
  );
};

export default App;
