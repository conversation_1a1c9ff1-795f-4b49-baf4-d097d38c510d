package org.io.apptile.ApptilePreviewDemo;

import android.app.Application;

import android.os.Bundle;
import com.facebook.react.PackageList;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainReactPackage;

import com.zoontek.rnpermissions.RNPermissionsPackage;
import org.reactnative.camera.RNCameraPackage;

import java.util.Arrays;
import java.util.List;

public class MainActivity extends ScopedReactActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected String getMainComponentName() {
        return "ApptilePreviewApp";
    }

    @Override
    protected ReactNativeHost createReactNativeHost(Application app) {
        return new ReactNativeHost(app) {
            @Override
            public boolean getUseDeveloperSupport() {
                return false;
            }

            @Override
            protected List<ReactPackage> getPackages() {
                List<ReactPackage> packages = new PackageList(this).getPackages();
                packages.add(new RNGetValuesPackage());
                return packages;
            }

            @Override
            protected String getJSMainModuleName() {
                return "preview";
            }

            @Override
            protected String getBundleAssetName() {
                return "preview.android.bundle";
            }
        };
    }
}
