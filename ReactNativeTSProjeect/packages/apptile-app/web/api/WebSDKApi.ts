import {Api} from './Api';

export default class WebSDKApi {
  static baseURL = '/api/web-sdk';
  static getApiUrl() {
    return Api.API_SERVER + WebSDKApi.baseURL;
  }

  static async fetchCurrentLiveSDKBundle(): Promise<string | null> {
    // TODO(abhinav) this will not come from localstorage
    const partner = localStorage.getItem('apptile-partner-name');
    if (partner === 'shoplazza') {
      const cdnLink = await Api.get(WebSDKApi.getApiUrl() + `/live/${partner}`);
      return cdnLink?.data?.artifact?.cdnLink;
    } else {
      console.log('falling back to default');
      return null;
    }
  }

  static async fetchBundleWithArtifactId(artifactId: string): Promise<string | null> {
    try {
      const cdnLink = await Api.get(WebSDKApi.getApiUrl() + `/artifact/${artifactId}`);
      return cdnLink?.data?.cdnLink;
    } catch (error) {
      console.log('error while fetching artifact', error);
      return null;
    }
  }
}
