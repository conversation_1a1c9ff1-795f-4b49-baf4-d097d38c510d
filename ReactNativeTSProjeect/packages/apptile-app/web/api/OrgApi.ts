import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IOrganization} from './ApiTypes';

export default class OrgApi {
  static baseURL = '/api/orgs';
  static getApiUrl() {
    return Api.API_SERVER + OrgApi.baseURL;
  }

  static fetchOrgs(): AxiosPromise<IOrganization[]> {
    return Api.get(OrgApi.getApiUrl());
  }

  static createOrgsApps(
    orgId: string | undefined,
    name: string,
    baseBlueprintId: string,
  ): AxiosPromise<IOrganization[]> {
    return Api.post(OrgApi.getApiUrl() + `/${orgId}/app`, {
      name,
      baseBlueprintId,
    });
  }

  static deleteApp(orgId: string, appId: string): AxiosPromise<any> {
    return Api.delete(OrgApi.getApiUrl() + `/${orgId}/app/${appId}`);
  }

  static platformInit(orgId: string): AxiosPromise<any> {
    return Api.get(OrgApi.getApiUrl() + `/${orgId}/init`);
  }

  static updateOnboardingStatus(orgId: string, isOnboarded: boolean): AxiosPromise<any> {
    return Api.put(OrgApi.getApiUrl() + `/${orgId}`, {
      isOnboarded,
    });
  }

  static createUserOrgMapping(orgId: string, userId: string): AxiosPromise<any> {
    // Get the token from localStorage or wherever it's stored after login
    const token = localStorage.getItem('authToken'); // Adjust this based on how you store the token

    return Api.post(
      OrgApi.getApiUrl() + `/create-userorg-mapping/${orgId}/${userId}`,
      {},
      {
        headers: {
          Authorization: token ? `Bearer ${token}` : '',
        },
      },
    );
  }
}
