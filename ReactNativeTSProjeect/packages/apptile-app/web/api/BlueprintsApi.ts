import {AxiosPromise} from 'axios';
import _ from 'lodash';
import {Api} from './Api';
import {ITileSaveInterface, ITilesListResponse, ITileUpdateInterface} from './ApiTypes';

export default class BlueprintsApi {
  static baseURL = '/api/blueprints';
  static getApiUrl() {
    return Api.API_SERVER + BlueprintsApi.baseURL;
  }

  static createBlueprint(blueprintSaveRecord: ITileSaveInterface): AxiosPromise<any> {
    return Api.post(BlueprintsApi.getApiUrl(), blueprintSaveRecord, {withCredentials: true});
  }

  static getBlueprint(uuid: string): AxiosPromise<any> {
    return Api.get(BlueprintsApi.getApiUrl() + `/${uuid}`);
  }
  static getAllBlueprints(tags: string[], offset = 0, limit = 50, sourcePlatformType?:string): AxiosPromise<ITilesListResponse> {
    return Api.get(BlueprintsApi.getApiUrl(), {params: {tags: _.each(tags, _.toUpper).join(','), offset, limit, sourcePlatformType}});
  }

  static updateBlueprintMeta(blueprintUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {id: uuid, ...rest} = blueprintUpdateRecord;
    return Api.put(BlueprintsApi.getApiUrl() + `/${uuid}`, rest, {withCredentials: true});
  }

  static updateBlueprint(blueprintUpdateRecord: ITileUpdateInterface): AxiosPromise<any> {
    const {id: uuid} = blueprintUpdateRecord;
    return Api.post(BlueprintsApi.getApiUrl() + `/${uuid}/version`, blueprintUpdateRecord, {withCredentials: true});
  }
}
