import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';

export default class AssetsApi {
  static baseURL = '/api/asset-manager';
  static getApiUrl() {
    return Api.API_SERVER + AssetsApi.baseURL;
  }

  static fetchAppAssets(appId: string, page: number, limit: number): AxiosPromise<AppConfigResponse> {
    return Api.get(AssetsApi.getApiUrl() + `/apps/${appId}/assets?page=${page}&limit=${limit}`);
  }

  static persistAppAsset(fileKey: string, appId: string): AxiosPromise<AppConfigResponse> {
    return Api.post(AssetsApi.getApiUrl() + `/assets/${appId}`, {fileKey});
  }

  static getIconsForCurrentApp(appId: string): AxiosPromise<AppConfigResponse> {
    return Api.get(AssetsApi.getApiUrl() + `/icons/${appId}`);
  }

  static addIconToCurrentApp(appId: string, payload: Record<string, string>): AxiosPromise<AppConfigResponse> {
    return Api.post(AssetsApi.getApiUrl() + `/icons/${appId}`, payload);
  }
}
