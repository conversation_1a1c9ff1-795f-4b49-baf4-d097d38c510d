import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IBrandResponse} from './ApiTypes';

export default class BrandApi {
  static baseURL = '/shopify-shop-manager/brand';
  static getApiUrl() {
    return Api.API_SERVER + BrandApi.baseURL;
  }

  static fetchBrandData(domain: string): AxiosPromise<IBrandResponse> {
    return Api.get(BrandApi.getApiUrl() + `/${domain}`);
  }

  static currencyConvertSalesData(domain: string): AxiosPromise<IBrandResponse> {
    return Api.get(BrandApi.getApiUrl() + `/currencyConvertSalesAmount/${domain}`);
  }
}
