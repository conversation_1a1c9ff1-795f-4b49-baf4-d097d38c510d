import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {
  IAppOnlyDiscountAttributes,
  IAppOnlyDiscountCreateAttributes,
  IApptileCartUpsellAttributes,
  IApptileCartUpsellPayload,
} from './ApiTypes';
export default class ApptileCartUpsellApi {
  static baseURL = '/apptile-shopify-discount-manager/api/v1/cart-upsell';
  static getApiUrl() {
    return Api.API_SERVER + ApptileCartUpsellApi.baseURL;
  }

  static syncAllDiscounts(appId: string): AxiosPromise<IApptileCartUpsellAttributes> {
    return Api.get(ApptileCartUpsellApi.getApiUrl() + `/syncDiscounts`, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static getCartUpsellDiscount(appId: string): AxiosPromise<IApptileCartUpsellAttributes> {
    return Api.get(ApptileCartUpsellApi.getApiUrl() + `/getDiscounts`, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static getCartUpsellDiscountbyId(appId: string, discountId: string): AxiosPromise<IApptileCartUpsellAttributes> {
    return Api.get(ApptileCartUpsellApi.getApiUrl() + `/${discountId}`, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static createCartUpsellDiscount(appId: string, createPayload: any): AxiosPromise<IApptileCartUpsellAttributes> {
    return Api.post(ApptileCartUpsellApi.getApiUrl() + `/`, createPayload, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static updateCartUpsellDiscount(
    discountId: string,
    updatePayload: IApptileCartUpsellPayload,
    appId: string,
  ): AxiosPromise<IApptileCartUpsellAttributes> {
    return Api.patch(ApptileCartUpsellApi.getApiUrl() + `/${discountId}`, updatePayload, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static deactivateDiscountById(discountId: string, appId: string) {
    return Api.delete(ApptileCartUpsellApi.getApiUrl() + `/${discountId}`, {
      headers: {
        'x-shopify-app-id': appId,
      },
    });
  }

  static activateDiscountById(discountId: string, appId: string) {
    return Api.patch(
      ApptileCartUpsellApi.getApiUrl() + `/activate/${discountId}`,
      {},
      {
        headers: {
          'x-shopify-app-id': appId,
        },
      },
    );
  }
}
