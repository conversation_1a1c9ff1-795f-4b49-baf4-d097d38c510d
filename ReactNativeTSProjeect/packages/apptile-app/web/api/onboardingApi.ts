import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {IOnboardingResponse} from './ApiTypes';

export default class OnboardingApi {
  static baseURL = '/api/onboarding';
  static getApiUrl() {
    return Api.API_SERVER + OnboardingApi.baseURL;
  }

  static fetchOnboardingData(appId: string | number): AxiosPromise<IOnboardingResponse> {
    return Api.get(OnboardingApi.getApiUrl() + `/${appId}`);
  }

  static saveOnboardingData(appId: string, metadata: Record<string, any>): AxiosPromise<IOnboardingResponse> {
    return Api.post(OnboardingApi.getApiUrl() + `/${appId}`, metadata);
  }
}
