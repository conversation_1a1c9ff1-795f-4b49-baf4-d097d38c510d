import {WEB_API_SERVER_ENDPOINT} from '@/root/.env.json';
import {Api} from '@/root/web/api/Api';

export class PushNotificationApi {
  static baseURL = '/user-communication';
  static shopifyShopManager = '/shopify-shop-manager';

  static getCommunicationUrl() {
    return WEB_API_SERVER_ENDPOINT + PushNotificationApi.baseURL;
  }

  static getShopManagerUrl() {
    return WEB_API_SERVER_ENDPOINT + PushNotificationApi.shopifyShopManager;
  }

  static getManualRecord<T>(notificationId: string, appId: string) {
    return Api.get<T>(PushNotificationApi.getCommunicationUrl() + `/notifications/manual/${notificationId}`, {
      headers: {'X-App-Id': appId},
    });
  }

  static createManualRecord<T>(appId: string, createEntry: Record<string, any>) {
    return Api.post<T>(PushNotificationApi.getCommunicationUrl() + '/notifications/manual', createEntry, {
      headers: {'X-App-Id': appId},
    });
  }

  static updateManualRecord<T>(notificationId: string, appId: string, updatedEntry: Record<string, any>) {
    return Api.put<T>(
      PushNotificationApi.getCommunicationUrl() + `/notifications/manual/${notificationId}`,
      updatedEntry,
      {
        headers: {'X-App-Id': appId},
      },
    );
  }

  static deleteManualRecord<T>(notificationId: string, appId: string) {
    return Api.delete<T>(PushNotificationApi.getCommunicationUrl() + `/notifications/manual/${notificationId}`, {
      headers: {'X-App-Id': appId},
    });
  }

  static stopLiveRecurringCampaign<T>(notificationId: string, appId: string) {
    return Api.put<T>(
      PushNotificationApi.getCommunicationUrl() + `/notifications/manual/${notificationId}/stop`,
      null,
      {
        headers: {'X-App-Id': appId},
      },
    );
  }

  static listManualRecord<T>(appId: string, limit: number, offset: number, status: string, deliveryType?: string) {
    return Api.get<T>(PushNotificationApi.getCommunicationUrl() + `/notifications/manual`, {
      headers: {'X-App-Id': appId},
      params: {
        appId: appId,
        limit,
        offset,
        status,
        deliveryType,
        orderBy: '-scheduledAt',
      },
    });
  }

  static shopifyUserSegment<T>(appId: string) {
    let config = appId ? {headers: {'X-App-Id': appId}} : {};
    return Api.get<T>(PushNotificationApi.getShopManagerUrl() + '/user-segments', config);
  }

  static listAutomatedRecord<T>(appId: string) {
    return Api.get<T>(PushNotificationApi.getCommunicationUrl() + '/notifications/automated', {
      params: {
        appId: appId,
      },
      headers: {'X-App-Id': appId},
    });
  }

  static getAutomatedRecord<T>(notificationId: string, appId: string) {
    return Api.get<T>(PushNotificationApi.getCommunicationUrl() + `/notifications/automated/${notificationId}`, {
      headers: {'X-App-Id': appId},
    });
  }

  static updateAutomatedRecord<T>(notificationId: string, appId: string, updatedEntry: Record<string, any>) {
    return Api.put<T>(
      PushNotificationApi.getCommunicationUrl() + `/notifications/automated/${notificationId}`,
      updatedEntry,
      {
        headers: {'X-App-Id': appId},
      },
    );
  }

  static createAutomatedRecord<T>(appId: string) {
    return Api.put<T>(
      PushNotificationApi.getCommunicationUrl() + `/notifications/automated/apps/${appId}/refresh`,
      {},
      {
        headers: {'X-App-Id': appId},
      },
    );
  }
}
