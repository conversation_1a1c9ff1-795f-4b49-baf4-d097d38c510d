import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';

export default class LiveStreamAnalyticsApi {
  static analyticsManagerBaseURL = `${WEB_API_SERVER_ENDPOINT}/analytics-manager`;
  static basePath = '/api/analytics/stream';

  static getStreamAddToCartEvent(appId: string, streamId: string): AxiosPromise {
    return Api.get(this.analyticsManagerBaseURL + this.basePath + `/${streamId}/add-to-cart`, {
      headers: {'x-shopify-app-id': appId},
    });
  }

  static getStreamViewsEvent(streamId: string, schemaName: string): AxiosPromise {
    return Api.post(this.analyticsManagerBaseURL + this.basePath + `/${streamId}/views`, {
      schemaName: schemaName,
    });
  }

  static getStreamSalesEvent(appId: string, streamId: string): AxiosPromise {
    return Api.get(this.analyticsManagerBaseURL + this.basePath + `/${streamId}/sales`, {
      headers: {'x-shopify-app-id': appId},
    });
  }
}
