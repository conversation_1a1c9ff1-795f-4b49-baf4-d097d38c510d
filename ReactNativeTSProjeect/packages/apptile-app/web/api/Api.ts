import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import {createApp} from '@shopify/app-bridge';
import {getSessionToken} from '@shopify/app-bridge-utils';
import {Redirect} from '@shopify/app-bridge/actions';

import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';

export const currentAppConfigVersion = '0.17.0';

const axiosInstance = axios.create({withCredentials: true});
let appbridgeInstance: any;

const getAppbridgeInstance = () => {
  //   TODO: Need to discuss it. https://shopify.dev/apps/auth/oauth/session-tokens/axios
  const appHost = window.appHost;
  if (!appHost) return null;

  const apiKey = process.env.REACT_APP_SHOPIFY_API_KEY;

  if (!apiKey) return null;

  if (!appbridgeInstance) {
    appbridgeInstance = createApp({apiKey: api<PERSON><PERSON>, host: appHost, forceRedirect: false});
  }
  return appbridgeInstance;
};

axiosInstance.interceptors.request.use(async function (config: any) {
  const app = getAppbridgeInstance();
  if (app) {
    logger.info('appBridgeinstance is now available');
    const token = await getSessionToken(app);
    config.headers['Authorization'] = `Bearer ${token}`;
    return config;
  } else {
    return config;
  }
});

axiosInstance.interceptors.response.use(
  response => response,
  function (error) {
    const headers = error?.response?.headers;
    if (headers && headers['x-shopify-api-request-failure-reauthorize'] === '1') {
      const authUrlHeader = headers['x-shopify-api-request-failure-reauthorize-url'];

      const app = getAppbridgeInstance();
      if (app) {
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.REMOTE, authUrlHeader || `/`);
      } else {
        logger.error('appBridgeinstance not available, unable to redirect');
      }
      return null;
    } else {
      return Promise.reject(error);
    }
  },
);

export class Api {
  static API_SERVER: string = WEB_API_SERVER_ENDPOINT;

  static get<T>(url: string, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.get<T>(url, config);
  }
  static post<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.post<T>(url, data, config);
  }
  static put<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.put<T>(url, data, config);
  }

  static patch<T>(url: string, data?: unknown, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.patch<T>(url, data, config);
  }

  static delete<T>(url: string, config: Partial<AxiosRequestConfig> = {}): Promise<AxiosResponse<T>> {
    return axiosInstance.delete<T>(url, config);
  }
}
