import {AxiosPromise} from 'axios';
import {Api} from './Api';

interface GenerateAppNameResponse {
  appName: string;
}

export default class AppNameGeneratorApi {
  static baseURL = '/api/generate-app-name';
  static getApiUrl() {
    return Api.API_SERVER + AppNameGeneratorApi.baseURL;
  }

  static generateAppName(prompt: string): AxiosPromise<GenerateAppNameResponse> {
    return Api.post(AppNameGeneratorApi.getApiUrl(), {
      prompt,
    });
  }
}
