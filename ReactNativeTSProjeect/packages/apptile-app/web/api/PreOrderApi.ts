import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';

export default class PreOrderApi {
  static baseURL = '/shopify-shop-manager/waitlist';
  static getApiUrl() {
    return Api.API_SERVER + PreOrderApi.baseURL;
  }

  static getAllSellingPlans(appId?: string, cursor?: string, cursorVariant?: string): AxiosPromise<AppConfigResponse> {
    return Api.get(
      PreOrderApi.getApiUrl() +
        `/get-selling-plan-group?${cursor ? `afterProduct=${cursor}` : ''}&${
          cursorVariant ? `afterVariant=${cursorVariant}` : ''
        }`,
      {
        headers: {
          'x-shopify-app-id': appId,
        },
      },
    );
  }
  static createSellingPlan(appId: string): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/create-selling-plan-group`, {
      input: {
        name: 'apptile-pre-order'
      },
      resources: {},
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
  static addProductsToSellingPlan(appId: string, SellingPlanGroupId: string, productsIds: string[], productIdMap: {[key: string]: string}): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/add-products-to-selling-plan-group`, {
      id: SellingPlanGroupId,
      productIds: productsIds,
      productIdMap: productIdMap,
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
  static removeProductsFromSellingPlan(
    appId: string,
    SellingPlanGroupId: string,
    productsIds: string[],
    productIdMap: {[key: string]: string},
  ): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/remove-products-from-selling-plan-group`, {
      id: SellingPlanGroupId,
      productIds: productsIds,
      productIdMap: productIdMap,
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
  static addVariantsToSellingPlan(appId: string, SellingPlanGroupId: string, variantsIds: string[], productIdMap: {[key: string]: string}): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/add-product-variants-to-selling-plan-group`, {
      id: SellingPlanGroupId,
      productVariantIds: variantsIds,
      productIdMap: productIdMap,
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
  static removeVariantsFromSellingPlan(
    appId: string,
    SellingPlanGroupId: string,
    variantsIds: string[],
    productIdMap: {[key: string]: string},
  ): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/remove-product-variants-from-selling-plan-group`, {
      id: SellingPlanGroupId,
      productVariantIds: variantsIds,
      productIdMap: productIdMap,
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
  static deleteSellingPlan(appId: string, SellingPlanGroupId: string): AxiosPromise<AppConfigResponse> {
    return Api.post(PreOrderApi.getApiUrl() + `/delete-selling-plan-group`, {
      id: SellingPlanGroupId,
    },
    {
      headers: {
        'x-shopify-app-id': appId,
      },
    },);
  }
}
