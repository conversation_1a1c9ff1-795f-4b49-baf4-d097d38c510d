import {AxiosPromise} from 'axios';
import {Api} from './Api';
import { useParams } from 'react-router';

export default class AnalyticsApi {
  static googleBaseURL = '/apptile-google-service-manager';
  static baseURL = '/api/analytics/';
  static getApiUrl(type = 'google') {
    return Api.API_SERVER + (type == 'google' ? AnalyticsApi.googleBaseURL : AnalyticsApi.baseURL);
  }

  static getReport(appId: string, analyticsQuery: any): AxiosPromise {
    return Api.post(AnalyticsApi.getApiUrl() + `/analytics/runReport`, analyticsQuery, {
      headers: {'X-App-Id': appId},
    });
  }

  static getDashboardUrl(appId: string): AxiosPromise {
    return Api.get(AnalyticsApi.getApiUrl('apptile'), {
      headers: {'X-App-Id': appId},
    });
  }
  
}
