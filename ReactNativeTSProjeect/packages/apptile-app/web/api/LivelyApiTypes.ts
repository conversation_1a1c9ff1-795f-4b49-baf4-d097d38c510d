export interface ILiveStream {
  _id: string;
  user_id: string;
  user_name: string;
  company_id: string;
  company_name: string;
  is_test: boolean;
  start_time: string;
  start_time_unix: number;
  scheduled_end_time: string;
  scheduled_end_time_unix: number;
  streaming_id: string;
  streaming_name: string;
  streaming_description: string;
  streaming_thumbnail: string;
  streaming_status: number;
  room_id: string;
  product_info: ILiveStreamProductInfo[];
  stream_to: any[];
  fb_auth_id: any;
  yt_auth_id: any;
  createdAt: string;
  updatedAt: string;
  __v: number;
  actual_start_time: string;
  actual_start_time_unix: number;
  recording: ILiveStreamRecording;
  recording_started: boolean;
  join_as: string[];
}

export interface ILiveStreamProductInfo {
  product_id: string;
  store_product_id: string;
  product_url: string;
  product_name: string;
  product_thumbnail: string;
  product_description: string;
  product_price: number;
}

export interface ILiveStreamRecording {
  zego_task_id: string;
  file_info: ILiveStreamFileInfo;
}

export interface ILiveStreamFileInfo {
  begin_timestamp: number;
  duration: number;
  file_id: string;
  file_size: number;
  file_url: string;
  media_track_type: number;
  output_file_format: string;
  resolution_height: number;
  resolution_width: number;
  status: number;
  stream_id: string;
  user_id: string;
  user_name: string;
  video_id: string;
}
