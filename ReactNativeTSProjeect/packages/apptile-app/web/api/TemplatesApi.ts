import {AppConfigResponse} from 'apptile-server';
import {AxiosPromise} from 'axios';
import {Api} from './Api';

import {ModuleRecord} from 'apptile-core';

export type ModuleChangeType = 'major' | 'minor';

export default class TemplatesApi {
  static baseURL = '/api';
  static getApiUrl() {
    return Api.API_SERVER + TemplatesApi.baseURL;
  }

  static getTemplate(uuid: string): AxiosPromise<AppConfigResponse> {
    return Api.get(TemplatesApi.getApiUrl() + `/template/${uuid}`);
  }

  static createModuleTemplate(orgId: string, moduleRecord: ModuleRecord): AxiosPromise<AppConfigResponse> {
    return Api.post(
      TemplatesApi.getApiUrl() + `/orgs/${orgId}/template`,
      {
        name: moduleRecord.moduleName,
        type: 'modules',
        uuid: moduleRecord.moduleUUID,
      },
      {withCredentials: true},
    );
  }

  static saveModuleTemplate(
    moduleUUID: string,
    moduleData: any,
    changeType: ModuleChangeType = 'minor',
  ): AxiosPromise<AppConfigResponse> {
    return Api.post(
      TemplatesApi.getApiUrl() + `/template/${moduleUUID}/save`,
      {
        data: moduleData,
        changeType,
      },
      {withCredentials: true},
    );
  }
}
