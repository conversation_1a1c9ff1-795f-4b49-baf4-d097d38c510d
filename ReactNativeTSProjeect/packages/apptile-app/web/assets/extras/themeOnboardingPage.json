{"modules": [{"data": {"moduleUUID": "3101ff87-d7f1-4c6c-969d-b9b3fc188f16", "moduleSaveId": {"$jsan": "u"}, "moduleName": "Add to Cart/Subscribe Buttons", "inputs": ["shopify", "PDP"], "outputs": [], "queries": [], "events": ["onEvent1", "onEvent2", "onEvent3", "onEvent4", "onEvent5", "onEvent6"], "tags": [], "persistInputBindings": true, "inputBindings": {"data": {"shopify": "{{shopify}}", "PDP": "{{PDP}}", "Input3": ""}, "__serializedType__": "ImmutableMap"}, "defaultEventHandlers": {"data": [{"data": {"eventHandler": {"data": {"label": "onEvent1", "type": "action", "method": "triggerAction", "pluginId": "shopify", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "increaseCartLineItemQuantity", "params": {"data": {"merchandiseId": "?event.merchandiseId", "quantity": "?event.quantity", "syncWithShopify": "?event.syncWithShopify"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent1", "type": "query", "method": "triggerToast", "pluginId": "QueryProduct", "isGlobalPlugin": false, "screenName": "", "prop": "", "value": "Product added to cart", "params": {"data": {"type": "?event.type", "placement": "?event.placement", "duration": "?event.duration", "offset": "?event.offset"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent2", "type": "action", "method": "triggerAction", "pluginId": "shopify", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "increaseCartLineItemQuantity", "params": {"data": {"merchandiseId": "?event.merchandiseId", "quantity": "?event.quantity", "syncWithShopify": "?event.syncWithShopify"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent2", "type": "query", "method": "triggerToast", "pluginId": null, "isGlobalPlugin": false, "screenName": "", "prop": "", "value": "Product added to cart", "params": {"data": {"type": "?event.type", "placement": "?event.placement", "duration": "?event.duration", "offset": "?event.offset"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent3", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "<PERSON><PERSON>", "prop": "", "value": null, "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent4", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "ProductBuy", "prop": "", "value": null, "params": {"data": {"isSubscriptionPurchase": "?event.isSubscriptionPurchase", "productHandle": "?event.productHandle", "activeVariantId": "?event.activeVariantId"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent5", "type": "page", "method": "navigate", "pluginId": "CreateCheckoutQueryV1", "isGlobalPlugin": false, "screenName": "ShopifyCheckout", "prop": "", "value": null, "params": {"data": {"checkoutId": "?event.checkoutId"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}, {"data": {"eventHandler": {"data": {"label": "onEvent6", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "<PERSON><PERSON>", "prop": "", "value": null, "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": "?event.condition"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": false}, "__serializedType__": "ImmutableMap"}], "__serializedType__": "ImmutableList"}, "editors": {"data": {}, "__serializedType__": "ImmutableOrderedMap"}, "styleEditors": {"data": {}, "__serializedType__": "ImmutableOrderedMap"}, "moduleConfig": {"data": {"CtaBtnContainer": {"data": {"id": "CtaBtnContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingTop": 8, "marginRight": 0, "borderBottomWidth": 0, "marginLeft": 0, "backgroundColor": "white", "marginBottom": 0, "paddingBottom": 8}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": "{{!PDP.value.product}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "", "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "85", "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "", "position": "absolute", "top": "", "bottom": "0", "left": "0", "right": "0"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OutOfStockCont": {"data": {"id": "OutOfStockCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.onPrimary", "borderColor": "#E5E5E5", "borderTopWidth": 1}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "CtaBtnContainer", "hidden": "{{PDP.value.activeVariant?.availableForSale}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": "", "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "80", "justifyContent": "center", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "subscriptionButtonCont": {"data": {"id": "subscriptionButtonCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"marginTop": 0, "paddingBottom": 4, "paddingTop": 4}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "CtaBtnContainer", "hidden": "{{PDP.value.product && PDP.value.activeVariant ? !_.find(PDP.value.product?._raw?.variants, (v)=>v?.id==PDP.value.activeVariant?.id)?.sellingPlanAllocations?.length>0:false}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "space-around", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OneTimeBuyCont": {"data": {"id": "OneTimeBuyCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "CtaBtnContainer", "hidden": "{{PDP.value.product && PDP.value.activeVariant ? _.find(PDP.value.product?._raw?.variants, (v)=>v?.id==PDP.value.activeVariant?.id)?.sellingPlanAllocations?.length>0:false}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OutOfStockText": {"data": {"id": "OutOfStockText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"horizontalAlign": "left", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "verticalAlign": "center", "value": "Out of Stock", "style": {"data": {"typography": {"data": {"fontWeight": "700", "fontStyle": "normal", "fontSize": 32, "lineHeight": 38, "textTransform": "none", "textDecorationLine": "none", "inherit": "tile.text.font", "letterSpacing": 0}, "__serializedType__": "ImmutableMap"}, "color": "#C55652", "paddingTop": 4, "paddingBottom": 8}, "__serializedType__": "ImmutableMap"}, "isLoading": false, "numLines": 1, "overflowType": "hidden"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "OutOfStockCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OneTimeAddToCartBtn1": {"data": {"id": "OneTimeAddToCartBtn1", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"horizontalAlign": "center", "onTap": "", "analytics": {"data": {"params": {"data": {"variantId": "{{PDP.value.activeVariant.id}}", "productType": "{{PDP.value.product.productType}}", "price": "{{PDP.value.activeVariant.price.amount}}", "productId": "{{PDP.value.product.id}}", "currency": "{{PDP.value.activeVariant.price.currencyCode}}", "variantTitle": "{{PDP.value.activeVariant.title}}", "title": "{{PDP.value.product.title}}"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "verticalAlign": "center", "value": "ADD TO CART", "style": {"data": {"_profile": "outline", "borderColor": "colors.primary", "borderWidth": "1", "borderRadius": "0", "margin": "0", "marginLeft": "4%"}, "__serializedType__": "ImmutableMap"}, "submit": "", "hapticMethod": "tap", "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent1", "params": {"data": {"merchandiseId": "{{PDP.value.activeVariant.id}}", "quantity": "{{1}}", "syncWithShopify": "{{false}}", "type": "info", "placement": "bottom", "duration": "1000", "offset": "150"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "enableHaptics": true, "loading": "", "disabled": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "subscriptionButtonCont", "hidden": "{{shopify.value.currentCartLineItems && PDP.value.activeVariant.id ?(_.some(shopify.value.currentCartLineItems, (v)=>v.merchandiseId==PDP.value.activeVariant.id) ):false}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": "", "top": "", "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"data": {"enabled": true, "type": "track", "name": "addToCart", "params": {"data": {"variantId": "{{PDP.value.activeVariant.id}}", "productType": "{{PDP.value.product.productType}}", "price": "{{PDP.value.activeVariant.price.amount}}", "productId": "{{PDP.value.product.id}}", "quantity": "1", "currency": "{{PDP.value.activeVariant.price.currencyCode}}", "variantTitle": "{{PDP.value.activeVariant.title}}", "title": "{{PDP.value.product.title}}"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 16}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "GoToCart": {"data": {"id": "GoToCart", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"value": "GO TO CART", "onTap": "", "loading": "", "disabled": "", "enableHaptics": true, "hapticMethod": "tap", "style": {"data": {"_profile": "outline", "typography": {"data": {"_inherit": "typography.subHeading"}, "__serializedType__": "ImmutableMap"}, "borderColor": "colors.secondary", "color": "colors.secondary", "marginLeft": "4%"}, "__serializedType__": "ImmutableMap"}, "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent6", "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "subscriptionButtonCont", "hidden": "{{shopify.value.currentCartLineItems && PDP.value.activeVariant.id ?!(_.some(shopify.value.currentCartLineItems, (v)=>v.merchandiseId===PDP.value.activeVariant.id) ):true}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Subscribe": {"data": {"id": "Subscribe", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"horizontalAlign": "", "onTap": "", "verticalAlign": "", "value": "SUBSCRIBE", "style": {"data": {"_profile": "primary", "marginRight": "4%"}, "__serializedType__": "ImmutableMap"}, "hapticMethod": "tap", "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent4", "params": {"data": {"isSubscriptionPurchase": "{{true}}", "productHandle": "{{PDP.value.product.handle}}", "activeVariantId": "{{PDP.value.activeVariant.id}}"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "enableHaptics": true, "loading": "", "disabled": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "subscriptionButtonCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "CreateCheckoutQueryV1": {"data": {"id": "CreateCheckoutQueryV1", "type": "query", "subtype": "QueryPlugin", "config": {"data": {"isPaginated": false, "onSuccess": "", "datasource": "shopify", "pageinationMeta": null, "runWhenModelUpdates": false, "onError": "", "data": null, "errors": null, "inputVariables": {"data": {"lineItems": "{{[\n{\n    quantity: 1,\n    variationId: PDP.value.activeVariant.id,\n  }]}}"}, "__serializedType__": "ImmutableMap"}, "hasError": false, "metadata": null, "rawData": null, "executeQuery": "", "queryName": "CreateCheckoutV1", "cachePolicy": "no-cache", "timestamp": 0, "runWhenPageLoads": false, "events": {"data": [{"data": {"label": "onSuccess", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent5", "params": {"data": {"checkoutId": "{{CreateCheckoutQueryV1.data.id}}"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "hasNextPage": false, "loading": false, "runOnPageFocus": false, "prevRunVariables": null}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "OneTimeBuyCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OneTimeGoToCart": {"data": {"id": "OneTimeGoToCart", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"value": "GO TO CART", "onTap": "", "loading": "", "disabled": "", "enableHaptics": true, "hapticMethod": "tap", "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent3", "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "style": {"data": {"marginLeft": "4%", "color": "colors.secondary", "_profile": "outline", "borderColor": "colors.secondary"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "OneTimeBuyCont", "hidden": "{{shopify.value.currentCartLineItems && PDP.value.activeVariant.id ?!(_.some(shopify.value.currentCartLineItems, (v)=>v.merchandiseId===PDP.value.activeVariant.id) ):true}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OneTimeAddToCart": {"data": {"id": "OneTimeAddToCart", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"value": "ADD TO CART", "onTap": "", "loading": "", "disabled": "", "enableHaptics": "", "hapticMethod": "", "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent2", "params": {"data": {"merchandiseId": "{{PDP.value.activeVariant.id}}", "quantity": "{{1}}", "syncWithShopify": "{{false}}", "type": "success", "placement": "bottom", "duration": "1000", "offset": "150"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "style": {"data": {"marginLeft": "4%", "_profile": "outline"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "OneTimeBuyCont", "hidden": "{{shopify.value.currentCartLineItems && PDP.value.activeVariant.id ?(_.some(shopify.value.currentCartLineItems, (v)=>v.merchandiseId==PDP.value.activeVariant.id) ):false}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "OneTimeBuyBtn": {"data": {"id": "OneTimeBuyBtn", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"horizontalAlign": "center", "onTap": "", "verticalAlign": "center", "value": "BUY NOW", "style": {"data": {"marginLeft": "4%", "_profile": "primary"}, "__serializedType__": "ImmutableMap"}, "hapticMethod": "tap", "events": {"data": [{"data": {"label": "onTap", "type": "query", "method": "execute<PERSON>uery", "pluginId": "CreateCheckoutQueryV1", "isGlobalPlugin": false, "screenName": "", "prop": "", "value": null, "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "enableHaptics": true, "loading": "{{CreateCheckoutQueryV1.loading}}", "disabled": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "OneTimeBuyCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "shopify": {"data": {"id": "shopify", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile1.shopify}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PDP": {"data": {"id": "PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile1.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Input3": {"data": {"id": "Input3", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile1.Input3}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}}, "__serializedType__": "ImmutableOrderedMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 11}, {"data": {"moduleUUID": "1b5e6cb5-1883-47d2-8f01-108efb6f9526", "moduleSaveId": {"$jsan": "u"}, "moduleName": "Product Title 03 (without variant title)", "inputs": ["PDP"], "outputs": [], "queries": [], "events": [], "tags": ["PDP"], "persistInputBindings": true, "inputBindings": {"data": {"PDP": "{{PDP}}", "Input2": "{{PDP.activeVariant}}"}, "__serializedType__": "ImmutableMap"}, "defaultEventHandlers": {"data": [], "__serializedType__": "ImmutableList"}, "editors": {"data": {}, "__serializedType__": "ImmutableOrderedMap"}, "styleEditors": {"data": {"TitleText.config.horizontalAlign": {"data": {"selector": ["TitleText", "config", "horizontalAlign"], "value": {"$jsan": "u"}, "label": "Alignment", "editorType": {"label": "", "type": "alignmentEditor", "defaultValue": {"$jsan": "u"}, "props": {"label": "Alignment", "options": [{"icon": "alpha-a", "value": "auto"}, {"icon": "format-align-left", "value": "left"}, {"icon": "format-align-center", "value": "center"}, {"icon": "format-align-right", "value": "right"}, {"icon": "format-align-justify", "value": "justify"}]}, "name": "horizontalAlign"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "TitleText.config.style.typography.fontSize": {"data": {"selector": ["TitleText", "config", "style", "typography", "fontSize"], "value": {"$jsan": "u"}, "label": "Text size", "editorType": {"label": "", "type": "rangeSliderInput", "defaultValue": {"$jsan": "u"}, "props": {"minRange": "8", "maxRange": "32", "steps": 1, "label": "Text size"}, "name": "fontSize"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "TitleText.config.style.color": {"data": {"selector": ["TitleText", "config", "style", "color"], "value": {"$jsan": "u"}, "label": "Color", "editorType": {"label": "", "type": "colorInput", "defaultValue": {"$jsan": "u"}, "props": {"disableBinding": true, "label": "Color"}, "name": "color"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "ProductTitleTile.config.style.backgroundColor": {"data": {"selector": ["ProductTitleTile", "config", "style", "backgroundColor"], "value": {"$jsan": "u"}, "label": "Background color", "editorType": {"label": "", "type": "colorInput", "defaultValue": {"$jsan": "u"}, "props": {"disableBinding": true, "label": "Background color"}, "name": "backgroundColor"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}}, "__serializedType__": "ImmutableOrderedMap"}, "moduleConfig": {"data": {"ProductTitleTile": {"data": {"id": "ProductTitleTile", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "TitleText": {"data": {"id": "TitleText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "{{PDP.value.product?.title}}", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "numLines": 1, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.heading"}, "__serializedType__": "ImmutableMap"}, "paddingTop": "8", "paddingRight": "12", "paddingBottom": "8", "paddingLeft": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductTitleTile", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PDP": {"data": {"id": "PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile2.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Input2": {"data": {"id": "Input2", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile2.Input2}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}}, "__serializedType__": "ImmutableOrderedMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 11}, {"data": {"moduleUUID": "8068152d-5823-4820-bfd3-f6a3f8972364", "moduleSaveId": {"$jsan": "u"}, "moduleName": "Product Price 01", "inputs": ["PDP"], "outputs": [], "queries": [], "events": [], "tags": ["PDP"], "persistInputBindings": true, "inputBindings": {"data": {"PDP": "{{PDP}}"}, "__serializedType__": "ImmutableMap"}, "defaultEventHandlers": {"data": [], "__serializedType__": "ImmutableList"}, "editors": {"data": {}, "__serializedType__": "ImmutableOrderedMap"}, "styleEditors": {"data": {"tile3.Section1": {"data": {"selector": ["tile3", "Section1"], "value": {"$jsan": "u"}, "label": "Sale Price", "editorType": {"type": "editorSectionHeader", "props": {"label": "Sale Price"}, "name": "Section1"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "salePriceText.config.style.typography.fontSize": {"data": {"selector": ["salePriceText", "config", "style", "typography", "fontSize"], "value": {"$jsan": "u"}, "label": "Text size", "editorType": {"label": "", "type": "rangeSliderInput", "defaultValue": {"$jsan": "u"}, "props": {"minRange": "8", "maxRange": "32", "steps": 1, "label": "Text size"}, "name": "fontSize"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "salePriceText.config.horizontalAlign": {"data": {"selector": ["salePriceText", "config", "horizontalAlign"], "value": {"$jsan": "u"}, "label": "Alignment", "editorType": {"label": "", "type": "alignmentEditor", "defaultValue": {"$jsan": "u"}, "props": {"label": "Alignment", "options": [{"icon": "alpha-a", "value": "auto"}, {"icon": "format-align-left", "value": "left"}, {"icon": "format-align-center", "value": "center"}, {"icon": "format-align-right", "value": "right"}, {"icon": "format-align-justify", "value": "justify"}]}, "name": "horizontalAlign"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "salePriceText.config.style.color": {"data": {"selector": ["salePriceText", "config", "style", "color"], "value": {"$jsan": "u"}, "label": "Color", "editorType": {"label": "", "type": "colorInput", "defaultValue": {"$jsan": "u"}, "props": {"disableBinding": true, "label": "Color"}, "name": "color"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "tile3.Section2": {"data": {"selector": ["tile3", "Section2"], "value": {"$jsan": "u"}, "label": "Strike-off Price", "editorType": {"type": "editorSectionHeader", "props": {"label": "Strike-off Price"}, "name": "Section2"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "strikeoutPriceText.config.style.typography.fontSize": {"data": {"selector": ["strikeoutPriceText", "config", "style", "typography", "fontSize"], "value": {"$jsan": "u"}, "label": "Text size", "editorType": {"label": "", "type": "rangeSliderInput", "defaultValue": {"$jsan": "u"}, "props": {"minRange": "8", "maxRange": "32", "steps": 1, "label": "Text size"}, "name": "fontSize"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "strikeoutPriceText.config.horizontalAlign": {"data": {"selector": ["strikeoutPriceText", "config", "horizontalAlign"], "value": {"$jsan": "u"}, "label": "Alignment", "editorType": {"label": "", "type": "alignmentEditor", "defaultValue": {"$jsan": "u"}, "props": {"label": "Alignment", "options": [{"icon": "alpha-a", "value": "auto"}, {"icon": "format-align-left", "value": "left"}, {"icon": "format-align-center", "value": "center"}, {"icon": "format-align-right", "value": "right"}, {"icon": "format-align-justify", "value": "justify"}]}, "name": "horizontalAlign"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "strikeoutPriceText.config.style.color": {"data": {"selector": ["strikeoutPriceText", "config", "style", "color"], "value": {"$jsan": "u"}, "label": "Color", "editorType": {"label": "", "type": "colorInput", "defaultValue": {"$jsan": "u"}, "props": {"disableBinding": true, "label": "Color"}, "name": "color"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "ProductPriceCard.config.style.backgroundColor": {"data": {"selector": ["ProductPriceCard", "config", "style", "backgroundColor"], "value": {"$jsan": "u"}, "label": "Background color", "editorType": {"label": "", "type": "colorInput", "defaultValue": {"$jsan": "u"}, "props": {"disableBinding": true, "label": "Background color"}, "name": "backgroundColor"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}}, "__serializedType__": "ImmutableOrderedMap"}, "moduleConfig": {"data": {"ProductPriceCard": {"data": {"id": "ProductPriceCard", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingTop": "8", "paddingRight": "12", "paddingBottom": "8", "paddingLeft": "12", "backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ProductPriceContainer": {"data": {"id": "ProductPriceContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductPriceCard", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "salePriceText": {"data": {"id": "salePriceText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"horizontalAlign": "left", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "verticalAlign": "center", "value": "{{PDP.value.activeVariant?.displaySalePrice}}", "style": {"data": {"typography": {"data": {"_inherit": "typography.heading"}, "__serializedType__": "ImmutableMap"}, "color": "#e65a4c"}, "__serializedType__": "ImmutableMap"}, "isLoading": "", "numLines": 1, "overflowType": "hidden"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductPriceContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "", "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ProductActualPriceContainer": {"data": {"id": "ProductActualPriceContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductPriceCard", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "strikeoutPriceText": {"data": {"id": "strikeoutPriceText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "{{PDP.value.activeVariant?.displayPrice}}", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "numLines": 1, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.subHeading", "textDecorationLine": "line-through"}, "__serializedType__": "ImmutableMap"}, "marginLeft": "4", "color": "#757575"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductActualPriceContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PDP": {"data": {"id": "PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile3.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}}, "__serializedType__": "ImmutableOrderedMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 11}, {"data": {"moduleUUID": "8d779164-9ffa-4475-89a4-3063fcf69638", "moduleSaveId": {"$jsan": "u"}, "moduleName": "Image banner 01", "inputs": [], "outputs": [], "queries": [], "events": ["onEvent1"], "tags": ["image-banner"], "persistInputBindings": false, "inputBindings": {"data": {}, "__serializedType__": "ImmutableMap"}, "defaultEventHandlers": {"data": [{"data": {"eventHandler": {"data": {"label": "onEvent1", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "Collection", "prop": "", "value": null, "params": {"data": {"collectionHandle": ""}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}, "isExposed": true}, "__serializedType__": "ImmutableMap"}], "__serializedType__": "ImmutableList"}, "editors": {"data": {"tile3.Section1": {"data": {"selector": ["tile3", "Section1"], "value": {"$jsan": "u"}, "label": "Image", "editorType": {"type": "editorSectionHeader", "props": {"label": "Image"}, "name": "Section1"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "Banner.config.value": {"data": {"selector": ["Banner", "config", "value"], "value": {"$jsan": "u"}, "label": "Attach Image", "editorType": {"label": "", "type": "assetEditor", "defaultValue": {"$jsan": "u"}, "props": {"urlProperty": "value", "assetProperty": "assetId", "sourceTypeProperty": "sourceType", "disableBinding": true, "label": "Attach Image"}, "name": "value"}, "mandatory": true}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}, "Banner.layout.aspectRatio": {"data": {"selector": ["Banner", "layout", "aspectRatio"], "value": {"$jsan": "u"}, "label": "Adjust fit", "editorType": {"label": "", "type": "rangeSliderInput", "defaultValue": {"$jsan": "u"}, "props": {"minRange": "0.5", "maxRange": "3", "steps": "0.01", "label": "Adjust fit"}, "name": "aspectRatio"}, "mandatory": true}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}}, "__serializedType__": "ImmutableOrderedMap"}, "styleEditors": {"data": {"Banner.layout.height": {"data": {"selector": ["Banner", "layout", "height"], "value": {"$jsan": "u"}, "label": "Height", "editorType": {"label": "", "type": "rangeSliderInput", "defaultValue": {"$jsan": "u"}, "props": {"minRange": 1, "maxRange": "1000", "steps": 1, "label": "Height"}, "name": "height"}, "mandatory": false}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 15}}, "__serializedType__": "ImmutableOrderedMap"}, "moduleConfig": {"data": {"ImageBanner": {"data": {"id": "ImageBanner", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": true, "hapticMethod": "tick", "style": {"data": {"padding": "0", "margin": "0"}, "__serializedType__": "ImmutableMap"}, "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent1", "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Banner": {"data": {"id": "Banner", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"value": "https://cdn-demo.apptile.io/313b261b-da83-45bf-8ae4-6011c4c05a43/dc9d84d9-3057-417e-95ef-3532423a5922/original-480x480.png", "resizeMode": "cover", "sourceType": "url", "assetId": "", "isLoading": false, "style": {"data": {"margin": "0", "padding": "0"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ImageBanner", "hidden": {"$jsan": "u"}, "aspectRatio": "0.875", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": "auto", "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}}, "__serializedType__": "ImmutableOrderedMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 11}], "page": {"data": {"title": "New Page", "pageId": "Custom_Theme_Onboarding_2249", "pageKey": {"$jsan": "u"}, "pageParams": {"data": {"productHandle": {"data": {"name": "productHandle", "isRequired": true, "defaultValue": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 9}}, "__serializedType__": "ImmutableMap"}, "plugins": {"data": {"GetProductMetafields": {"data": {"id": "GetProductMetafields", "type": "query", "subtype": "QueryPlugin", "config": {"data": {"isPaginated": false, "onSuccess": "", "datasource": "shopify", "pageinationMeta": null, "runWhenModelUpdates": true, "onError": "", "data": null, "errors": null, "inputVariables": {"data": {"productHandle": "{{currentPage.params?.productHandle}}", "identifiers": "{{[{key: 'g_bestsellertag', namespace: 'custom'},{key: 'after_atc_test_ingrdients', namespace: 'my_fields'},{key: 'after_atc_single_line_text', namespace: 'my_fields'},{key: 'rich_text_skin_type_batc', namespace: 'my_fields'},{key: 'product_size', namespace: 'my_fields'},{key: 'ingredients1_url', namespace: 'my_fields'},{key: 'ingredients2_url', namespace: 'my_fields'},{key: 'ingredients3_url', namespace: 'my_fields'},{key: 'featured-product-final', namespace: 'custom'},{key: 'how_to_use', namespace: 'my_fields'}, {key: 'test_benefit_url', namespace: 'my_fields'}, {key: 'after_atc_benefit2_url', namespace: 'my_fields'},{key: 'after_atc_benefit3_url', namespace: 'my_fields'}\n]}}"}, "__serializedType__": "ImmutableMap"}, "hasError": false, "metadata": null, "rawData": null, "executeQuery": "", "queryName": "GetProductMetafieldsByHandle", "cachePolicy": "no-cache", "timestamp": 0, "runWhenPageLoads": true, "hasNextPage": false, "loading": false, "runOnPageFocus": false, "prevRunVariables": null}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ActiveItemWishlistState": {"data": {"id": "ActiveItemWishlistState", "type": "state", "subtype": "StatePlugin", "config": {"data": {"value": "{{\nlocalWishlist.products.find(product=>product.id==PDP.product.id.split('/').pop())\n}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PDP": {"data": {"id": "PDP", "type": "state", "subtype": "ShopifyPDP_22_10", "config": {"data": {"selectedOptions": {}, "datasource": "shopify", "variantImageFilterByOptionName": "", "variantImageSelectionStrategy": "", "variantCount": 1, "product": "", "productOptions": "", "value": null, "optionNames": [], "productHandle": "{{currentPage.params?.productHandle}}", "displayOptions": {}, "variantImages": [], "selectVariantOption": "action", "activeVariant": {}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "query1": {"data": {"id": "query1", "type": "query", "subtype": "QueryPlugin", "config": {"data": {"isPaginated": false, "onSuccess": "", "datasource": "shopify", "pageinationMeta": null, "runWhenModelUpdates": false, "onError": "", "data": null, "errors": null, "inputVariables": {"data": {"identifiers": "{{[{key: 'top_ing2', namespace: 'custom'}]}}"}, "__serializedType__": "ImmutableMap"}, "hasError": false, "metadata": null, "rawData": null, "executeQuery": "", "queryName": "GetProductMetafieldsByHandle", "cachePolicy": "no-cache", "timestamp": 0, "runWhenPageLoads": false, "hasNextPage": false, "loading": false, "runOnPageFocus": false, "prevRunVariables": null}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PDPContainer": {"data": {"id": "PDPContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#F9F9F9"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "100%", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "bottomCaret1": {"data": {"id": "bottomCaret1", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "<PERSON><PERSON><PERSON>", "value": "triangle-down", "style": {"data": {"fontSize": "32", "color": "#222222"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "SecondaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": "61", "bottom": {"$jsan": "u"}, "left": "20", "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorCont1": {"data": {"id": "colorCont1", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.secondary", "borderColor": "#8f8f8f", "borderRadius": "8", "borderWidth": "1"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "SecondaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "30%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorNameCont1": {"data": {"id": "colorNameCont1", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingLeft": "8"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "SecondaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": "space-evenly", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "50%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "emptyText1": {"data": {"id": "emptyText1", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Text", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "colorCont1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "0", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "0", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorName1": {"data": {"id": "colorName1", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Secondary Color", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"color": "#fff", "typography": {"data": {"fontSize": "14"}, "__serializedType__": "ImmutableMap"}, "backgroundColor": "#222222"}, "__serializedType__": "ImmutableMap"}, "horizontalAlign": "left"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "colorNameCont1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "bottomCaret": {"data": {"id": "bottomCaret", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "<PERSON><PERSON><PERSON>", "value": "triangle-down", "style": {"data": {"fontSize": "32", "color": "#222222"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": "61", "bottom": {"$jsan": "u"}, "left": "20", "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorCont": {"data": {"id": "colorCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.primary", "borderColor": "colors.onPrimary", "borderRadius": "8", "borderWidth": "1"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "40%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorNameCont": {"data": {"id": "colorNameCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingLeft": "8"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": "space-evenly", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "50%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "colorName": {"data": {"id": "colorName", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Primary Color", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"color": "#fff", "typography": {"data": {"fontSize": "14", "lineHeight": "18"}, "__serializedType__": "ImmutableMap"}, "backgroundColor": "#222222"}, "__serializedType__": "ImmutableMap"}, "horizontalAlign": "left"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "colorNameCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "emptyText": {"data": {"id": "emptyText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Text", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "colorCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "0", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "0", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Icon1113": {"data": {"id": "Icon1113", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "<PERSON><PERSON><PERSON>", "value": "triangle-down", "style": {"data": {"fontSize": "32", "color": "#2d2d2d"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": "61", "bottom": {"$jsan": "u"}, "left": "20", "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "container2112": {"data": {"id": "container2112", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.primary", "margin": "12", "borderRadius": "8", "borderWidth": "1", "borderColor": "colors.onPrimary"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "28%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Icon11112": {"data": {"id": "Icon11112", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "<PERSON><PERSON><PERSON>", "value": "triangle-down", "style": {"data": {"fontSize": "32", "color": "#2d2d2d"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::PrimaryColorNudge", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": "61", "bottom": {"$jsan": "u"}, "left": "20", "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "text1112": {"data": {"id": "text1112", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Text", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container2112", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "AppHeaderBox1": {"data": {"id": "AppHeaderBox1", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingLeft": 8, "paddingRight": 8, "paddingBottom": 8}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PDPContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "60", "justifyContent": "space-between", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": "absolute", "top": "5", "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "HamburgerCont1": {"data": {"id": "HamburgerCont1", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "events": {"data": [], "__serializedType__": "ImmutableList"}, "style": {"data": {"padding": 10, "marginLeft": 4}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "AppHeaderBox1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "logoCont": {"data": {"id": "logoCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "events": {"data": [], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "AppHeaderBox1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": "center", "alignItems": {"$jsan": "u"}, "alignSelf": "center", "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "center", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "SearchCont": {"data": {"id": "SearchCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "events": {"data": [], "__serializedType__": "ImmutableList"}, "style": {"data": {}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "AppHeaderBox1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row-reverse", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "HamburgerIcon": {"data": {"id": "HamburgerIcon", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"assetId": "", "value": "https://cdn.apptile.io/2aca332b-5107-4666-81d1-4e6c08e42d46/720bc89e-e292-41be-8eeb-dd860550d35d/original.png", "style": {"data": {"marginLeft": 0, "marginTop": 0, "marginBottom": 0}, "__serializedType__": "ImmutableMap"}, "isLoading": false, "aspectRatio": 1, "sourceType": "url", "loadingIcon": "", "resizeMode": "contain", "allowPreview": false}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "HamburgerCont1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": "", "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "20", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "20", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "logoImage": {"data": {"id": "logoImage", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"value": "{{Apptile.brandLogoAssetId}}", "resizeMode": "cover", "sourceType": "upload", "assetId": "{{Apptile.brandLogoAssetId}}", "isLoading": false, "dynamicWidth": true, "width": "{{Apptile.brandLogoSize}}", "aspectRatio": "", "loadingIcon": "", "allowPreview": false}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "logoCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": "auto", "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "container12": {"data": {"id": "container12", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"marginLeft": "8"}, "__serializedType__": "ImmutableMap"}, "events": {"data": [{"data": {"label": "onTap", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "Search", "prop": "", "value": null, "params": {"data": {"collectionHandle": ""}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "SearchCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Icon41": {"data": {"id": "Icon41", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "Material Icon", "value": "magnify", "style": {"data": {"fontSize": "24"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container12", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "rootContainer": {"data": {"id": "rootContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#F9F9F9", "zIndex": "1"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PDPContainer", "hidden": "", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "", "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": "", "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": "scroll", "width": "100%", "position": "absolute", "top": "60", "bottom": "80", "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "FavouriteIconFilled": {"data": {"id": "FavouriteIconFilled", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": true, "hapticMethod": "tap", "style": {"data": {}, "__serializedType__": "ImmutableMap"}, "events": {"data": [], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "FavouritesContainer", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": "center", "alignItems": "center", "alignSelf": "center", "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "center", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "40", "position": "relative", "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "FilledFavouriteIcon": {"data": {"id": "FilledFavouriteIcon", "type": "widget", "subtype": "IconWidget", "config": {"data": {"iconType": "Material Icon", "value": "heart", "style": {"data": {"fontSize": "30", "color": "#000000"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "FavouriteIconFilled", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile1::ImageBanner": {"data": {"id": "tile1::ImageBanner", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": true, "detectVisibility": false, "enableHaptics": true, "hapticMethod": "tick", "style": {"data": {"padding": "0", "margin": "0"}, "__serializedType__": "ImmutableMap"}, "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent1", "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile1"], "pluginId": "ImageBanner"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile1::Banner": {"data": {"id": "tile1::<PERSON>", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"value": "https://cdn-demo.apptile.io/dd2b1d4f-f124-46d6-8cb6-eae12e5b6442/93475dbd-2d9e-4a5a-a6d6-1338e882e6a9/original-1024x1024.jpg", "resizeMode": "contain", "sourceType": "url", "assetId": "", "isLoading": false, "style": {"data": {"margin": "0", "padding": "0"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile1::ImageBanner", "hidden": {"$jsan": "u"}, "aspectRatio": "5.5", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": "auto", "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile1"], "pluginId": "Banner"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ProductSlider": {"data": {"id": "ProductSlider", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "1", "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "FavouritesContainer": {"data": {"id": "FavouritesContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"zIndex": "1"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductSlider", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": "center", "alignItems": "center", "alignSelf": "center", "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "center", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "40", "position": "absolute", "top": {"$jsan": "u"}, "bottom": "20", "left": {"$jsan": "u"}, "right": "20"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ProductImageSlider": {"data": {"id": "ProductImageSlider", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductSlider", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": "", "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Label": {"data": {"id": "Label", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.secondary", "paddingTop": "4", "paddingBottom": "4", "paddingRight": "8", "paddingLeft": "8", "borderBottomRightRadius": "8", "borderTopRightRadius": "8"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductSlider", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": "20", "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "listview1": {"data": {"id": "listview1", "type": "widget", "subtype": "ListViewWidget", "config": {"data": {"horizontal": true, "scrollToIndex": "action", "data": "", "currentOffset": 0, "isSliderMode": true, "isLoading": false, "numPlaceholderItems": 3, "itemWidth": "", "onEndReachedThreshold": 0.5, "isRepeaterMode": false, "onEndReached": "", "numColumns": 1, "itemHeight": "300", "instances": "4"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductImageSlider", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"data": {"enabled": true, "transitions": {"data": {"entering": {"$jsan": "u"}, "exiting": {"$jsan": "u"}, "layout": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 18}, "references": {"data": {}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 17}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "scrollBubbles1": {"data": {"id": "scrollBubbles1", "type": "widget", "subtype": "ScrollBubblesWidget", "config": {"data": {"value": "{{PDP.variantImages.length || 1}}", "isLoading": false, "style": {"data": {"_profile": "default"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductImageSlider", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "absolute", "top": {"$jsan": "u"}, "bottom": "8", "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"data": {"enabled": true, "transitions": {"data": {"entering": {"$jsan": "u"}, "exiting": {"$jsan": "u"}, "layout": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 18}, "references": {"data": {"currentItem": ["listview1", "currentItem"]}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 17}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "container1": {"data": {"id": "container1", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "listview1", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "1", "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "image1": {"data": {"id": "image1", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"value": "https://iconicentertainment.in/wp-content/uploads/2013/11/dummy-image-square.jpg", "resizeMode": "contain", "sourceType": "url", "assetId": "", "isLoading": false}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container1", "hidden": {"$jsan": "u"}, "aspectRatio": "1", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::CtaBtnContainer": {"data": {"id": "tile7::CtaBtnContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingTop": "12", "marginRight": 0, "borderBottomWidth": 0, "marginLeft": 0, "backgroundColor": "colors.onPrimary", "zIndex": "2", "marginBottom": 0, "paddingBottom": 8, "elevation": "20"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "", "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "85", "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "", "position": "absolute", "top": "", "bottom": "0", "left": "0", "right": "0"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "CtaBtnContainer"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::OneTimeBuyCont": {"data": {"id": "tile7::OneTimeBuyCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7::CtaBtnContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "OneTimeBuyCont"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::CreateCheckoutQueryV1": {"data": {"id": "tile7::<PERSON><PERSON><PERSON>heckoutQueryV1", "type": "query", "subtype": "QueryPlugin", "config": {"data": {"isPaginated": false, "onSuccess": "", "datasource": "shopify", "pageinationMeta": null, "runWhenModelUpdates": false, "onError": "", "data": null, "errors": null, "inputVariables": {"data": {"lineItems": "{{[\n{\n    quantity: 1,\n    variationId: PDP.value.activeVariant.id,\n  }]}}"}, "__serializedType__": "ImmutableMap"}, "hasError": false, "metadata": null, "rawData": null, "executeQuery": "", "queryName": "CreateCheckoutV1", "cachePolicy": "no-cache", "timestamp": 0, "runWhenPageLoads": false, "events": {"data": [{"data": {"label": "onSuccess", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent5", "params": {"data": {"checkoutId": "{{CreateCheckoutQueryV1.data.id}}"}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "hasNextPage": false, "loading": false, "runOnPageFocus": false, "prevRunVariables": null}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7::OneTimeBuyCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "CreateCheckoutQueryV1"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::OneTimeAddToCart": {"data": {"id": "tile7::OneTimeAddToCart", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"value": "ADD TO CART", "onTap": "", "loading": "", "disabled": "", "enableHaptics": "", "hapticMethod": "", "events": {"data": [], "__serializedType__": "ImmutableList"}, "style": {"data": {"marginLeft": "4%", "_profile": "outline", "borderRadius": "12", "borderColor": "colors.primary", "color": "colors.primary"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7::OneTimeBuyCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "OneTimeAddToCart"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::OneTimeBuyBtn": {"data": {"id": "tile7::OneTimeBuyBtn", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"horizontalAlign": "center", "onTap": "", "verticalAlign": "center", "value": "BUY NOW", "style": {"data": {"marginLeft": "4%", "_profile": "primary", "borderRadius": "12", "backgroundColor": "colors.primary", "color": "colors.onPrimary", "typography": {"data": {"_inherit": "typography.subHeading"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "hapticMethod": "tap", "events": {"data": [], "__serializedType__": "ImmutableList"}, "enableHaptics": true, "loading": "{{CreateCheckoutQueryV1.loading}}", "disabled": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7::OneTimeBuyCont", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "OneTimeBuyBtn"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::shopify": {"data": {"id": "tile7::shopify", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile7.shopify}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "shopify"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::PDP": {"data": {"id": "tile7::PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile7.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "PDP"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::Input3": {"data": {"id": "tile7::Input3", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile7.Input3}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "Input3"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7": {"data": {"id": "tile7", "type": "widget", "subtype": "ModuleInstance", "config": {"data": {"childNamespace": "tile7", "moduleUUID": "3101ff87-d7f1-4c6c-969d-b9b3fc188f16", "moduleName": "Add to Cart/Subscribe Buttons", "events": {"data": [], "__serializedType__": "ImmutableList"}, "shopify": "{{shopify}}", "PDP": "{{PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "PDPContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile7::OneTimeGoToCart": {"data": {"id": "tile7::OneTimeGoToCart", "type": "widget", "subtype": "ButtonWidget", "config": {"data": {"value": "GO TO CART", "onTap": "", "loading": "", "disabled": "", "enableHaptics": true, "hapticMethod": "tap", "events": {"data": [{"data": {"label": "onTap", "type": "action", "method": "forwardModuleEvent", "pluginId": "", "isGlobalPlugin": true, "screenName": "", "prop": "", "value": "onEvent3", "params": {"data": {}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}, "style": {"data": {"marginLeft": "4%", "color": "colors.secondary", "_profile": "outline", "borderColor": "colors.secondary"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile7::OneTimeBuyCont", "hidden": "{{shopify.value.currentCartLineItems && PDP.value.activeVariant.id ?!(_.some(shopify.value.currentCartLineItems, (v)=>v.merchandiseId===PDP.value.activeVariant.id) ):true}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": "50", "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "44%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile7"], "pluginId": "OneTimeGoToCart"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "Bestsellertag": {"data": {"id": "Bestsellertag", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Best Seller", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"letterSpacing": "2"}, "__serializedType__": "ImmutableMap"}, "color": "#FFFFFF"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "Label", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2": {"data": {"id": "tile2", "type": "widget", "subtype": "ModuleInstance", "config": {"data": {"childNamespace": "tile2", "moduleUUID": "1b5e6cb5-1883-47d2-8f01-108efb6f9526", "moduleName": "Product Title 03 (without variant title)", "events": {"data": [], "__serializedType__": "ImmutableList"}, "PDP": "{{PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::ratingcontainer": {"data": {"id": "tile2::ratingcontainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingLeft": "12", "paddingBottom": "8"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::ProductTitleTile", "hidden": false, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "flex-start", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "ratingcontainer"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::text1": {"data": {"id": "tile2::text1", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "{{_.round(PDP.value.product.metafields[0].value.value, 1)}}", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.subHeading", "fontSize": "15", "lineHeight": "18", "letterSpacing": ""}, "__serializedType__": "ImmutableMap"}, "paddingRight": "4", "color": "colors.onBackground"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::ratingcontainer", "hidden": "{{PDP.value.product.metafields.length==0}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "text1"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::Rating1": {"data": {"id": "tile2::Rating1", "type": "widget", "subtype": "RatingWidget", "config": {"data": {"value": "{{_.round(PDP.value.product.metafields[0].value.value, 1)}}", "readonly": true, "onChange": "", "style": {"data": {"color": "colors.primary", "fontSize": "14", "paddingRight": "1"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::ratingcontainer", "hidden": "{{PDP.value.product.metafields.length==0}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "Rating1"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "ProductSubTitle": {"data": {"id": "ProductSubTitle", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "text2": {"data": {"id": "text2", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "A brief tagline to bring out the uniqueness of your product", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.body", "fontSize": "12"}, "__serializedType__": "ImmutableMap"}, "color": "#4b4b4b", "marginRight": "12", "marginLeft": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "ProductSubTitle", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "container3": {"data": {"id": "container3", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "PrimaryColorNudge": {"data": {"id": "PrimaryColorNudge", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#222222", "padding": "12", "borderRadius": "8", "borderWidth": "1", "borderColor": "colors.onBackground", "zIndex": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container3", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "165", "position": "absolute", "top": {"$jsan": "u"}, "bottom": "70", "left": "10", "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "VariantRadioGroup": {"data": {"id": "VariantRadioGroup", "type": "widget", "subtype": "RadioGroupWidget", "config": {"data": {"onTap": "", "input": "{{[{name: \"Variant 1\", value: \"new\"},{name: \"Variant 2\", value: \"new2\"},{name: \"Variant 3\", value: \"new3\"}]}}", "isMultiSelect": false, "value": "new", "style": {"data": {"selectedBackgroundColor": "colors.primary", "color": "#000000", "typography": {"data": {"_inherit": "typography.body"}, "__serializedType__": "ImmutableMap"}, "selectedTypography": {"data": {"_inherit": "typography.body"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "isLoading": false, "hapticMethod": "", "enableHaptics": "", "radioGroupType": "Text", "itemAlignSelf": "center"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container3", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": "center", "alignItems": "", "alignSelf": "", "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": 70, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "container2": {"data": {"id": "container2", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": "relative", "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "SecondaryColorNudge": {"data": {"id": "SecondaryColorNudge", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#222222", "zIndex": "12", "borderRadius": "8", "borderWidth": "1", "borderColor": "colors.onBackground", "padding": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container2", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": "1", "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "190", "position": "absolute", "top": {"$jsan": "u"}, "bottom": "60", "left": {"$jsan": "u"}, "right": "10"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3": {"data": {"id": "tile3", "type": "widget", "subtype": "ModuleInstance", "config": {"data": {"childNamespace": "tile3", "moduleUUID": "8068152d-5823-4820-bfd3-f6a3f8972364", "moduleName": "Product Price 01", "events": {"data": [], "__serializedType__": "ImmutableList"}, "PDP": "{{PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "container2", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::Priceanddiscount": {"data": {"id": "tile3::Priceanddiscount", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductPriceCard", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "Priceanddiscount"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::DiscountPercentage": {"data": {"id": "tile3::DiscountPercentage", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "colors.secondary", "marginRight": "5", "paddingRight": "8", "paddingLeft": "8"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::Priceanddiscount", "hidden": "{{!PDP.value.activeVariant?.price}}", "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": "center", "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "center", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "DiscountPercentage"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::text1": {"data": {"id": "tile3::text1", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Save {{_.round(100/PDP.value.activeVariant?.price* (PDP.value.activeVariant?.price- PDP.value.activeVariant?.salePrice))}}%", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"fontSize": "12", "lineHeight": "16", "_inherit": "typography.body"}, "__serializedType__": "ImmutableMap"}, "color": "colors.onSecondary"}, "__serializedType__": "ImmutableMap"}, "verticalAlign": "center"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::DiscountPercentage", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "text1"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::ProductTitleTile": {"data": {"id": "tile2::ProductTitleTile", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "ProductTitleTile"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::TitleText": {"data": {"id": "tile2::TitleText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "Product Title", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.heading"}, "__serializedType__": "ImmutableMap"}, "paddingTop": "16", "paddingRight": "12", "paddingBottom": "8", "paddingLeft": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2::ProductTitleTile", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "TitleText"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::PDP": {"data": {"id": "tile2::PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile2.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "PDP"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile2::Input2": {"data": {"id": "tile2::Input2", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile2.Input2}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile2", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile2"], "pluginId": "Input2"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "DynamicOffers": {"data": {"id": "DynamicOffers", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "DiscountBannerCont": {"data": {"id": "DiscountBannerCont", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"marginRight": "12", "marginLeft": "12", "borderRadius": "12", "marginTop": "18", "marginBottom": "18"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "DynamicOffers", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "DiscountBannerImage": {"data": {"id": "DiscountBannerImage", "type": "widget", "subtype": "ImageWidget", "config": {"data": {"value": "https://cdn-demo.apptile.io/69e85479-ff66-4755-b6b0-ca983cf37212/0f8d3e12-cdd0-481e-9737-22acfc7a1f8c/original-480x480.jpg", "resizeMode": "contain", "sourceType": "url", "assetId": "0f8d3e12-cdd0-481e-9737-22acfc7a1f8c", "isLoading": false, "style": {"data": {"borderRadius": "12"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "DiscountBannerCont", "hidden": {"$jsan": "u"}, "aspectRatio": "4", "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": "auto", "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": "100%", "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::ProductPriceCard": {"data": {"id": "tile3::ProductPriceCard", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": "", "style": {"data": {"paddingTop": "18", "paddingRight": "12", "paddingBottom": "18", "paddingLeft": "12", "backgroundColor": "#ffffff"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": "", "alignItems": "center", "alignSelf": "", "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "ProductPriceCard"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::ProductPriceContainer": {"data": {"id": "tile3::ProductPriceContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductPriceCard", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "column", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "ProductPriceContainer"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::salePriceText": {"data": {"id": "tile3::salePriceText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"horizontalAlign": "left", "adjustsFontSizeToFit": false, "useAnimamtedValue": false, "minFontScale": 0.5, "verticalAlign": "center", "value": "$75.00", "style": {"data": {"typography": {"data": {"_inherit": "typography.heading", "fontSize": "28"}, "__serializedType__": "ImmutableMap"}, "color": "#000000", "marginRight": "4"}, "__serializedType__": "ImmutableMap"}, "isLoading": "", "numLines": 1, "overflowType": "hidden"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductPriceContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": "", "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "salePriceText"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::ProductActualPriceContainer": {"data": {"id": "tile3::ProductActualPriceContainer", "type": "widget", "subtype": "ContainerWidget", "config": {"data": {"isTappable": false, "detectVisibility": false, "enableHaptics": false, "hapticMethod": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductPriceCard", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": "center", "alignItems": "center", "alignSelf": "center", "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": "row", "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": "", "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "ProductActualPriceContainer"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::strikeoutPriceText": {"data": {"id": "tile3::strikeoutPriceText", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "$90.00", "adjustsFontSizeToFit": false, "minFontScale": 0.5, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"typography": {"data": {"_inherit": "typography.subHeading", "textDecorationLine": "line-through"}, "__serializedType__": "ImmutableMap"}, "color": "#757575", "marginLeft": "6", "_profile": "default"}, "__serializedType__": "ImmutableMap"}, "verticalAlign": ""}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductActualPriceContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "strikeoutPriceText"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::text7": {"data": {"id": "tile3::text7", "type": "widget", "subtype": "TextWidget", "config": {"data": {"value": "(20% off)", "adjustsFontSizeToFit": false, "minFontScale": 1, "numLines": 1, "useAnimamtedValue": false, "isLoading": false, "style": {"data": {"color": "colors.onPrimary", "paddingTop": "4", "paddingRight": "12", "typography": {"data": {"_inherit": "typography.body"}, "__serializedType__": "ImmutableMap"}, "marginLeft": "12", "backgroundColor": "colors.secondary", "paddingLeft": "12", "paddingBottom": "4", "borderRadius": "20"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3::ProductActualPriceContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": 1, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "text7"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile3::PDP": {"data": {"id": "tile3::PDP", "type": "state", "subtype": "ModuleProperty", "config": {"data": {"value": "{{tile3.PDP}}"}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "tile3", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"namespace": ["tile3"], "pluginId": "PDP"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}, "tile1": {"data": {"id": "tile1", "type": "widget", "subtype": "ModuleInstance", "config": {"data": {"childNamespace": "tile1", "moduleUUID": "8d779164-9ffa-4475-89a4-3063fcf69638", "moduleName": "Image banner 01", "events": {"data": [{"data": {"label": "onEvent1", "type": "page", "method": "navigate", "pluginId": null, "isGlobalPlugin": false, "screenName": "Collection", "prop": "", "value": null, "params": {"data": {"collectionHandle": ""}, "__serializedType__": "ImmutableMap"}, "hasCondition": false, "condition": ""}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 6}], "__serializedType__": "ImmutableList"}}, "__serializedType__": "ImmutableMap"}, "layout": {"data": {"container": "rootContainer", "hidden": {"$jsan": "u"}, "aspectRatio": {"$jsan": "u"}, "alignContent": {"$jsan": "u"}, "alignItems": {"$jsan": "u"}, "alignSelf": {"$jsan": "u"}, "flex": {"$jsan": "u"}, "flexBasis": {"$jsan": "u"}, "flexDirection": {"$jsan": "u"}, "direction": {"$jsan": "u"}, "flexGrow": {"$jsan": "u"}, "flexShrink": {"$jsan": "u"}, "flexWrap": {"$jsan": "u"}, "height": {"$jsan": "u"}, "justifyContent": {"$jsan": "u"}, "maxHeight": {"$jsan": "u"}, "maxWidth": {"$jsan": "u"}, "minHeight": {"$jsan": "u"}, "minWidth": {"$jsan": "u"}, "overflow": {"$jsan": "u"}, "width": {"$jsan": "u"}, "position": {"$jsan": "u"}, "top": {"$jsan": "u"}, "bottom": {"$jsan": "u"}, "left": {"$jsan": "u"}, "right": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 4}, "namespace": {"$jsan": "u"}, "analytics": {"$jsan": "u"}, "animations": {"$jsan": "u"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 0}}, "__serializedType__": "ImmutableOrderedMap"}, "containerType": "View", "disableSafeArea": false, "disablePageCache": true, "disableBackground": false, "type": "Screen", "pageOptions": {"data": {}, "__serializedType__": "ImmutableMap"}, "events": {"data": [], "__serializedType__": "ImmutableList"}, "_cachedPluginModels": {"$jsan": "u"}, "_cachedDependencyGraph": {"$jsan": "u"}, "analytics": {"data": {"enabled": true, "type": "page", "name": "pageView", "params": {"data": {"pageName": "Product Page", "pageId": "NewProduct"}, "__serializedType__": "ImmutableMap"}}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 16}, "pageUUID": null, "transition": "<PERSON><PERSON><PERSON>"}, "__serializedType__": "ImmutableRecord", "__serializedRef__": 1}}