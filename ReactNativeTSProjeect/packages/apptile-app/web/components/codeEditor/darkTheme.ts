import {EditorView} from '@codemirror/view';
import {Extension} from '@codemirror/state';
import {HighlightStyle, syntaxHighlighting} from '@codemirror/language';
import {tags as t} from '@lezer/highlight';
import {ThemeColors} from './themetype';
import appTheme from '../../views/prompt-to-app/styles-prompt-to-app/theme';

// Dark theme color palette based on Figma design
// Base editor colors
const background = '#282828'; // Editor background from Figma
const foreground = '#EFEFEF'; // Default text color from Figma
const darkBackground = '#1E1E1E'; // Darker variation for panels
const tooltipBackground = '#313131'; // Tooltip background from Figma
const selection = '#3A3A3A'; // Selection background
const cursor = foreground; // Cursor color

// Syntax token colors
const commentColor = '#979797'; // Comments - gray from Figma
const keywordColor = '#0062FF'; // Keywords - blue accent from theme
const nameColor = '#FFFFFF'; // Names - white
const variableColor = '#EFEFEF'; // Variables - light gray from Figma
const functionColor = '#0062FF'; // Functions - blue accent from theme
const constantColor = '#EA599E'; // Constants - pink accent from theme
const typeColor = '#E5DAFA'; // Types - light purple from theme
const operatorColor = '#EFEFEF'; // Operators
const stringColor = '#EA599E'; // Strings - pink accent from theme

// Invalid/error color
const invalidColor = '#ff4c28'; // Error color from theme

// Chat interface colors
const chatMessageBackground = '#1A1D20'; // Background for chat messages
const chatAssistantMessageBackground = '#292B32'; // Background for assistant messages
const userMessageBackground = '#0062FF'; // Background for user messages
const chatBorderColor = '#444444'; // Border color for chat elements
const chatCompletedBackground = '#2A2A2A'; // Background for completed steps
const chatCompletedCheckmark = '#000000'; // Color for completed checkmark (softer green)
const toolCallBackground = '#292B32'; // Background color for tool calls

// Editor theme definition
export const darkEditorTheme = EditorView.theme(
  {
    '&': {
      color: foreground,
      backgroundColor: background,
    },

    '.cm-content': {
      caretColor: cursor,
    },

    '.cm-cursor, .cm-dropCursor': {
      borderLeftColor: cursor,
      borderLeftWidth: '2px',
      marginLeft: '-1.5px',
    },
    '&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection':
      {backgroundColor: selection},

    '.cm-panels': {backgroundColor: darkBackground, color: commentColor},
    '.cm-panels.cm-panels-top': {borderBottom: '2px solid black'},
    '.cm-panels.cm-panels-bottom': {borderTop: '2px solid black'},

    '.cm-searchMatch': {
      backgroundColor: 'transparent',
      outline: `1px solid ${keywordColor}`,
    },
    '.cm-searchMatch.cm-searchMatch-selected': {
      backgroundColor: foreground,
      color: background,
    },

    '.cm-activeLine': {backgroundColor: selection},
    '.cm-selectionMatch': {
      backgroundColor: foreground,
      color: darkBackground,
    },

    '&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket': {
      outline: `1px solid ${keywordColor}`,
    },

    '&.cm-focused .cm-matchingBracket': {
      backgroundColor: tooltipBackground,
      color: foreground,
    },

    '.cm-gutters': {
      backgroundColor: background,
      color: commentColor,
      border: 'none',
    },

    '.cm-activeLineGutter': {
      backgroundColor: selection,
      color: foreground,
    },

    '.cm-foldPlaceholder': {
      backgroundColor: 'transparent',
      border: 'none',
      color: '#ddd',
    },

    '.cm-tooltip': {
      border: 'none',
      backgroundColor: tooltipBackground,
    },
    '.cm-tooltip .cm-tooltip-arrow:before': {
      borderTopColor: 'transparent',
      borderBottomColor: 'transparent',
    },
    '.cm-tooltip .cm-tooltip-arrow:after': {
      borderTopColor: tooltipBackground,
      borderBottomColor: tooltipBackground,
    },
    '.cm-tooltip-autocomplete': {
      '& > ul > li[aria-selected]': {
        backgroundColor: selection,
        color: commentColor,
      },
    },
  },
  {dark: true},
);

// Syntax highlighting style
export const darkHighlightStyle = HighlightStyle.define([
  {tag: t.keyword, color: keywordColor},
  {
    tag: [t.name, t.deleted, t.character, t.propertyName, t.macroName],
    color: nameColor,
  },
  {tag: [t.variableName], color: variableColor},
  {tag: [t.function(t.variableName)], color: functionColor},
  {tag: [t.labelName], color: nameColor},
  {
    tag: [t.color, t.constant(t.name), t.standard(t.name)],
    color: constantColor,
  },
  {tag: [t.definition(t.name), t.separator], color: typeColor},
  {tag: [t.brace], color: variableColor},
  {
    tag: [t.annotation],
    color: invalidColor,
  },
  {
    tag: [t.number, t.changed, t.modifier, t.self, t.namespace],
    color: constantColor,
  },
  {
    tag: [t.typeName, t.className],
    color: typeColor,
  },
  {
    tag: [t.operator, t.operatorKeyword],
    color: operatorColor,
  },
  {
    tag: [t.tagName],
    color: keywordColor,
  },
  {
    tag: [t.squareBracket],
    color: constantColor,
  },
  {
    tag: [t.angleBracket],
    color: constantColor,
  },
  {
    tag: [t.attributeName],
    color: typeColor,
  },
  {
    tag: [t.regexp],
    color: keywordColor,
  },
  {
    tag: [t.quote],
    color: constantColor,
  },
  {tag: [t.string], color: stringColor},
  {
    tag: t.link,
    color: stringColor,
    textDecoration: 'underline',
    textUnderlinePosition: 'under',
  },
  {
    tag: [t.url, t.escape, t.special(t.string)],
    color: variableColor,
  },
  {tag: [t.meta], color: nameColor},
  {tag: [t.monospace], color: variableColor, fontStyle: 'italic'},
  {tag: [t.comment], color: commentColor, fontStyle: 'italic'},
  {tag: t.strong, fontWeight: 'bold', color: keywordColor},
  {tag: t.emphasis, fontStyle: 'italic', color: keywordColor},
  {tag: t.strikethrough, textDecoration: 'line-through'},
  {tag: t.heading, fontWeight: 'bold', color: keywordColor},
  {tag: t.special(t.heading1), fontWeight: 'bold', color: keywordColor},
  {tag: t.heading1, fontWeight: 'bold', color: keywordColor},
  {
    tag: [t.heading2, t.heading3, t.heading4],
    fontWeight: 'bold',
    color: keywordColor,
  },
  {
    tag: [t.heading5, t.heading6],
    color: keywordColor,
  },
  {tag: [t.atom, t.bool, t.special(t.variableName)], color: constantColor},
  {
    tag: [t.processingInstruction, t.inserted],
    color: variableColor,
  },
  {
    tag: [t.contentSeparator],
    color: typeColor,
  },
  {tag: t.invalid, color: invalidColor, borderBottom: `1px dotted ${invalidColor}`},
]);

/// Extension to enable the Dark theme (both editor and highlighting)
export const darkTheme: Extension = [darkEditorTheme, syntaxHighlighting(darkHighlightStyle)];

export const themeColors: ThemeColors = {
  EDITOR_BACKGROUND_COLOR: background,
  EDITOR_FOREGROUND_COLOR: foreground,
  EDITOR_DARK_BACKGROUND: darkBackground,
  EDITOR_ACCENT_COLOR: appTheme.DEFAULT,
  EDITOR_CONTRAST_BACKGROUND: tooltipBackground,
  EDITOR_CONSTANT_COLOR: constantColor,
  EDITOR_STRING_COLOR: stringColor,

  // Chat interface colors
  CHAT_MESSAGE_BACKGROUND: chatMessageBackground,
  USER_MESSAGE_BACKGROUND: userMessageBackground,
  CHAT_ASSISTANT_MESSAGE_BACKGROUND: chatAssistantMessageBackground,
  CHAT_BORDER_COLOR: chatBorderColor,
  CHAT_COMPLETED_BACKGROUND: chatCompletedBackground,
  CHAT_COMPLETED_CHECKMARK: chatCompletedCheckmark,
  TOOL_CALL_BACKGROUND: toolCallBackground,
};
