import React, {FC, useRef, useEffect, useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import {useDispatch} from 'react-redux';

import {basicSetup, EditorView} from 'codemirror';
import {keymap} from '@codemirror/view'
import {EditorState, Compartment} from '@codemirror/state';

import {javascript} from '@codemirror/lang-javascript';
import {DependencyGraph, getJSBindingVariables} from 'apptile-core';
// import {nord} from 'cm6-theme-nord';
import {EDITOR_CLOSE_BINDING_EDITOR} from '../../actions/editorActions';
// import {createCircularReplacer} from '../../../web/common/utils';
import {EditorState as StoreEditorState} from '../../common/webDatatypes';
// import {tokyonightGogh as theme, themeColors} from './tokyonight';
import {goghLight as theme, themeColors} from './goghlight';

const EDITOR_MAX_HEIGHT = '50vh';
const themeConfig = new Compartment();

const BindingEditor: FC<{editorState: StoreEditorState['bindingEditor']}> = function BindingEditor({editorState}) {
  const dispatch = useDispatch();

  const debuggerCheckbox = useRef<HTMLInputElement>();
  const rootContainer = useRef();
  const readOnlyContainer = useRef();
  const editorView = useRef<EditorView>();
  const readOnlyEditorView = useRef<EditorView>();

  const onCommit = useCallback(() => {
    if (editorState.callback && editorView.current) {
      editorState.callback(editorView.current.state.doc.toString());
    }
  }, [editorState.callback, editorView]);

  const closeEditor = () => {
    dispatch({
      type: EDITOR_CLOSE_BINDING_EDITOR,
    });
  };

  const recompile = (code: string, shouldAddDebugger?: boolean) => {
    const depGraph: DependencyGraph | null = DependencyGraph.getInstance();
    if (depGraph) {
      try {
        const {transpiled} = depGraph.transpileBinding(
          code ?? '',
          editorState.namespace,
          editorState.selector,
          shouldAddDebugger || false,
        ) ?? {transpiled: 'compiler returned nothing'};
        return transpiled;
      } catch (err: any) {
        return (
          err?.toString() +
          `
            ` +
          err?.stack?.toString()
        );
      }
    }
  };

  const onTry = useCallback(() => {
    let shouldAddDebugger = false;
    if (debuggerCheckbox.current) {
      shouldAddDebugger = debuggerCheckbox.current.checked;
    }

    const binding = editorView.current?.state?.doc?.toString() ?? '';
    const transpiledCode = recompile(binding, shouldAddDebugger);
    const evalContext = editorState.evalContext;

    let bindingVariables = getJSBindingVariables(binding).map(it => it.join('.'));
    let result;
    try {
      const compiledFn = new Function(`return [${transpiledCode}]`)()[0];
      result = compiledFn(
        evalContext.$pageContext,
        evalContext.$appJSModel,
        0,
        evalContext.currentPage,
        evalContext._,
        evalContext.moment,
      );
      result = `// Any changes made on this side will not be saved!
${transpiledCode}
/* -----------------------
Note that bindings should not mutate any of the inputs. An error will be thrown 
if such a binding is saved. Result of current binding: 
*/ 
const result = ${JSON.stringify(result, null, 2)}
/*
----
dependencies(parents): This function will be re-evaluated everytime the following 
things change in the appModel

${JSON.stringify(bindingVariables, null, 2)}
-------------------------- */`;
    } catch (err: any) {
      result = `${transpiledCode}
/* -----------------------
dependencies(parents): ${JSON.stringify(bindingVariables)}
error:
${err?.toString()}
${err?.stack?.toString()}
 ------------------------ */`;
    }

    readOnlyEditorView.current?.setState(
      EditorState.create({
        doc: result,
        extensions: [
          basicSetup, 
          themeConfig.of(theme),
          javascript(), 
          EditorView.lineWrapping
        ],
      }),
    );
  }, [editorState, editorView, readOnlyEditorView, debuggerCheckbox]);

  useEffect(() => {
    if (rootContainer.current) {
      const keyboardShortcuts = keymap.of([
        {
          key: "Mod-e",  
          run: () => {
            onTry();  
            return true;  
          }
        },
        {
          key: "Mod-s",  
          run: () => {
            onCommit();  
            return true;  
          }
        }
      ]);
      const extensions = [
        basicSetup, 
        themeConfig.of(theme),
        javascript(), 
        EditorView.lineWrapping, 
        keyboardShortcuts
      ];
      editorView.current = new EditorView({
        state: EditorState.create({
          doc: editorState.binding,
          extensions,
        }),
        parent: rootContainer.current,
      });
    }

    if (readOnlyContainer.current) {
      const transpiledCode = recompile(editorState.binding);
      readOnlyEditorView.current = new EditorView({
        state: EditorState.create({
          doc: `// Any changes made on this side will not be saved
${transpiledCode}`,
          extensions: [
            basicSetup, 
            themeConfig.of(theme),
            javascript(), 
            EditorView.lineWrapping, 
          ],
        }),
        parent: readOnlyContainer.current,
      });
    }
  }, [rootContainer]);

  return (
    <View
      style={{
          borderStyle: 'solid',
          position: 'fixed',
          bottom: 0,
          right: 0,
          width: `100vw`,
          height: EDITOR_MAX_HEIGHT,
          backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
          display: 'flex',
          zIndex: 1
        }}
    >
      <div style={{...styles.menubar, backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR}}>
        <div style={{marginRight: 10, color: themeColors.EDITOR_FOREGROUND_COLOR}}>
          <input id="debug" type="checkbox" ref={debuggerCheckbox} />
          Add debugger
        </div>
        <button style={{...styles.button, borderColor: themeColors.EDITOR_ACCENT_COLOR}} onClick={onTry}>
          Execute
        </button>
        <button style={{...styles.button, borderColor: themeColors.EDITOR_ACCENT_COLOR}} onClick={onCommit}>
          save
        </button>
        <button style={{...styles.button, borderColor: themeColors.EDITOR_ACCENT_COLOR}} onClick={closeEditor}>
          close
        </button>
      </div>
      <div style={styles.editorRoot}>
        <div ref={readOnlyContainer} style={styles.editorTab}></div>
        <div ref={rootContainer} style={styles.editorTab}></div>
      </div>
    </View>
  );
}

export default BindingEditor;

const styles = StyleSheet.create({
  menubar: {
    height: 20,
    display: 'flex',
    paddingTop: 2,
    width: '100%',
    justifyContent: 'flex-end',
  },
  editorRoot: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
  },
  editorTab: {
    width: '25vw',
    height: EDITOR_MAX_HEIGHT,
    overflowY: 'auto',
    flex: 1,
  },
  button: {
    cursor: 'pointer',
    // boxShadow: `${NORD_ACCENT_COLOR} 0px 0px 1px 0px`,
    borderStyle: 'solid',
    borderWidth: 1,
    borderRadius: 4,
    backgroundColor: "#363d47",
    color: "white",
    marginRight: 15
  },
});

