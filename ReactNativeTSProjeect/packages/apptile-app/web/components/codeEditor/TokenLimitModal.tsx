import React from 'react';
import {ThemeColors} from './themetype';

interface TokenLimitModalProps {
  themeColors: ThemeColors;
  onClose: () => void;
  isOpen: boolean;
}

const TokenLimitModal: React.FC<TokenLimitModalProps> = ({themeColors, onClose, isOpen}) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 400,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 200000, // Increased z-index to ensure it's above other elements
      }}
      onClick={onClose}>
      <div
        style={{
          backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
          borderRadius: '8px',
          padding: '20px',
          boxShadow: `0px 0px 15px 0px ${themeColors.EDITOR_ACCENT_COLOR}`,
          color: themeColors.EDITOR_FOREGROUND_COLOR,
          maxWidth: '500px',
          width: '60%',
          position: 'relative', // Ensure proper stacking context
          transform: 'translate(0, 0)', // Force GPU acceleration for better rendering
        }}
        onClick={e => e.stopPropagation()}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px',
          }}>
          <h2 style={{margin: 0, color: themeColors.EDITOR_FOREGROUND_COLOR}}>Token Limit Reached</h2>
          <button
            style={{
              background: 'none',
              border: 'none',
              color: themeColors.EDITOR_FOREGROUND_COLOR,
              fontSize: '20px',
              cursor: 'pointer',
            }}
            onClick={onClose}>
            ×
          </button>
        </div>
        <div style={{marginBottom: '20px'}}>
          <p>
            You've reached your token usage limit for this period. To continue using the AI features, you have the
            following options:
          </p>
          <ul style={{paddingLeft: '20px'}}>
            <li>Upgrade your subscription plan for more tokens</li>
            <li>Purchase additional tokens</li>
            <li>Wait until your token allocation refreshes</li>
          </ul>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '10px',
          }}>
          <button
            style={{
              backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
              borderStyle: 'solid',
              borderWidth: 1,
              borderColor: themeColors.EDITOR_ACCENT_COLOR,
              borderRadius: 4,
              color: themeColors.EDITOR_FOREGROUND_COLOR,
              padding: '8px 16px',
              cursor: 'pointer',
            }}
            onClick={onClose}>
            Close
          </button>
          <button
            style={{
              backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
              borderStyle: 'solid',
              borderWidth: 1,
              borderColor: themeColors.EDITOR_ACCENT_COLOR,
              borderRadius: 4,
              color: '#FFFFFF',
              padding: '8px 16px',
              cursor: 'pointer',
              fontWeight: 'bold',
            }}
            onClick={() => {
              // Open Slack to talk to Ankit
              // window.open('https://slack.com/app_redirect?channel=UXXXXXXXX', '_blank');
              onClose();
            }}>
            Talk to ankit
          </button>
        </div>
      </div>
    </div>
  );
};

export default TokenLimitModal;
