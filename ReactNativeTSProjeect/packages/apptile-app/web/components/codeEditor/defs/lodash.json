{"!name": "lodash", "_": {"chunk": {"!url": "https://lodash.com/docs/4.17.15#chunk", "!doc": "Creates an array of elements split into groups the length of size. If array can't be split evenly, the final chunk will be the remaining elements.", "!type": "fn(array, [size])"}, "compact": {"!url": "https://lodash.com/docs/4.17.15#compact", "!doc": "Creates an array with all falsey values removed. The values false, null, 0, \"\", undefined, and NaN are falsey.", "!type": "fn(array)"}, "concat": {"!url": "https://lodash.com/docs/4.17.15#concat", "!doc": "Creates a new array concatenating array with any additional arrays and/or values.", "!type": "fn(array, [values])"}, "difference": {"!url": "https://lodash.com/docs/4.17.15#difference", "!doc": "Creates an array of array values not included in the other given arrays using SameValueZero for equality comparisons. The order and references of result values are determined by the first array.Note: Unlike _.pullAll, this method returns a new array.", "!type": "fn(array, [values])"}, "differenceBy": {"!url": "https://lodash.com/docs/4.17.15#differenceBy", "!doc": "This method is like _.difference except that it accepts iteratee which is invoked for each element of array and values to generate the criterion by which they're compared. The order and references of result values are determined by the first array. The iteratee is invoked with one argument:(value).Note: Unlike _.pullAllBy, this method returns a new array.", "!type": "fn(array, [values], [iteratee])"}, "differenceWith": {"!url": "https://lodash.com/docs/4.17.15#differenceWith", "!doc": "This method is like _.difference except that it accepts comparator which is invoked to compare elements of array to values. The order and references of result values are determined by the first array. The comparator is invoked with two arguments: (arrVal, othVal).Note: Unlike _.pullAllWith, this method returns a new array.", "!type": "fn(array, [values], [comparator])"}, "drop": {"!url": "https://lodash.com/docs/4.17.15#drop", "!doc": "Creates a slice of array with n elements dropped from the beginning.", "!type": "fn(array, [n])"}, "dropRight": {"!url": "https://lodash.com/docs/4.17.15#dropRight", "!doc": "Creates a slice of array with n elements dropped from the end.", "!type": "fn(array, [n])"}, "dropRightWhile": {"!url": "https://lodash.com/docs/4.17.15#dropRightWhile", "!doc": "Creates a slice of array excluding elements dropped from the end. Elements are dropped until predicate returns falsey. The predicate is invoked with three arguments: (value, index, array).", "!type": "fn(array, [predicate])"}, "dropWhile": {"!url": "https://lodash.com/docs/4.17.15#dropWhile", "!doc": "Creates a slice of array excluding elements dropped from the beginning. Elements are dropped until predicate returns falsey. The predicate is invoked with three arguments: (value, index, array).", "!type": "fn(array, [predicate])"}, "fill": {"!url": "https://lodash.com/docs/4.17.15#fill", "!doc": "Fills elements of array with value from start up to, but not including, end.Note: This method mutates array.", "!type": "fn(array, value, [start], [end])"}, "findIndex": {"!url": "https://lodash.com/docs/4.17.15#findIndex", "!doc": "This method is like _.find except that it returns the index of the first element predicate returns truthy for instead of the element itself.", "!type": "fn(array, [predicate], [fromIndex])"}, "findLastIndex": {"!url": "https://lodash.com/docs/4.17.15#findLastIndex", "!doc": "This method is like _.findIndex except that it iterates over elements of collection from right to left.", "!type": "fn(array, [predicate], [fromIndex])"}, "flatten": {"!url": "https://lodash.com/docs/4.17.15#flatten", "!doc": "Flattens array a single level deep.", "!type": "fn(array)"}, "flattenDeep": {"!url": "https://lodash.com/docs/4.17.15#flattenDeep", "!doc": "Recursively flattens array.", "!type": "fn(array)"}, "flattenDepth": {"!url": "https://lodash.com/docs/4.17.15#flattenDepth", "!doc": "Recursively flatten array up to depth times.", "!type": "fn(array, [depth])"}, "fromPairs": {"!url": "https://lodash.com/docs/4.17.15#fromPairs", "!doc": "The inverse of _.toPairs; this method returns an object composed from key-value pairs.", "!type": "fn(pairs)"}, "head": {"!url": "https://lodash.com/docs/4.17.15#head", "!doc": "Gets the first element of array.", "!type": "fn(array)"}, "indexOf": {"!url": "https://lodash.com/docs/4.17.15#indexOf", "!doc": "Gets the index at which the first occurrence of value is found in array using SameValueZero for equality comparisons. If fromIndex is negative, it's used as the offset from the end of array.", "!type": "fn(array, value, [fromIndex])"}, "initial": {"!url": "https://lodash.com/docs/4.17.15#initial", "!doc": "Gets all but the last element of array.", "!type": "fn(array)"}, "intersection": {"!url": "https://lodash.com/docs/4.17.15#intersection", "!doc": "Creates an array of unique values that are included in all given arrays using SameValueZero for equality comparisons. The order and references of result values are determined by the first array.", "!type": "fn([arrays])"}, "intersectionBy": {"!url": "https://lodash.com/docs/4.17.15#intersectionBy", "!doc": "This method is like _.intersection except that it accepts iteratee which is invoked for each element of each arrays to generate the criterion by which they're compared. The order and references of result values are determined by the first array. The iteratee is invoked with one argument:(value).", "!type": "fn([arrays], [iteratee])"}, "intersectionWith": {"!url": "https://lodash.com/docs/4.17.15#intersectionWith", "!doc": "This method is like _.intersection except that it accepts comparator which is invoked to compare elements of arrays. The order and references of result values are determined by the first array. The comparator is invoked with two arguments: (arrVal, othVal).", "!type": "fn([arrays], [comparator])"}, "join": {"!url": "https://lodash.com/docs/4.17.15#join", "!doc": "Converts all elements in array into a string separated by separator.", "!type": "fn(array, [separator])"}, "last": {"!url": "https://lodash.com/docs/4.17.15#last", "!doc": "Gets the last element of array.", "!type": "fn(array)"}, "lastIndexOf": {"!url": "https://lodash.com/docs/4.17.15#lastIndexOf", "!doc": "This method is like _.indexOf except that it iterates over elements of array from right to left.", "!type": "fn(array, value, [fromIndex])"}, "nth": {"!url": "https://lodash.com/docs/4.17.15#nth", "!doc": "Gets the element at index n of array. If n is negative, the nth element from the end is returned.", "!type": "fn(array, [n])"}, "pull": {"!url": "https://lodash.com/docs/4.17.15#pull", "!doc": "Removes all given values from array using SameValueZero for equality comparisons.Note: Unlike _.without, this method mutates array. Use _.remove to remove elements from an array by predicate.", "!type": "fn(array, [values])"}, "pullAll": {"!url": "https://lodash.com/docs/4.17.15#pullAll", "!doc": "This method is like _.pull except that it accepts an array of values to remove.Note: Unlike _.difference, this method mutates array.", "!type": "fn(array, values)"}, "pullAllBy": {"!url": "https://lodash.com/docs/4.17.15#pullAllBy", "!doc": "This method is like _.pullAll except that it accepts iteratee which is invoked for each element of array and values to generate the criterion by which they're compared. The iteratee is invoked with one argument: (value).Note: Unlike _.differenceBy, this method mutates array.", "!type": "fn(array, values, [iteratee])"}, "pullAllWith": {"!url": "https://lodash.com/docs/4.17.15#pullAllWith", "!doc": "This method is like _.pullAll except that it accepts comparator which is invoked to compare elements of array to values. The comparator is invoked with two arguments: (arrVal, othVal).Note: Unlike _.differenceWith, this method mutates array.", "!type": "fn(array, values, [comparator])"}, "pullAt": {"!url": "https://lodash.com/docs/4.17.15#pullAt", "!doc": "Removes elements from array corresponding to indexes and returns an array of removed elements.Note: Unlike _.at, this method mutates array.", "!type": "fn(array, [indexes])"}, "remove": {"!url": "https://lodash.com/docs/4.17.15#remove", "!doc": "Removes all elements from array that predicate returns truthy for and returns an array of the removed elements. The predicate is invoked with three arguments: (value, index, array).Note: Unlike _.filter, this method mutates array. Use _.pull to pull elements from an array by value.", "!type": "fn(array, [predicate])"}, "reverse": {"!url": "https://lodash.com/docs/4.17.15#reverse", "!doc": "Reverses array so that the first element becomes the last, the second element becomes the second to last, and so on.Note: This method mutates array and is based on Array#reverse.", "!type": "fn(array)"}, "slice": {"!url": "https://lodash.com/docs/4.17.15#slice", "!doc": "Creates a slice of array from start up to, but not including, end.Note: This method is used instead of Array#slice to ensure dense arrays are returned.", "!type": "fn(array, [start], [end])"}, "sortedIndex": {"!url": "https://lodash.com/docs/4.17.15#sortedIndex", "!doc": "Uses a binary search to determine the lowest index at which value should be inserted into array in order to maintain its sort order.", "!type": "fn(array, value)"}, "sortedIndexBy": {"!url": "https://lodash.com/docs/4.17.15#sortedIndexBy", "!doc": "This method is like _.sortedIndex except that it accepts iteratee which is invoked for value and each element of array to compute their sort ranking. The iteratee is invoked with one argument: (value).", "!type": "fn(array, value, [iteratee])"}, "sortedIndexOf": {"!url": "https://lodash.com/docs/4.17.15#sortedIndexOf", "!doc": "This method is like _.indexOf except that it performs a binary search on a sorted array.", "!type": "fn(array, value)"}, "sortedLastIndex": {"!url": "https://lodash.com/docs/4.17.15#sortedLastIndex", "!doc": "This method is like _.sortedIndex except that it returns the highest index at which value should be inserted into array in order to maintain its sort order.", "!type": "fn(array, value)"}, "sortedLastIndexBy": {"!url": "https://lodash.com/docs/4.17.15#sortedLastIndexBy", "!doc": "This method is like _.sortedLastIndex except that it accepts iteratee which is invoked for value and each element of array to compute their sort ranking. The iteratee is invoked with one argument: (value).", "!type": "fn(array, value, [iteratee])"}, "sortedLastIndexOf": {"!url": "https://lodash.com/docs/4.17.15#sortedLastIndexOf", "!doc": "This method is like _.lastIndexOf except that it performs a binary search on a sorted array.", "!type": "fn(array, value)"}, "sortedUniq": {"!url": "https://lodash.com/docs/4.17.15#sortedUniq", "!doc": "This method is like _.uniq except that it's designed and optimized for sorted arrays.", "!type": "fn(array)"}, "sortedUniqBy": {"!url": "https://lodash.com/docs/4.17.15#sortedUniqBy", "!doc": "This method is like _.uniqBy except that it's designed and optimized for sorted arrays.", "!type": "fn(array, [iteratee])"}, "tail": {"!url": "https://lodash.com/docs/4.17.15#tail", "!doc": "Gets all but the first element of array.", "!type": "fn(array)"}, "take": {"!url": "https://lodash.com/docs/4.17.15#take", "!doc": "Creates a slice of array with n elements taken from the beginning.", "!type": "fn(array, [n])"}, "takeRight": {"!url": "https://lodash.com/docs/4.17.15#takeRight", "!doc": "Creates a slice of array with n elements taken from the end.", "!type": "fn(array, [n])"}, "takeRightWhile": {"!url": "https://lodash.com/docs/4.17.15#takeRightWhile", "!doc": "Creates a slice of array with elements taken from the end. Elements are taken until predicate returns falsey. The predicate is invoked with three arguments: (value, index, array).", "!type": "fn(array, [predicate])"}, "takeWhile": {"!url": "https://lodash.com/docs/4.17.15#takeWhile", "!doc": "Creates a slice of array with elements taken from the beginning. Elements are taken until predicate returns falsey. The predicate is invoked with three arguments: (value, index, array).", "!type": "fn(array, [predicate])"}, "union": {"!url": "https://lodash.com/docs/4.17.15#union", "!doc": "Creates an array of unique values, in order, from all given arrays using SameValueZero for equality comparisons.", "!type": "fn([arrays])"}, "unionBy": {"!url": "https://lodash.com/docs/4.17.15#unionBy", "!doc": "This method is like _.union except that it accepts iteratee which is invoked for each element of each arrays to generate the criterion by which uniqueness is computed. Result values are chosen from the first array in which the value occurs. The iteratee is invoked with one argument:(value).", "!type": "fn([arrays], [iteratee])"}, "unionWith": {"!url": "https://lodash.com/docs/4.17.15#unionWith", "!doc": "This method is like _.union except that it accepts comparator which is invoked to compare elements of arrays. Result values are chosen from the first array in which the value occurs. The comparator is invoked with two arguments: (arrVal, othVal).", "!type": "fn([arrays], [comparator])"}, "uniq": {"!url": "https://lodash.com/docs/4.17.15#uniq", "!doc": "Creates a duplicate-free version of an array, using SameValueZero for equality comparisons, in which only the first occurrence of each element is kept. The order of result values is determined by the order they occur in the array.", "!type": "fn(array)"}, "uniqBy": {"!url": "https://lodash.com/docs/4.17.15#uniqBy", "!doc": "This method is like _.uniq except that it accepts iteratee which is invoked for each element in array to generate the criterion by which uniqueness is computed. The order of result values is determined by the order they occur in the array. The iteratee is invoked with one argument:(value).", "!type": "fn(array, [iteratee])"}, "uniqWith": {"!url": "https://lodash.com/docs/4.17.15#uniqWith", "!doc": "This method is like _.uniq except that it accepts comparator which is invoked to compare elements of array. The order of result values is determined by the order they occur in the array.The comparator is invoked with two arguments: (arrVal, othVal).", "!type": "fn(array, [comparator])"}, "unzip": {"!url": "https://lodash.com/docs/4.17.15#unzip", "!doc": "This method is like _.zip except that it accepts an array of grouped elements and creates an array regrouping the elements to their pre-zip configuration.", "!type": "fn(array)"}, "unzipWith": {"!url": "https://lodash.com/docs/4.17.15#unzipWith", "!doc": "This method is like _.unzip except that it accepts iteratee to specify how regrouped values should be combined. The iteratee is invoked with the elements of each group: (...group).", "!type": "fn(array, [iteratee])"}, "without": {"!url": "https://lodash.com/docs/4.17.15#without", "!doc": "Creates an array excluding all given values using SameValueZero for equality comparisons.Note: Unlike _.pull, this method returns a new array.", "!type": "fn(array, [values])"}, "xor": {"!url": "https://lodash.com/docs/4.17.15#xor", "!doc": "Creates an array of unique values that is the symmetric difference of the given arrays. The order of result values is determined by the order they occur in the arrays.", "!type": "fn([arrays])"}, "xorBy": {"!url": "https://lodash.com/docs/4.17.15#xorBy", "!doc": "This method is like _.xor except that it accepts iteratee which is invoked for each element of each arrays to generate the criterion by which by which they're compared. The order of result values is determined by the order they occur in the arrays. The iteratee is invoked with one argument: (value).", "!type": "fn([arrays], [iteratee])"}, "xorWith": {"!url": "https://lodash.com/docs/4.17.15#xorWith", "!doc": "This method is like _.xor except that it accepts comparator which is invoked to compare elements of arrays. The order of result values is determined by the order they occur in the arrays. The comparator is invoked with two arguments: (arrVal, othVal).", "!type": "fn([arrays], [comparator])"}, "zip": {"!url": "https://lodash.com/docs/4.17.15#zip", "!doc": "Creates an array of grouped elements, the first of which contains the first elements of the given arrays, the second of which contains the second elements of the given arrays, and so on.", "!type": "fn([arrays])"}, "zipObject": {"!url": "https://lodash.com/docs/4.17.15#zipObject", "!doc": "This method is like _.fromPairs except that it accepts two arrays, one of property identifiers and one of corresponding values.", "!type": "fn([props], [values])"}, "zipObjectDeep": {"!url": "https://lodash.com/docs/4.17.15#zipObjectDeep", "!doc": "This method is like _.zipObject except that it supports property paths.", "!type": "fn([props], [values])"}, "zipWith": {"!url": "https://lodash.com/docs/4.17.15#zipWith", "!doc": "This method is like _.zip except that it accepts iteratee to specify how grouped values should be combined. The iteratee is invoked with the elements of each group: (...group).", "!type": "fn([arrays], [iteratee])"}, "countBy": {"!url": "https://lodash.com/docs/4.17.15#countBy", "!doc": "Creates an object composed of keys generated from the results of running each element of collection thru iteratee. The corresponding value of each key is the number of times the key was returned by iteratee. The iteratee is invoked with one argument: (value).", "!type": "fn(collection, [iteratee])"}, "every": {"!url": "https://lodash.com/docs/4.17.15#every", "!doc": "Checks if predicate returns truthy for all elements of collection. Iteration is stopped once predicate returns falsey. The predicate is invoked with three arguments: (value, index|key, collection).Note: This method returns true for empty collections because everything is true of elements of empty collections.", "!type": "fn(collection, [predicate])"}, "filter": {"!url": "https://lodash.com/docs/4.17.15#filter", "!doc": "Iterates over elements of collection, returning an array of all elements predicate returns truthy for. The predicate is invoked with three arguments: (value, index|key, collection).Note: Unlike _.remove, this method returns a new array.", "!type": "fn(collection, [predicate])"}, "find": {"!url": "https://lodash.com/docs/4.17.15#find", "!doc": "Iterates over elements of collection, returning the first element predicate returns truthy for. The predicate is invoked with three arguments: (value, index|key, collection).", "!type": "fn(collection, [predicate], [fromIndex])"}, "findLast": {"!url": "https://lodash.com/docs/4.17.15#findLast", "!doc": "This method is like _.find except that it iterates over elements of collection from right to left.", "!type": "fn(collection, [predicate], [fromIndex])"}, "flatMap": {"!url": "https://lodash.com/docs/4.17.15#flatMap", "!doc": "Creates a flattened array of values by running each element in collection thru iteratee and flattening the mapped results. The iteratee is invoked with three arguments: (value, index|key, collection).", "!type": "fn(collection, [iteratee])"}, "flatMapDeep": {"!url": "https://lodash.com/docs/4.17.15#flatMapDeep", "!doc": "This method is like _.flatMap except that it recursively flattens the mapped results.", "!type": "fn(collection, [iteratee])"}, "flatMapDepth": {"!url": "https://lodash.com/docs/4.17.15#flatMapDepth", "!doc": "This method is like _.flatMap except that it recursively flattens the mapped results up to depth times.", "!type": "fn(collection, [iteratee], [depth])"}, "forEach": {"!url": "https://lodash.com/docs/4.17.15#forEach", "!doc": "Iterates over elements of collection and invokes iteratee for each element. The iteratee is invoked with three arguments: (value, index|key, collection). Iteratee functions may exit iteration early by explicitly returning false.Note: As with other \"Collections\" methods, objects with a \"length\" property are iterated like arrays. To avoid this behavior use _.forIn or _.forOwn for object iteration.", "!type": "fn(collection, [iteratee])"}, "forEachRight": {"!url": "https://lodash.com/docs/4.17.15#forEachRight", "!doc": "This method is like _.forEach except that it iterates over elements of collection from right to left.", "!type": "fn(collection, [iteratee])"}, "groupBy": {"!url": "https://lodash.com/docs/4.17.15#groupBy", "!doc": "Creates an object composed of keys generated from the results of running each element of collection thru iteratee. The order of grouped values is determined by the order they occur in collection. The corresponding value of each key is an array of elements responsible for generating the key. The iteratee is invoked with one argument: (value).", "!type": "fn(collection, [iteratee])"}, "includes": {"!url": "https://lodash.com/docs/4.17.15#includes", "!doc": "Checks if value is in collection. If collection is a string, it's checked for a substring of value, otherwise SameValueZero is used for equality comparisons. If fromIndex is negative, it's used as the offset from the end of collection.", "!type": "fn(collection, value, [fromIndex])"}, "invokeMap": {"!url": "https://lodash.com/docs/4.17.15#invokeMap", "!doc": "Invokes the method at path of each element in collection, returning an array of the results of each invoked method. Any additional arguments are provided to each invoked method. If path is a function, it's invoked for, and this bound to, each element in collection.", "!type": "fn(collection, path, [args])"}, "keyBy": {"!url": "https://lodash.com/docs/4.17.15#keyBy", "!doc": "Creates an object composed of keys generated from the results of running each element of collection thru iteratee. The corresponding value of each key is the last element responsible for generating the key. The iteratee is invoked with one argument: (value).", "!type": "fn(collection, [iteratee])"}, "map": {"!url": "https://lodash.com/docs/4.17.15#map", "!doc": "Creates an array of values by running each element in collection thru iteratee. The iteratee is invoked with three arguments:(value, index|key, collection).Many lodash methods are guarded to work as iteratees for methods like _.every, _.filter, _.map, _.mapValues, _.reject, and _.some.The guarded methods are:ary, chunk, curry, curryRight, drop, dropRight, every, fill, invert, parseInt, random, range, rangeRight, repeat, sampleSize, slice, some, sortBy, split, take, takeRight, template, trim, trimEnd, trimStart, and words", "!type": "fn(collection, [iteratee])"}, "orderBy": {"!url": "https://lodash.com/docs/4.17.15#orderBy", "!doc": "This method is like _.sortBy except that it allows specifying the sort orders of the iteratees to sort by. If orders is unspecified, all values are sorted in ascending order. Otherwise, specify an order of \"desc\" for descending or \"asc\" for ascending sort order of corresponding values.", "!type": "fn(collection, [iteratees], [orders])"}, "partition": {"!url": "https://lodash.com/docs/4.17.15#partition", "!doc": "Creates an array of elements split into two groups, the first of which contains elements predicate returns truthy for, the second of which contains elements predicate returns falsey for. The predicate is invoked with one argument: (value).", "!type": "fn(collection, [predicate])"}, "reduce": {"!url": "https://lodash.com/docs/4.17.15#reduce", "!doc": "Reduces collection to a value which is the accumulated result of running each element in collection thru iteratee, where each successive invocation is supplied the return value of the previous. If accumulator is not given, the first element of collection is used as the initial value. The iteratee is invoked with four arguments:(accumulator, value, index|key, collection).Many lodash methods are guarded to work as iteratees for methods like _.reduce, _.reduceRight, and _.transform.The guarded methods are:assign, defaults, defaultsDeep, includes, merge, orderBy, and sortBy", "!type": "fn(collection, [iteratee], [accumulator])"}, "reduceRight": {"!url": "https://lodash.com/docs/4.17.15#reduceRight", "!doc": "This method is like _.reduce except that it iterates over elements of collection from right to left.", "!type": "fn(collection, [iteratee], [accumulator])"}, "reject": {"!url": "https://lodash.com/docs/4.17.15#reject", "!doc": "The opposite of _.filter; this method returns the elements of collection that predicate does not return truthy for.", "!type": "fn(collection, [predicate])"}, "sample": {"!url": "https://lodash.com/docs/4.17.15#sample", "!doc": "Gets a random element from collection.", "!type": "fn(collection)"}, "sampleSize": {"!url": "https://lodash.com/docs/4.17.15#sampleSize", "!doc": "Gets n random elements at unique keys from collection up to the size of collection.", "!type": "fn(collection, [n])"}, "shuffle": {"!url": "https://lodash.com/docs/4.17.15#shuffle", "!doc": "Creates an array of shuffled values, using a version of the <PERSON><PERSON><PERSON> shuffle.", "!type": "fn(collection)"}, "size": {"!url": "https://lodash.com/docs/4.17.15#size", "!doc": "Gets the size of collection by returning its length for array-like values or the number of own enumerable string keyed properties for objects.", "!type": "fn(collection)"}, "some": {"!url": "https://lodash.com/docs/4.17.15#some", "!doc": "Checks if predicate returns truthy for any element of collection. Iteration is stopped once predicate returns truthy. The predicate is invoked with three arguments: (value, index|key, collection).", "!type": "fn(collection, [predicate])"}, "sortBy": {"!url": "https://lodash.com/docs/4.17.15#sortBy", "!doc": "Creates an array of elements, sorted in ascending order by the results of running each element in a collection thru each iteratee. This method performs a stable sort, that is, it preserves the original sort order of equal elements. The iteratees are invoked with one argument: (value).", "!type": "fn(collection, [iteratees])"}, "now": {"!url": "https://lodash.com/docs/4.17.15#now", "!doc": "Gets the timestamp of the number of milliseconds that have elapsed since the Unix epoch (1 January 1970 00:00:00 UTC).", "!type": "fn()"}, "after": {"!url": "https://lodash.com/docs/4.17.15#after", "!doc": "The opposite of _.before; this method creates a function that invokes func once it's called n or more times.", "!type": "fn(n, func)"}, "ary": {"!url": "https://lodash.com/docs/4.17.15#ary", "!doc": "Creates a function that invokes func, with up to n arguments, ignoring any additional arguments.", "!type": "fn(func, [n])"}, "before": {"!url": "https://lodash.com/docs/4.17.15#before", "!doc": "Creates a function that invokes func, with the this binding and arguments of the created function, while it's called less than n times. Subsequent calls to the created function return the result of the last func invocation.", "!type": "fn(n, func)"}, "bind": {"!url": "https://lodash.com/docs/4.17.15#bind", "!doc": "Creates a function that invokes func with the this binding of thisArg and partials prepended to the arguments it receives.The _.bind.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for partially applied arguments.Note: Unlike native Function#bind, this method doesn't set the \"length\" property of bound functions.", "!type": "fn(func, thisArg, [partials])"}, "bindKey": {"!url": "https://lodash.com/docs/4.17.15#bindKey", "!doc": "Creates a function that invokes the method at object[key] with partials prepended to the arguments it receives.This method differs from _.bind by allowing bound functions to reference methods that may be redefined or don't yet exist. See <PERSON>'s article for more details.The _.bindKey.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for partially applied arguments.", "!type": "fn(object, key, [partials])"}, "curry": {"!url": "https://lodash.com/docs/4.17.15#curry", "!doc": "Creates a function that accepts arguments of func and either invokes func returning its result, if at least arity number of arguments have been provided, or returns a function that accepts the remaining func arguments, and so on. The arity of func may be specified if func.length is not sufficient.The _.curry.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for provided arguments.Note: This method doesn't set the \"length\" property of curried functions.", "!type": "fn(func, [arity])"}, "curryRight": {"!url": "https://lodash.com/docs/4.17.15#curryRight", "!doc": "This method is like _.curry except that arguments are applied to func in the manner of _.partialRight instead of _.partial.The _.curryRight.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for provided arguments.Note: This method doesn't set the \"length\" property of curried functions.", "!type": "fn(func, [arity])"}, "debounce": {"!url": "https://lodash.com/docs/4.17.15#debounce", "!doc": "Creates a debounced function that delays invoking func until after wait milliseconds have elapsed since the last time the debounced function was invoked. The debounced function comes with a cancel method to cancel delayed func invocations and a flush method to immediately invoke them. Provide options to indicate whether func should be invoked on the leading and/or trailing edge of the wait timeout. The func is invoked with the last arguments provided to the debounced function. Subsequent calls to the debounced function return the result of the last func invocation.Note: If leading and trailing options are true, func is invoked on the trailing edge of the timeout only if the debounced function is invoked more than once during the wait timeout.If wait is 0 and leading is false, func invocation is deferred until to the next tick, similar to setTimeout with a timeout of 0.See <PERSON>'s article for details over the differences between _.debounce and _.throttle.", "!type": "fn(func, [wait], [options])"}, "defer": {"!url": "https://lodash.com/docs/4.17.15#defer", "!doc": "Defers invoking the func until the current call stack has cleared. Any additional arguments are provided to func when it's invoked.", "!type": "fn(func, [args])"}, "delay": {"!url": "https://lodash.com/docs/4.17.15#delay", "!doc": "Invokes func after wait milliseconds. Any additional arguments are provided to func when it's invoked.", "!type": "fn(func, wait, [args])"}, "flip": {"!url": "https://lodash.com/docs/4.17.15#flip", "!doc": "Creates a function that invokes func with arguments reversed.", "!type": "fn(func)"}, "memoize": {"!url": "https://lodash.com/docs/4.17.15#memoize", "!doc": "Creates a function that memoizes the result of func. If resolver is provided, it determines the cache key for storing the result based on the arguments provided to the memoized function. By default, the first argument provided to the memoized function is used as the map cache key. The func is invoked with the this binding of the memoized function.Note: The cache is exposed as the cache property on the memoized function. Its creation may be customized by replacing the _.memoize.Cache constructor with one whose instances implement the Map method interface of clear, delete, get, has, and set.", "!type": "fn(func, [resolver])"}, "negate": {"!url": "https://lodash.com/docs/4.17.15#negate", "!doc": "Creates a function that negates the result of the predicate func. The func predicate is invoked with the this binding and arguments of the created function.", "!type": "fn(predicate)"}, "once": {"!url": "https://lodash.com/docs/4.17.15#once", "!doc": "Creates a function that is restricted to invoking func once. <PERSON><PERSON> calls to the function return the value of the first invocation. The func is invoked with the this binding and arguments of the created function.", "!type": "fn(func)"}, "overArgs": {"!url": "https://lodash.com/docs/4.17.15#overArgs", "!doc": "Creates a function that invokes func with its arguments transformed.", "!type": "fn(func, [transforms])"}, "partial": {"!url": "https://lodash.com/docs/4.17.15#partial", "!doc": "Creates a function that invokes func with partials prepended to the arguments it receives. This method is like _.bind except it does not alter the this binding.The _.partial.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for partially applied arguments.Note: This method doesn't set the \"length\" property of partially applied functions.", "!type": "fn(func, [partials])"}, "partialRight": {"!url": "https://lodash.com/docs/4.17.15#partialRight", "!doc": "This method is like _.partial except that partially applied arguments are appended to the arguments it receives.The _.partialRight.placeholder value, which defaults to _ in monolithic builds, may be used as a placeholder for partially applied arguments.Note: This method doesn't set the \"length\" property of partially applied functions.", "!type": "fn(func, [partials])"}, "rearg": {"!url": "https://lodash.com/docs/4.17.15#rearg", "!doc": "Creates a function that invokes func with arguments arranged according to the specified indexes where the argument value at the first index is provided as the first argument, the argument value at the second index is provided as the second argument, and so on.", "!type": "fn(func, indexes)"}, "rest": {"!url": "https://lodash.com/docs/4.17.15#rest", "!doc": "Creates a function that invokes func with the this binding of the created function and arguments from start and beyond provided as an array.Note: This method is based on the rest parameter.", "!type": "fn(func, [start])"}, "spread": {"!url": "https://lodash.com/docs/4.17.15#spread", "!doc": "Creates a function that invokes func with the this binding of the create function and an array of arguments much like Function#apply.Note: This method is based on the spread operator.", "!type": "fn(func, [start])"}, "throttle": {"!url": "https://lodash.com/docs/4.17.15#throttle", "!doc": "Creates a throttled function that only invokes func at most once per every wait milliseconds. The throttled function comes with a cancel method to cancel delayed func invocations and a flush method to immediately invoke them. Provide options to indicate whether func should be invoked on the leading and/or trailing edge of the wait timeout. The func is invoked with the last arguments provided to the throttled function. Subsequent calls to the throttled function return the result of the last func invocation.Note: If leading and trailing options are true, func is invoked on the trailing edge of the timeout only if the throttled function is invoked more than once during the wait timeout.If wait is 0 and leading is false, func invocation is deferred until to the next tick, similar to setTimeout with a timeout of 0.See <PERSON>'s article for details over the differences between _.throttle and _.debounce.", "!type": "fn(func, [wait], [options])"}, "unary": {"!url": "https://lodash.com/docs/4.17.15#unary", "!doc": "Creates a function that accepts up to one argument, ignoring any additional arguments.", "!type": "fn(func)"}, "wrap": {"!url": "https://lodash.com/docs/4.17.15#wrap", "!doc": "Creates a function that provides value to wrapper as its first argument. Any additional arguments provided to the function are appended to those provided to the wrapper. The wrapper is invoked with the this binding of the created function.", "!type": "fn(value, [wrapper])"}, "castArray": {"!url": "https://lodash.com/docs/4.17.15#castArray", "!doc": "Casts value as an array if it's not one.", "!type": "fn(value)"}, "clone": {"!url": "https://lodash.com/docs/4.17.15#clone", "!doc": "Creates a shallow clone of value.Note: This method is loosely based on the structured clone algorithm and supports cloning arrays, array buffers, booleans, date objects, maps, numbers, Object objects, regexes, sets, strings, symbols, and typed arrays. The own enumerable properties of arguments objects are cloned as plain objects. An empty object is returned for uncloneable values such as error objects, functions, DOM nodes, and WeakMaps.", "!type": "fn(value)"}, "cloneDeep": {"!url": "https://lodash.com/docs/4.17.15#cloneDeep", "!doc": "This method is like _.clone except that it recursively clones value.", "!type": "fn(value)"}, "cloneDeepWith": {"!url": "https://lodash.com/docs/4.17.15#cloneDeepWith", "!doc": "This method is like _.cloneWith except that it recursively clones value.", "!type": "fn(value, [customizer])"}, "cloneWith": {"!url": "https://lodash.com/docs/4.17.15#cloneWith", "!doc": "This method is like _.clone except that it accepts customizer which is invoked to produce the cloned value. If customizer returns undefined, cloning is handled by the method instead. The customizer is invoked with up to four arguments; (value [, index|key, object, stack]).", "!type": "fn(value, [customizer])"}, "conformsTo": {"!url": "https://lodash.com/docs/4.17.15#conformsTo", "!doc": "Checks if object conforms to source by invoking the predicate properties of source with the corresponding property values of object.Note: This method is equivalent to _.conforms when source is partially applied.", "!type": "fn(object, source)"}, "eq": {"!url": "https://lodash.com/docs/4.17.15#eq", "!doc": "Performs a SameValueZero comparison between two values to determine if they are equivalent.", "!type": "fn(value, other)"}, "gt": {"!url": "https://lodash.com/docs/4.17.15#gt", "!doc": "Checks if value is greater than other.", "!type": "fn(value, other)"}, "gte": {"!url": "https://lodash.com/docs/4.17.15#gte", "!doc": "Checks if value is greater than or equal to other.", "!type": "fn(value, other)"}, "isArguments": {"!url": "https://lodash.com/docs/4.17.15#isArguments", "!doc": "Checks if value is likely an arguments object.", "!type": "fn(value)"}, "isArray": {"!url": "https://lodash.com/docs/4.17.15#isArray", "!doc": "Checks if value is classified as an Array object.", "!type": "fn(value)"}, "isArrayBuffer": {"!url": "https://lodash.com/docs/4.17.15#isArrayBuffer", "!doc": "Checks if value is classified as an ArrayBuffer object.", "!type": "fn(value)"}, "isArrayLike": {"!url": "https://lodash.com/docs/4.17.15#isArrayLike", "!doc": "Checks if value is array-like. A value is considered array-like if it's not a function and has a value.length that's an integer greater than or equal to 0 and less than or equal to Number.MAX_SAFE_INTEGER.", "!type": "fn(value)"}, "isArrayLikeObject": {"!url": "https://lodash.com/docs/4.17.15#isArrayLikeObject", "!doc": "This method is like _.isArrayLike except that it also checks if value is an object.", "!type": "fn(value)"}, "isBoolean": {"!url": "https://lodash.com/docs/4.17.15#isBoolean", "!doc": "Checks if value is classified as a boolean primitive or object.", "!type": "fn(value)"}, "isBuffer": {"!url": "https://lodash.com/docs/4.17.15#isBuffer", "!doc": "Checks if value is a buffer.", "!type": "fn(value)"}, "isDate": {"!url": "https://lodash.com/docs/4.17.15#isDate", "!doc": "Checks if value is classified as a Date object.", "!type": "fn(value)"}, "isElement": {"!url": "https://lodash.com/docs/4.17.15#isElement", "!doc": "Checks if value is likely a DOM element.", "!type": "fn(value)"}, "isEmpty": {"!url": "https://lodash.com/docs/4.17.15#isEmpty", "!doc": "Checks if value is an empty object, collection, map, or set.Objects are considered empty if they have no own enumerable string keyed properties.Array-like values such as arguments objects, arrays, buffers, strings, or jQuery-like collections are considered empty if they have a length of 0. Similarly, maps and sets are considered empty if they have a size of 0.", "!type": "fn(value)"}, "isEqual": {"!url": "https://lodash.com/docs/4.17.15#isEqual", "!doc": "Performs a deep comparison between two values to determine if they are equivalent.Note: This method supports comparing arrays, array buffers, booleans, date objects, error objects, maps, numbers, Object objects, regexes, sets, strings, symbols, and typed arrays. Object objects are compared by their own, not inherited, enumerable properties. Functions and DOM nodes are compared by strict equality, i.e. ===.", "!type": "fn(value, other)"}, "isEqualWith": {"!url": "https://lodash.com/docs/4.17.15#isEqualWith", "!doc": "This method is like _.isEqual except that it accepts customizer which is invoked to compare values. If customizer returns undefined, comparisons are handled by the method instead. The customizer is invoked with up to six arguments: (objValue, othValue [, index|key, object, other, stack]).", "!type": "fn(value, other, [customizer])"}, "isError": {"!url": "https://lodash.com/docs/4.17.15#isError", "!doc": "Checks if value is an Error, EvalError, RangeError, ReferenceError, SyntaxError, TypeError, or URIError object.", "!type": "fn(value)"}, "isFinite": {"!url": "https://lodash.com/docs/4.17.15#isFinite", "!doc": "Checks if value is a finite primitive number.Note: This method is based on Number.isFinite.", "!type": "fn(value)"}, "isFunction": {"!url": "https://lodash.com/docs/4.17.15#isFunction", "!doc": "Checks if value is classified as a Function object.", "!type": "fn(value)"}, "isInteger": {"!url": "https://lodash.com/docs/4.17.15#isInteger", "!doc": "Checks if value is an integer.Note: This method is based on Number.isInteger.", "!type": "fn(value)"}, "isLength": {"!url": "https://lodash.com/docs/4.17.15#isLength", "!doc": "Checks if value is a valid array-like length.Note: This method is loosely based on ToLength.", "!type": "fn(value)"}, "isMap": {"!url": "https://lodash.com/docs/4.17.15#isMap", "!doc": "Checks if value is classified as a Map object.", "!type": "fn(value)"}, "isMatch": {"!url": "https://lodash.com/docs/4.17.15#isMatch", "!doc": "Performs a partial deep comparison between object and source to determine if object contains equivalent property values.Note: This method is equivalent to _.matches when source is partially applied.Partial comparisons will match empty array and empty object source values against any array or object value, respectively. See _.isEqual for a list of supported value comparisons.", "!type": "fn(object, source)"}, "isMatchWith": {"!url": "https://lodash.com/docs/4.17.15#isMatchWith", "!doc": "This method is like _.isMatch except that it accepts customizer which is invoked to compare values. If customizer returns undefined, comparisons are handled by the method instead. The customizer is invoked with five arguments: (objValue, srcValue, index|key, object, source).", "!type": "fn(object, source, [customizer])"}, "isNaN": {"!url": "https://lodash.com/docs/4.17.15#isNaN", "!doc": "Checks if value is NaN.Note: This method is based on Number.isNaN and is not the same as global isNaN which returns true for undefined and other non-number values.", "!type": "fn(value)"}, "isNative": {"!url": "https://lodash.com/docs/4.17.15#isNative", "!doc": "Checks if value is a pristine native function.Note: This method can't reliably detect native functions in the presence of the core-js package because core-js circumvents this kind of detection. Despite multiple requests, the core-js maintainer has made it clear: any attempt to fix the detection will be obstructed. As a result, we're left with little choice but to throw an error. Unfortunately, this also affects packages, like babel-polyfill, which rely on core-js.", "!type": "fn(value)"}, "isNil": {"!url": "https://lodash.com/docs/4.17.15#isNil", "!doc": "Checks if value is null or undefined.", "!type": "fn(value)"}, "isNull": {"!url": "https://lodash.com/docs/4.17.15#isNull", "!doc": "Checks if value is null.", "!type": "fn(value)"}, "isNumber": {"!url": "https://lodash.com/docs/4.17.15#isNumber", "!doc": "Checks if value is classified as a Number primitive or object.Note: To exclude Infinity, -Infinity, and NaN, which are classified as numbers, use the _.isFinite method.", "!type": "fn(value)"}, "isObject": {"!url": "https://lodash.com/docs/4.17.15#isObject", "!doc": "Checks if value is the language type of Object. (e.g. arrays, functions, objects, regexes, new Number(0), and new String(''))", "!type": "fn(value)"}, "isObjectLike": {"!url": "https://lodash.com/docs/4.17.15#isObjectLike", "!doc": "Checks if value is object-like. A value is object-like if it's not null and has a typeof result of \"object\".", "!type": "fn(value)"}, "isPlainObject": {"!url": "https://lodash.com/docs/4.17.15#isPlainObject", "!doc": "Checks if value is a plain object, that is, an object created by the Object constructor or one with a [[Prototype]] of null.", "!type": "fn(value)"}, "isRegExp": {"!url": "https://lodash.com/docs/4.17.15#isRegExp", "!doc": "Checks if value is classified as a RegExp object.", "!type": "fn(value)"}, "isSafeInteger": {"!url": "https://lodash.com/docs/4.17.15#isSafeInteger", "!doc": "Checks if value is a safe integer. An integer is safe if it's an IEEE-754 double precision number which isn't the result of a rounded unsafe integer.Note: This method is based on Number.isSafeInteger.", "!type": "fn(value)"}, "isSet": {"!url": "https://lodash.com/docs/4.17.15#isSet", "!doc": "Checks if value is classified as a Set object.", "!type": "fn(value)"}, "isString": {"!url": "https://lodash.com/docs/4.17.15#isString", "!doc": "Checks if value is classified as a String primitive or object.", "!type": "fn(value)"}, "isSymbol": {"!url": "https://lodash.com/docs/4.17.15#isSymbol", "!doc": "Checks if value is classified as a Symbol primitive or object.", "!type": "fn(value)"}, "isTypedArray": {"!url": "https://lodash.com/docs/4.17.15#isTypedArray", "!doc": "Checks if value is classified as a typed array.", "!type": "fn(value)"}, "isUndefined": {"!url": "https://lodash.com/docs/4.17.15#isUndefined", "!doc": "Checks if value is undefined.", "!type": "fn(value)"}, "isWeakMap": {"!url": "https://lodash.com/docs/4.17.15#isWeakMap", "!doc": "Checks if value is classified as a WeakMap object.", "!type": "fn(value)"}, "isWeakSet": {"!url": "https://lodash.com/docs/4.17.15#isWeakSet", "!doc": "Checks if value is classified as a WeakSet object.", "!type": "fn(value)"}, "lt": {"!url": "https://lodash.com/docs/4.17.15#lt", "!doc": "Checks if value is less than other.", "!type": "fn(value, other)"}, "lte": {"!url": "https://lodash.com/docs/4.17.15#lte", "!doc": "Checks if value is less than or equal to other.", "!type": "fn(value, other)"}, "toArray": {"!url": "https://lodash.com/docs/4.17.15#toArray", "!doc": "Converts value to an array.", "!type": "fn(value)"}, "toFinite": {"!url": "https://lodash.com/docs/4.17.15#toFinite", "!doc": "Converts value to a finite number.", "!type": "fn(value)"}, "toInteger": {"!url": "https://lodash.com/docs/4.17.15#toInteger", "!doc": "Converts value to an integer.Note: This method is loosely based on ToInteger.", "!type": "fn(value)"}, "toLength": {"!url": "https://lodash.com/docs/4.17.15#toLength", "!doc": "Converts value to an integer suitable for use as the length of an array-like object.Note: This method is based on ToLength.", "!type": "fn(value)"}, "toNumber": {"!url": "https://lodash.com/docs/4.17.15#toNumber", "!doc": "Converts value to a number.", "!type": "fn(value)"}, "toPlainObject": {"!url": "https://lodash.com/docs/4.17.15#toPlainObject", "!doc": "Converts value to a plain object flattening inherited enumerable string keyed properties of value to own properties of the plain object.", "!type": "fn(value)"}, "toSafeInteger": {"!url": "https://lodash.com/docs/4.17.15#toSafeInteger", "!doc": "Converts value to a safe integer. A safe integer can be compared and represented correctly.", "!type": "fn(value)"}, "toString": {"!url": "https://lodash.com/docs/4.17.15#toString", "!doc": "Converts value to a string. An empty string is returned for null and undefined values. The sign of -0 is preserved.", "!type": "fn(value)"}, "add": {"!url": "https://lodash.com/docs/4.17.15#add", "!doc": "Adds two numbers.", "!type": "fn(augend, addend)"}, "ceil": {"!url": "https://lodash.com/docs/4.17.15#ceil", "!doc": "Computes number rounded up to precision.", "!type": "fn(number, [precision])"}, "divide": {"!url": "https://lodash.com/docs/4.17.15#divide", "!doc": "Divide two numbers.", "!type": "fn(dividend, divisor)"}, "floor": {"!url": "https://lodash.com/docs/4.17.15#floor", "!doc": "Computes number rounded down to precision.", "!type": "fn(number, [precision])"}, "max": {"!url": "https://lodash.com/docs/4.17.15#max", "!doc": "Computes the maximum value of array. If array is empty or falsey, undefined is returned.", "!type": "fn(array)"}, "maxBy": {"!url": "https://lodash.com/docs/4.17.15#maxBy", "!doc": "This method is like _.max except that it accepts iteratee which is invoked for each element in array to generate the criterion by which the value is ranked. The iteratee is invoked with one argument: (value).", "!type": "fn(array, [iteratee])"}, "mean": {"!url": "https://lodash.com/docs/4.17.15#mean", "!doc": "Computes the mean of the values in array.", "!type": "fn(array)"}, "meanBy": {"!url": "https://lodash.com/docs/4.17.15#meanBy", "!doc": "This method is like _.mean except that it accepts iteratee which is invoked for each element in array to generate the value to be averaged. The iteratee is invoked with one argument: (value).", "!type": "fn(array, [iteratee])"}, "min": {"!url": "https://lodash.com/docs/4.17.15#min", "!doc": "Computes the minimum value of array. If array is empty or falsey, undefined is returned.", "!type": "fn(array)"}, "minBy": {"!url": "https://lodash.com/docs/4.17.15#minBy", "!doc": "This method is like _.min except that it accepts iteratee which is invoked for each element in array to generate the criterion by which the value is ranked. The iteratee is invoked with one argument: (value).", "!type": "fn(array, [iteratee])"}, "multiply": {"!url": "https://lodash.com/docs/4.17.15#multiply", "!doc": "Multiply two numbers.", "!type": "fn(multiplier, multiplicand)"}, "round": {"!url": "https://lodash.com/docs/4.17.15#round", "!doc": "Computes number rounded to precision.", "!type": "fn(number, [precision])"}, "subtract": {"!url": "https://lodash.com/docs/4.17.15#subtract", "!doc": "Subtract two numbers.", "!type": "fn(minuend, subtrahend)"}, "sum": {"!url": "https://lodash.com/docs/4.17.15#sum", "!doc": "Computes the sum of the values in array.", "!type": "fn(array)"}, "sumBy": {"!url": "https://lodash.com/docs/4.17.15#sumBy", "!doc": "This method is like _.sum except that it accepts iteratee which is invoked for each element in array to generate the value to be summed. The iteratee is invoked with one argument: (value).", "!type": "fn(array, [iteratee])"}, "clamp": {"!url": "https://lodash.com/docs/4.17.15#clamp", "!doc": "Clamps number within the inclusive lower and upper bounds.", "!type": "fn(number, [lower], upper)"}, "inRange": {"!url": "https://lodash.com/docs/4.17.15#inRange", "!doc": "Checks if n is between start and up to, but not including, end. If end is not specified, it's set to start with start then set to 0. If start is greater than end the params are swapped to support negative ranges.", "!type": "fn(number, [start], end)"}, "random": {"!url": "https://lodash.com/docs/4.17.15#random", "!doc": "Produces a random number between the inclusive lower and upper bounds. If only one argument is provided a number between 0 and the given number is returned. If floating is true, or either lower or upper are floats, a floating-point number is returned instead of an integer.Note: JavaScript follows the IEEE-754 standard for resolving floating-point values which can produce unexpected results.", "!type": "fn([lower], [upper], [floating])"}, "assign": {"!url": "https://lodash.com/docs/4.17.15#assign", "!doc": "Assigns own enumerable string keyed properties of source objects to the destination object. Source objects are applied from left to right. Subsequent sources overwrite property assignments of previous sources.Note: This method mutates object and is loosely based on Object.assign.", "!type": "fn(object, [sources])"}, "assignIn": {"!url": "https://lodash.com/docs/4.17.15#assignIn", "!doc": "This method is like _.assign except that it iterates over own and inherited source properties.Note: This method mutates object.", "!type": "fn(object, [sources])"}, "assignInWith": {"!url": "https://lodash.com/docs/4.17.15#assignInWith", "!doc": "This method is like _.assignIn except that it accepts customizer which is invoked to produce the assigned values. If customizer returns undefined, assignment is handled by the method instead. The customizer is invoked with five arguments: (objValue, srcValue, key, object, source).Note: This method mutates object.", "!type": "fn(object, sources, [customizer])"}, "assignWith": {"!url": "https://lodash.com/docs/4.17.15#assignWith", "!doc": "This method is like _.assign except that it accepts customizer which is invoked to produce the assigned values. If customizer returns undefined, assignment is handled by the method instead. The customizer is invoked with five arguments: (objValue, srcValue, key, object, source).Note: This method mutates object.", "!type": "fn(object, sources, [customizer])"}, "at": {"!url": "https://lodash.com/docs/4.17.15#at", "!doc": "Creates an array of values corresponding to paths of object.", "!type": "fn(object, [paths])"}, "create": {"!url": "https://lodash.com/docs/4.17.15#create", "!doc": "Creates an object that inherits from the prototype object. If a properties object is given, its own enumerable string keyed properties are assigned to the created object.", "!type": "fn(prototype, [properties])"}, "defaults": {"!url": "https://lodash.com/docs/4.17.15#defaults", "!doc": "Assigns own and inherited enumerable string keyed properties of source objects to the destination object for all destination properties that resolve to undefined. Source objects are applied from left to right. Once a property is set, additional values of the same property are ignored.Note: This method mutates object.", "!type": "fn(object, [sources])"}, "defaultsDeep": {"!url": "https://lodash.com/docs/4.17.15#defaultsDeep", "!doc": "This method is like _.defaults except that it recursively assigns default properties.Note: This method mutates object.", "!type": "fn(object, [sources])"}, "findKey": {"!url": "https://lodash.com/docs/4.17.15#findKey", "!doc": "This method is like _.find except that it returns the key of the first element predicate returns truthy for instead of the element itself.", "!type": "fn(object, [predicate])"}, "findLastKey": {"!url": "https://lodash.com/docs/4.17.15#findLastKey", "!doc": "This method is like _.find<PERSON>ey except that it iterates over elements of a collection in the opposite order.", "!type": "fn(object, [predicate])"}, "forIn": {"!url": "https://lodash.com/docs/4.17.15#forIn", "!doc": "Iterates over own and inherited enumerable string keyed properties of an object and invokes iteratee for each property. The iteratee is invoked with three arguments: (value, key, object). Iteratee functions may exit iteration early by explicitly returning false.", "!type": "fn(object, [iteratee])"}, "forInRight": {"!url": "https://lodash.com/docs/4.17.15#forInRight", "!doc": "This method is like _.forIn except that it iterates over properties of object in the opposite order.", "!type": "fn(object, [iteratee])"}, "forOwn": {"!url": "https://lodash.com/docs/4.17.15#forOwn", "!doc": "Iterates over own enumerable string keyed properties of an object and invokes iteratee for each property. The iteratee is invoked with three arguments: (value, key, object). Iteratee functions may exit iteration early by explicitly returning false.", "!type": "fn(object, [iteratee])"}, "forOwnRight": {"!url": "https://lodash.com/docs/4.17.15#forOwnRight", "!doc": "This method is like _.forOwn except that it iterates over properties of object in the opposite order.", "!type": "fn(object, [iteratee])"}, "functions": {"!url": "https://lodash.com/docs/4.17.15#functions", "!doc": "Creates an array of function property names from own enumerable properties of object.", "!type": "fn(object)"}, "functionsIn": {"!url": "https://lodash.com/docs/4.17.15#functionsIn", "!doc": "Creates an array of function property names from own and inherited enumerable properties of object.", "!type": "fn(object)"}, "get": {"!url": "https://lodash.com/docs/4.17.15#get", "!doc": "Gets the value at path of object. If the resolved value is undefined, the defaultValue is returned in its place.", "!type": "fn(object, path, [defaultValue])"}, "has": {"!url": "https://lodash.com/docs/4.17.15#has", "!doc": "Checks if path is a direct property of object.", "!type": "fn(object, path)"}, "hasIn": {"!url": "https://lodash.com/docs/4.17.15#hasIn", "!doc": "Checks if path is a direct or inherited property of object.", "!type": "fn(object, path)"}, "invert": {"!url": "https://lodash.com/docs/4.17.15#invert", "!doc": "Creates an object composed of the inverted keys and values of object. If object contains duplicate values, subsequent values overwrite property assignments of previous values.", "!type": "fn(object)"}, "invertBy": {"!url": "https://lodash.com/docs/4.17.15#invertBy", "!doc": "This method is like _.invert except that the inverted object is generated from the results of running each element of object thru iteratee. The corresponding inverted value of each inverted key is an array of keys responsible for generating the inverted value. The iteratee is invoked with one argument: (value).", "!type": "fn(object, [iteratee])"}, "invoke": {"!url": "https://lodash.com/docs/4.17.15#invoke", "!doc": "Invokes the method at path of object.", "!type": "fn(object, path, [args])"}, "keys": {"!url": "https://lodash.com/docs/4.17.15#keys", "!doc": "Creates an array of the own enumerable property names of object.Note: Non-object values are coerced to objects. See the ES spec for more details.", "!type": "fn(object)"}, "keysIn": {"!url": "https://lodash.com/docs/4.17.15#keysIn", "!doc": "Creates an array of the own and inherited enumerable property names of object.Note: Non-object values are coerced to objects.", "!type": "fn(object)"}, "mapKeys": {"!url": "https://lodash.com/docs/4.17.15#mapKeys", "!doc": "The opposite of _.mapValues; this method creates an object with the same values as object and keys generated by running each own enumerable string keyed property of object thru iteratee. The iteratee is invoked with three arguments: (value, key, object).", "!type": "fn(object, [iteratee])"}, "mapValues": {"!url": "https://lodash.com/docs/4.17.15#mapValues", "!doc": "Creates an object with the same keys as object and values generated by running each own enumerable string keyed property of object thru iteratee. The iteratee is invoked with three arguments:(value, key, object).", "!type": "fn(object, [iteratee])"}, "merge": {"!url": "https://lodash.com/docs/4.17.15#merge", "!doc": "This method is like _.assign except that it recursively merges own and inherited enumerable string keyed properties of source objects into the destination object. Source properties that resolve to undefined are skipped if a destination value exists. Array and plain object properties are merged recursively. Other objects and value types are overridden by assignment. Source objects are applied from left to right. Subsequent sources overwrite property assignments of previous sources.Note: This method mutates object.", "!type": "fn(object, [sources])"}, "mergeWith": {"!url": "https://lodash.com/docs/4.17.15#mergeWith", "!doc": "This method is like _.merge except that it accepts customizer which is invoked to produce the merged values of the destination and source properties. If customizer returns undefined, merging is handled by the method instead. The customizer is invoked with six arguments:(objValue, srcValue, key, object, source, stack).Note: This method mutates object.", "!type": "fn(object, sources, customizer)"}, "omit": {"!url": "https://lodash.com/docs/4.17.15#omit", "!doc": "The opposite of _.pick; this method creates an object composed of the own and inherited enumerable property paths of object that are not omitted.Note: This method is considerably slower than _.pick.", "!type": "fn(object, [paths])"}, "omitBy": {"!url": "https://lodash.com/docs/4.17.15#omitBy", "!doc": "The opposite of _.pickBy; this method creates an object composed of the own and inherited enumerable string keyed properties of object that predicate doesn't return truthy for. The predicate is invoked with two arguments: (value, key).", "!type": "fn(object, [predicate])"}, "pick": {"!url": "https://lodash.com/docs/4.17.15#pick", "!doc": "Creates an object composed of the picked object properties.", "!type": "fn(object, [paths])"}, "pickBy": {"!url": "https://lodash.com/docs/4.17.15#pickBy", "!doc": "Creates an object composed of the object properties predicate returns truthy for. The predicate is invoked with two arguments: (value, key).", "!type": "fn(object, [predicate])"}, "result": {"!url": "https://lodash.com/docs/4.17.15#result", "!doc": "This method is like _.get except that if the resolved value is a function it's invoked with the this binding of its parent object and its result is returned.", "!type": "fn(object, path, [defaultValue])"}, "set": {"!url": "https://lodash.com/docs/4.17.15#set", "!doc": "Sets the value at path of object. If a portion of path doesn't exist, it's created. Arrays are created for missing index properties while objects are created for all other missing properties. Use _.setWith to customize path creation.Note: This method mutates object.", "!type": "fn(object, path, value)"}, "setWith": {"!url": "https://lodash.com/docs/4.17.15#setWith", "!doc": "This method is like _.set except that it accepts customizer which is invoked to produce the objects of path. If customizer returns undefined path creation is handled by the method instead. The customizer is invoked with three arguments: (nsValue, key, nsObject).Note: This method mutates object.", "!type": "fn(object, path, value, [customizer])"}, "toPairs": {"!url": "https://lodash.com/docs/4.17.15#toPairs", "!doc": "Creates an array of own enumerable string keyed-value pairs for object which can be consumed by _.fromPairs. If object is a map or set, its entries are returned.", "!type": "fn(object)"}, "toPairsIn": {"!url": "https://lodash.com/docs/4.17.15#toPairsIn", "!doc": "Creates an array of own and inherited enumerable string keyed-value pairs for object which can be consumed by _.fromPairs. If object is a map or set, its entries are returned.", "!type": "fn(object)"}, "transform": {"!url": "https://lodash.com/docs/4.17.15#transform", "!doc": "An alternative to _.reduce; this method transforms object to a new accumulator object which is the result of running each of its own enumerable string keyed properties thru iteratee, with each invocation potentially mutating the accumulator object. If accumulator is not provided, a new object with the same [[Prototype]] will be used. The iteratee is invoked with four arguments: (accumulator, value, key, object). Iteratee functions may exit iteration early by explicitly returning false.", "!type": "fn(object, [iteratee], [accumulator])"}, "unset": {"!url": "https://lodash.com/docs/4.17.15#unset", "!doc": "Removes the property at path of object.Note: This method mutates object.", "!type": "fn(object, path)"}, "update": {"!url": "https://lodash.com/docs/4.17.15#update", "!doc": "This method is like _.set except that accepts updater to produce the value to set. Use _.updateWith to customize path creation. The updater is invoked with one argument: (value).Note: This method mutates object.", "!type": "fn(object, path, updater)"}, "updateWith": {"!url": "https://lodash.com/docs/4.17.15#updateWith", "!doc": "This method is like _.update except that it accepts customizer which is invoked to produce the objects of path. If customizer returns undefined path creation is handled by the method instead. The customizer is invoked with three arguments: (nsValue, key, nsObject).Note: This method mutates object.", "!type": "fn(object, path, updater, [customizer])"}, "values": {"!url": "https://lodash.com/docs/4.17.15#values", "!doc": "Creates an array of the own enumerable string keyed property values of object.Note: Non-object values are coerced to objects.", "!type": "fn(object)"}, "valuesIn": {"!url": "https://lodash.com/docs/4.17.15#valuesIn", "!doc": "Creates an array of the own and inherited enumerable string keyed property values of object.Note: Non-object values are coerced to objects.", "!type": "fn(object)"}, "_": {"!url": "https://lodash.com/docs/4.17.15#lodash", "!doc": "Creates a lodash object which wraps value to enable implicit method chain sequences. Methods that operate on and return arrays, collections, and functions can be chained together. Methods that retrieve a single value or may return a primitive value will automatically end the chain sequence and return the unwrapped value. Otherwise, the value must be unwrapped with _#value.Explicit chain sequences, which must be unwrapped with _#value, may be enabled using _.chain.The execution of chained methods is lazy, that is, it's deferred until _#value is implicitly or explicitly called.Lazy evaluation allows several methods to support shortcut fusion. Shortcut fusion is an optimization to merge iteratee calls; this avoids the creation of intermediate arrays and can greatly reduce the number of iteratee executions. Sections of a chain sequence qualify for shortcut fusion if the section is applied to an array and iteratees accept only one argument. The heuristic for whether a section qualifies for shortcut fusion is subject to change.Chaining is supported in custom builds as long as the _#value method is directly or indirectly included in the build.In addition to lodash methods, wrappers have Array and String methods.The wrapper Array methods are:concat, join, pop, push, shift, sort, splice, and unshiftThe wrapper String methods are:replace and splitThe wrapper methods that support shortcut fusion are:at, compact, drop, dropRight, dropWhile, filter, find, findLast, head, initial, last, map, reject, reverse, slice, tail, take, takeRight, takeRightWhile, takeWhile, and toArrayThe chainable wrapper methods are:after, ary, assign, assignIn, assignInWith, assignWith, at, before, bind, bindAll, bindKey, castArray, chain, chunk, commit, compact, concat, conforms, constant, countBy, create, curry, debounce, defaults, defaultsDeep, defer, delay, difference, differenceBy, differenceWith, drop, dropRight, dropRightWhile, dropWhile, extend, extendWith, fill, filter, flatMap, flatMapDeep, flatMapDepth, flatten, flattenDeep, flattenDepth, flip, flow, flowRight, fromPairs, functions, functionsIn, groupBy, initial, intersection, intersectionBy, intersectionWith, invert, invertBy, invokeMap, iteratee, keyBy, keys, keysIn, map, mapKeys, mapValues, matches, matchesProperty, memoize, merge, mergeWith, method, methodOf, mixin, negate, nthArg, omit, omitBy, once, orderBy, over, overArgs, overEvery, overSome, partial, partialRight, partition, pick, pickBy, plant, property, propertyOf, pull, pullAll, pullAllBy, pullAllWith, pullAt, push, range, rangeRight, rearg, reject, remove, rest, reverse, sampleSize, set, setWith, shuffle, slice, sort, sortBy, splice, spread, tail, take, takeRight, takeRightWhile, takeWhile, tap, throttle, thru, toArray, toPairs, toPairsIn, toPath, toPlainObject, transform, unary, union, unionBy, unionWith, uniq, uniqBy, uniqWith, unset, unshift, unzip, unzipWith, update, updateWith, values, valuesIn, without, wrap, xor, xorBy, xorWith, zip, zipObject, zipObjectDeep, and zipWithThe wrapper methods that are not chainable by default are:add, attempt, camelCase, capitalize, ceil, clamp, clone, cloneDeep, cloneDeepWith, cloneWith, conformsTo, deburr, defaultTo, divide, each, eachRight, endsWith, eq, escape, escapeRegExp, every, find, findIndex, findKey, findLast, findLastIndex, findLastKey, first, floor, forEach, forEachRight, forIn, forInRight, forOwn, forOwnRight, get, gt, gte, has, hasIn, head, identity, includes, indexOf, inRange, invoke, isArguments, isArray, isArrayBuffer, isArrayLike, isArrayLikeObject, isBoolean, isBuffer, isDate, isElement, isEmpty, isEqual, isEqualWith, isError, isFinite, isFunction, isInteger, isLength, isMap, isMatch, isMatchWith, isNaN, isNative, isNil, isNull, isNumber, isObject, isObjectLike, isPlainObject, isRegExp, isSafeInteger, isSet, isString, isUndefined, isTypedArray, isWeakMap, isWeakSet, join, kebabCase, last, lastIndexOf, lowerCase, lowerFirst, lt, lte, max, maxBy, mean, meanBy, min, minBy, multiply, noConflict, noop, now, nth, pad, padEnd, padStart, parseInt, pop, random, reduce, reduceRight, repeat, result, round, runInContext, sample, shift, size, snakeCase, some, sortedIndex, sortedIndexBy, sortedLastIndex, sortedLastIndexBy, startCase, startsWith, stubArray, stubFalse, stubObject, stubString, stubTrue, subtract, sum, sumBy, template, times, toFinite, toInteger, toJSON, toLength, toLower, toNumber, toSafeInteger, toString, toUpper, trim, trimEnd, trimStart, truncate, unescape, uniqueId, upperCase, upperFirst, value, and words", "!type": "fn(value)"}, "chain": {"!url": "https://lodash.com/docs/4.17.15#chain", "!doc": "Creates a lodash wrapper instance that wraps value with explicit method chain sequences enabled. The result of such sequences must be unwrapped with _#value.", "!type": "fn(value)"}, "tap": {"!url": "https://lodash.com/docs/4.17.15#tap", "!doc": "This method invokes interceptor and returns value. The interceptor is invoked with one argument; (value). The purpose of this method is to \"tap into\" a method chain sequence in order to modify intermediate results.", "!type": "fn(value, interceptor)"}, "thru": {"!url": "https://lodash.com/docs/4.17.15#thru", "!doc": "This method is like _.tap except that it returns the result of interceptor. The purpose of this method is to \"pass thru\" values replacing intermediate results in a method chain sequence.", "!type": "fn(value, interceptor)"}, "prototype[Symbol.iterator]": {"!url": "https://lodash.com/docs/4.17.15#prototype-Symbol-iterator", "!doc": "Enables the wrapper to be iterable.", "!type": "fn()"}, "prototype.at": {"!url": "https://lodash.com/docs/4.17.15#prototype-at", "!doc": "This method is the wrapper version of _.at.", "!type": "fn([paths])"}, "prototype.chain": {"!url": "https://lodash.com/docs/4.17.15#prototype-chain", "!doc": "Creates a lodash wrapper instance with explicit method chain sequences enabled.", "!type": "fn()"}, "prototype.commit": {"!url": "https://lodash.com/docs/4.17.15#prototype-commit", "!doc": "Executes the chain sequence and returns the wrapped result.", "!type": "fn()"}, "prototype.next": {"!url": "https://lodash.com/docs/4.17.15#prototype-next", "!doc": "Gets the next value on a wrapped object following the iterator protocol.", "!type": "fn()"}, "prototype.plant": {"!url": "https://lodash.com/docs/4.17.15#prototype-plant", "!doc": "Creates a clone of the chain sequence planting value as the wrapped value.", "!type": "fn(value)"}, "prototype.reverse": {"!url": "https://lodash.com/docs/4.17.15#prototype-reverse", "!doc": "This method is the wrapper version of _.reverse.Note: This method mutates the wrapped array.", "!type": "fn()"}, "prototype.value": {"!url": "https://lodash.com/docs/4.17.15#prototype-value", "!doc": "Executes the chain sequence to resolve the unwrapped value.", "!type": "fn()"}, "camelCase": {"!url": "https://lodash.com/docs/4.17.15#camelCase", "!doc": "Converts string to camel case.", "!type": "fn([string])"}, "capitalize": {"!url": "https://lodash.com/docs/4.17.15#capitalize", "!doc": "Converts the first character of string to upper case and the remaining to lower case.", "!type": "fn([string])"}, "deburr": {"!url": "https://lodash.com/docs/4.17.15#deburr", "!doc": "Deburrs string by converting Latin-1 Supplement and Latin Extended-A letters to basic Latin letters and removing combining diacritical marks.", "!type": "fn([string])"}, "endsWith": {"!url": "https://lodash.com/docs/4.17.15#endsWith", "!doc": "Checks if string ends with the given target string.", "!type": "fn([string], [target], [position])"}, "escape": {"!url": "https://lodash.com/docs/4.17.15#escape", "!doc": "Converts the characters \"&\", \"<\", \">\", '\"', and \"'\" in string to their corresponding HTML entities.Note: No other characters are escaped. To escape additional characters use a third-party library like he.Though the \">\" character is escaped for symmetry, characters like \">\" and \"/\" don't need escaping in HTML and have no special meaning unless they're part of a tag or unquoted attribute value. See <PERSON>'s article (under \"semi-related fun fact\") for more details.When working with HTML you should always quote attribute values to reduce XSS vectors.", "!type": "fn([string])"}, "escapeRegExp": {"!url": "https://lodash.com/docs/4.17.15#escapeRegExp", "!doc": "Escapes the RegExp special characters \"^\", \"$\", \"\", \".\", \"*\", \"+\", \"?\", \"(\", \")\", \"[\", \"]\", \"{\", \"}\", and \"|\" in string.", "!type": "fn([string])"}, "kebabCase": {"!url": "https://lodash.com/docs/4.17.15#kebabCase", "!doc": "Converts string to kebab case.", "!type": "fn([string])"}, "lowerCase": {"!url": "https://lodash.com/docs/4.17.15#lowerCase", "!doc": "Converts string, as space separated words, to lower case.", "!type": "fn([string])"}, "lowerFirst": {"!url": "https://lodash.com/docs/4.17.15#lowerFirst", "!doc": "Converts the first character of string to lower case.", "!type": "fn([string])"}, "pad": {"!url": "https://lodash.com/docs/4.17.15#pad", "!doc": "Pads string on the left and right sides if it's shorter than length. Padding characters are truncated if they can't be evenly divided by length.", "!type": "fn([string], [length], [chars])"}, "padEnd": {"!url": "https://lodash.com/docs/4.17.15#padEnd", "!doc": "Pads string on the right side if it's shorter than length. Padding characters are truncated if they exceed length.", "!type": "fn([string], [length], [chars])"}, "padStart": {"!url": "https://lodash.com/docs/4.17.15#padStart", "!doc": "Pads string on the left side if it's shorter than length. Padding characters are truncated if they exceed length.", "!type": "fn([string], [length], [chars])"}, "parseInt": {"!url": "https://lodash.com/docs/4.17.15#parseInt", "!doc": "Converts string to an integer of the specified radix. If radix is undefined or 0, a radix of 10 is used unless value is a hexadecimal, in which case a radix of 16 is used.Note: This method aligns with the ES5 implementation of parseInt.", "!type": "fn(string, [radix])"}, "repeat": {"!url": "https://lodash.com/docs/4.17.15#repeat", "!doc": "Repeats the given string n times.", "!type": "fn([string], [n])"}, "replace": {"!url": "https://lodash.com/docs/4.17.15#replace", "!doc": "Replaces matches for pattern in string with replacement.Note: This method is based on String#replace.", "!type": "fn([string], pattern, replacement)"}, "snakeCase": {"!url": "https://lodash.com/docs/4.17.15#snakeCase", "!doc": "Converts string to snake case.", "!type": "fn([string])"}, "split": {"!url": "https://lodash.com/docs/4.17.15#split", "!doc": "Splits string by separator.Note: This method is based on String#split.", "!type": "fn([string], separator, [limit])"}, "startCase": {"!url": "https://lodash.com/docs/4.17.15#startCase", "!doc": "Converts string to start case.", "!type": "fn([string])"}, "startsWith": {"!url": "https://lodash.com/docs/4.17.15#startsWith", "!doc": "Checks if string starts with the given target string.", "!type": "fn([string], [target], [position])"}, "template": {"!url": "https://lodash.com/docs/4.17.15#template", "!doc": "Creates a compiled template function that can interpolate data properties in \"interpolate\" delimiters, HTML-escape interpolated data properties in \"escape\" delimiters, and execute JavaScript in \"evaluate\" delimiters. Data properties may be accessed as free variables in the template. If a setting object is given, it takes precedence over _.templateSettings values.Note: In the development build _.template utilizes sourceURLs for easier debugging.For more information on precompiling templates see lodash's custom builds documentation.For more information on Chrome extension sandboxes see Chrome's extensions documentation.", "!type": "fn([string], [options])"}, "toLower": {"!url": "https://lodash.com/docs/4.17.15#toLower", "!doc": "Converts string, as a whole, to lower case just like String#toLowerCase.", "!type": "fn([string])"}, "toUpper": {"!url": "https://lodash.com/docs/4.17.15#toUpper", "!doc": "Converts string, as a whole, to upper case just like String#toUpperCase.", "!type": "fn([string])"}, "trim": {"!url": "https://lodash.com/docs/4.17.15#trim", "!doc": "Removes leading and trailing whitespace or specified characters from string.", "!type": "fn([string], [chars])"}, "trimEnd": {"!url": "https://lodash.com/docs/4.17.15#trimEnd", "!doc": "Removes trailing whitespace or specified characters from string.", "!type": "fn([string], [chars])"}, "trimStart": {"!url": "https://lodash.com/docs/4.17.15#trimStart", "!doc": "Removes leading whitespace or specified characters from string.", "!type": "fn([string], [chars])"}, "truncate": {"!url": "https://lodash.com/docs/4.17.15#truncate", "!doc": "Truncates string if it's longer than the given maximum string length. The last characters of the truncated string are replaced with the omission string which defaults to \"...\".", "!type": "fn([string], [options])"}, "unescape": {"!url": "https://lodash.com/docs/4.17.15#unescape", "!doc": "The inverse of _.escape; this method converts the HTML entities &amp;, &lt;, &gt;, &quot;, and &#39; in string to their corresponding characters.Note: No other HTML entities are unescaped. To unescape additional HTML entities use a third-party library like he.", "!type": "fn([string])"}, "upperCase": {"!url": "https://lodash.com/docs/4.17.15#upperCase", "!doc": "Converts string, as space separated words, to upper case.", "!type": "fn([string])"}, "upperFirst": {"!url": "https://lodash.com/docs/4.17.15#upperFirst", "!doc": "Converts the first character of string to upper case.", "!type": "fn([string])"}, "words": {"!url": "https://lodash.com/docs/4.17.15#words", "!doc": "Splits string into an array of its words.", "!type": "fn([string], [pattern])"}, "attempt": {"!url": "https://lodash.com/docs/4.17.15#attempt", "!doc": "Attempts to invoke func, returning either the result or the caught error object. Any additional arguments are provided to func when it's invoked.", "!type": "fn(func, [args])"}, "bindAll": {"!url": "https://lodash.com/docs/4.17.15#bindAll", "!doc": "Binds methods of an object to the object itself, overwriting the existing method.Note: This method doesn't set the \"length\" property of bound functions.", "!type": "fn(object, methodNames)"}, "cond": {"!url": "https://lodash.com/docs/4.17.15#cond", "!doc": "Creates a function that iterates over pairs and invokes the corresponding function of the first predicate to return truthy. The predicate-function pairs are invoked with the this binding and arguments of the created function.", "!type": "fn(pairs)"}, "conforms": {"!url": "https://lodash.com/docs/4.17.15#conforms", "!doc": "Creates a function that invokes the predicate properties of source with the corresponding property values of a given object, returning true if all predicates return truthy, else false.Note: The created function is equivalent to _.conformsTo with source partially applied.", "!type": "fn(source)"}, "constant": {"!url": "https://lodash.com/docs/4.17.15#constant", "!doc": "Creates a function that returns value.", "!type": "fn(value)"}, "defaultTo": {"!url": "https://lodash.com/docs/4.17.15#defaultTo", "!doc": "Checks value to determine whether a default value should be returned in its place. The defaultValue is returned if value is NaN, null, or undefined.", "!type": "fn(value, defaultValue)"}, "flow": {"!url": "https://lodash.com/docs/4.17.15#flow", "!doc": "Creates a function that returns the result of invoking the given functions with the this binding of the created function, where each successive invocation is supplied the return value of the previous.", "!type": "fn([funcs])"}, "flowRight": {"!url": "https://lodash.com/docs/4.17.15#flowRight", "!doc": "This method is like _.flow except that it creates a function that invokes the given functions from right to left.", "!type": "fn([funcs])"}, "identity": {"!url": "https://lodash.com/docs/4.17.15#identity", "!doc": "This method returns the first argument it receives.", "!type": "fn(value)"}, "iteratee": {"!url": "https://lodash.com/docs/4.17.15#iteratee", "!doc": "Creates a function that invokes func with the arguments of the created function. If func is a property name, the created function returns the property value for a given element. If func is an array or object, the created function returns true for elements that contain the equivalent source properties, otherwise it returns false.", "!type": "fn([func])"}, "matches": {"!url": "https://lodash.com/docs/4.17.15#matches", "!doc": "Creates a function that performs a partial deep comparison between a given object and source, returning true if the given object has equivalent property values, else false.Note: The created function is equivalent to _.isMatch with source partially applied.Partial comparisons will match empty array and empty object source values against any array or object value, respectively. See _.isEqual for a list of supported value comparisons.", "!type": "fn(source)"}, "matchesProperty": {"!url": "https://lodash.com/docs/4.17.15#matchesProperty", "!doc": "Creates a function that performs a partial deep comparison between the value at path of a given object to srcValue, returning true if the object value is equivalent, else false.Note: Partial comparisons will match empty array and empty object srcValue values against any array or object value, respectively. See _.isEqual for a list of supported value comparisons.", "!type": "fn(path, srcValue)"}, "method": {"!url": "https://lodash.com/docs/4.17.15#method", "!doc": "Creates a function that invokes the method at path of a given object. Any additional arguments are provided to the invoked method.", "!type": "fn(path, [args])"}, "methodOf": {"!url": "https://lodash.com/docs/4.17.15#methodOf", "!doc": "The opposite of _.method; this method creates a function that invokes the method at a given path of object. Any additional arguments are provided to the invoked method.", "!type": "fn(object, [args])"}, "mixin": {"!url": "https://lodash.com/docs/4.17.15#mixin", "!doc": "Adds all own enumerable string keyed function properties of a source object to the destination object. If object is a function, then methods are added to its prototype as well.Note: Use _.runInContext to create a pristine lodash function to avoid conflicts caused by modifying the original.", "!type": "fn([object], source, [options])"}, "noConflict": {"!url": "https://lodash.com/docs/4.17.15#noConflict", "!doc": "Reverts the _ variable to its previous value and returns a reference to the lodash function.", "!type": "fn()"}, "noop": {"!url": "https://lodash.com/docs/4.17.15#noop", "!doc": "This method returns undefined.", "!type": "fn()"}, "nthArg": {"!url": "https://lodash.com/docs/4.17.15#nthArg", "!doc": "Creates a function that gets the argument at index n. If n is negative, the nth argument from the end is returned.", "!type": "fn([n])"}, "over": {"!url": "https://lodash.com/docs/4.17.15#over", "!doc": "Creates a function that invokes iteratees with the arguments it receives and returns their results.", "!type": "fn([iteratees])"}, "overEvery": {"!url": "https://lodash.com/docs/4.17.15#overEvery", "!doc": "Creates a function that checks if all of the predicates return truthy when invoked with the arguments it receives.", "!type": "fn([predicates])"}, "overSome": {"!url": "https://lodash.com/docs/4.17.15#overSome", "!doc": "Creates a function that checks if any of the predicates return truthy when invoked with the arguments it receives.", "!type": "fn([predicates])"}, "property": {"!url": "https://lodash.com/docs/4.17.15#property", "!doc": "Creates a function that returns the value at path of a given object.", "!type": "fn(path)"}, "propertyOf": {"!url": "https://lodash.com/docs/4.17.15#propertyOf", "!doc": "The opposite of _.property; this method creates a function that returns the value at a given path of object.", "!type": "fn(object)"}, "range": {"!url": "https://lodash.com/docs/4.17.15#range", "!doc": "Creates an array of numbers (positive and/or negative) progressing from start up to, but not including, end. A step of -1 is used if a negative start is specified without an end or step. If end is not specified, it's set to start with start then set to 0.Note: JavaScript follows the IEEE-754 standard for resolving floating-point values which can produce unexpected results.", "!type": "fn([start], end, [step])"}, "rangeRight": {"!url": "https://lodash.com/docs/4.17.15#rangeRight", "!doc": "This method is like _.range except that it populates values in descending order.", "!type": "fn([start], end, [step])"}, "runInContext": {"!url": "https://lodash.com/docs/4.17.15#runInContext", "!doc": "Create a new pristine lodash function using the context object.", "!type": "fn([context])"}, "stubArray": {"!url": "https://lodash.com/docs/4.17.15#stubArray", "!doc": "This method returns a new empty array.", "!type": "fn()"}, "stubFalse": {"!url": "https://lodash.com/docs/4.17.15#stubFalse", "!doc": "This method returns false.", "!type": "fn()"}, "stubObject": {"!url": "https://lodash.com/docs/4.17.15#stubObject", "!doc": "This method returns a new empty object.", "!type": "fn()"}, "stubString": {"!url": "https://lodash.com/docs/4.17.15#stubString", "!doc": "This method returns an empty string.", "!type": "fn()"}, "stubTrue": {"!url": "https://lodash.com/docs/4.17.15#stubTrue", "!doc": "This method returns true.", "!type": "fn()"}, "times": {"!url": "https://lodash.com/docs/4.17.15#times", "!doc": "Invokes the iteratee n times, returning an array of the results of each invocation. The iteratee is invoked with one argument; (index).", "!type": "fn(n, [iteratee])"}, "toPath": {"!url": "https://lodash.com/docs/4.17.15#toPath", "!doc": "Converts value to a property path array.", "!type": "fn(value)"}, "uniqueId": {"!url": "https://lodash.com/docs/4.17.15#uniqueId", "!doc": "Generates a unique ID. If prefix is given, the ID is appended to it.", "!type": "fn([prefix])"}, "": {"!url": "https://lodash.com/docs/4.17.15#templateSettings-imports-_", "!doc": "A reference to the lodash function.", "!type": "fn_.templateSettings.imports._"}}}