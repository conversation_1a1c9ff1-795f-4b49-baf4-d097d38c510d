import {isJSBinding} from 'apptile-core';
import React, {forwardRef, useState, useRef, useEffect} from 'react';
import {StyleSheet, Text, View, Button} from 'react-native';
import {useSelector} from 'react-redux';
import {getModelJSForPlugin, JSModel} from 'apptile-core';
import {resolvePluginDocs} from 'apptile-core';
import {selectEditorState} from 'apptile-core';
import {selectAppModel} from 'apptile-core';
import {useDispatch} from 'react-redux';
import theme from '../../styles-v2/theme';
import _ from 'lodash'
import moment from 'moment';
import {EDITOR_OPEN_BINDING_EDITOR, openBindingEditor} from '../../actions/editorActions';
import {createCircularReplacer} from '../../../web/common/utils';

type CodeInputProps = {
  value?: unknown;
  defaultValue?: unknown;
  placeholder?: unknown;
  onChange: (value: string) => void;
  propertyName?: string;
  singleLine?: boolean;
  noOfLines?: number;
};

const CodeInput = forwardRef<{isFocused: () => boolean}, CodeInputProps>(({
  value,
  defaultValue,
  placeholder,
  propertyName,
  onChange,
  customHints,
  singleLine,
  noOfLines = singleLine ? 1 : 0,
}, ref) => {
  const inputEl = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch();
  const editorState = useSelector(selectEditorState);
  const appModel = useSelector(selectAppModel);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const isDynamicString = isJSBinding(value);
  useEffect(() => {
    if (inputEl.current && document.activeElement !== inputEl.current) {
      if (value && inputEl.current && (inputEl.current.value != value)) {
        inputEl.current.value = value.toString() || '';
      }
    }
  }, [value]);

  const dependencyGraph = appModel.dependencyGraph;

  const modelDocs = {'!name': 'jsModel'};
  const currentPluginSel = editorState.selectedPluginConfigSel;
  let compiledBindingFn = null;
  const sel = editorState.selectedPluginConfigSel?.slice() ?? [];
  const pageKey = sel[0];

  if (currentPluginSel) {
    const scratchMemory: JSModel = {
        $global: {},
        unresolved: true,
        $context: {},
        hasCurrentPage: false,
        currentPage: {},
        hasIndex: false,
        i: 0
      };
    const jsModel = getModelJSForPlugin(appModel, currentPluginSel, scratchMemory);
    Object.entries(jsModel || {}).map(([key, value]) => {
      const pluginType = _.get(value, 'pluginType') || _.get(value, '0.pluginType');
      const doc = resolvePluginDocs(pluginType);
      if (doc) Object.assign(modelDocs, {[key]: doc});
    });

    
    const pageId = appModel.pageKeysToId?.get(sel[0]);
    sel[0] = pageId;
    const node = dependencyGraph?.depGraph?.nodes?.get(sel.join('.')) ?? undefined;
    let bindingKey = value;
    if (node?.namespace) {
      bindingKey = node.namespace + '::' + value;
    }
    compiledBindingFn = dependencyGraph?.compiledBindingFns?.get(bindingKey) ?? null;
  }
  
  const storeGlobal = () => {
    const __binding_debug = {
      compiledBindingFn,
      $pageContext: appModel.jsModel[pageKey]?.plugins,
      $appJSModel: appModel.jsModel,
      currentPage: appModel.jsModel[pageKey],
      _,
      moment,
    }

    window.__binding_debug = __binding_debug;
    console.log("Binding:\n " + value);
    console.log("Transpiled function:\n " + __binding_debug.compiledBindingFn.transpiled);
    console.log(`__binding_debug.compiledBindingFn.compiled(__binding_debug.$pageContext, __binding_debug.$appJSModel, 0, __binding_debug.currentPage, __binding_debug._, __binding_debug.moment)`);
  }
  
  let result = 'Evaluation not done';

   if (compiledBindingFn) {
    try {
      result = compiledBindingFn.compiled(
        appModel.jsModel[pageKey].plugins, 
        appModel.jsModel,
        0,
        appModel.jsModel[pageKey],
        _,
        moment
      );
      if (typeof result === 'function') {
        result = result();
      }
      result = JSON.stringify(result, createCircularReplacer(), 1);
    } catch (err) {
      console.error("Failed during evaluation: ", err);
    }
  } else {
    result = "Binding not found";
  }   

  const onInputChange = _.debounce(ev => onChange(ev.target.value), 200, {trailing: true})

  return (
    <View style={{flex: 1}}>
      <input 
        style={{padding: 8, borderWidth: 1, borderColor: '#dadada', borderRadius: 8}}
        ref={inputEl}
        onChange={onInputChange}
        paceholder={placeholder ? `${placeholder}` : ''}
        onFocus={() => setPreviewVisible(true)}
        forwardedRef={ref}
      />
      
      {previewVisible && isDynamicString && (
        <View style={styles.previewValue}>
          <div style={{ 
              display: 'flex',
              flexDirection: 'row'
            }}
          >
            <button onClick={storeGlobal}>L</button>
            <button onClick={() => setPreviewVisible(false)}>X</button>

            <button 
              onClick={() => {
                const node = dependencyGraph?.depGraph?.nodes?.get(sel.join('.')) ?? undefined;
                dispatch(
                  openBindingEditor({
                    binding: value ? `${value}` : '',
                    callback: onChange,
                    namespace: node?.namespace,
                    selector: sel,
                    evalContext: {
                      $pageContext: appModel.jsModel[pageKey]?.plugins,
                      $appJSModel: appModel.jsModel,
                      currentPage: appModel.jsModel[pageKey],
                      i: 0,
                      _,
                      moment,
                    }
                  })
                );
              }}
            >
            O
            </button>
          </div>
          <Text>Result: {result}</Text>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  previewValue: {
    overflow: 'scroll',
    borderTopColor: '#cee1da',
    borderTopWidth: 0.2,
    paddingTop: 5,
    paddingHorizontal: 6,
    maxWidth: 200,
    maxHeight: 300
  },
  previewText: {
    color: theme.PRIMARY_COLOR,
    fontSize: 10,
    fontWeight: '200',
    fontFamily: 'monospace',
    marginTop: 4,
  },
});

CodeInput.displayName = 'CodeInput';
export default CodeInput;
