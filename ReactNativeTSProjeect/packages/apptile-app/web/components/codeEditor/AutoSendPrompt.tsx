import React, { useEffect, useState } from 'react';

interface AutoSendPromptProps {
  initialPrompt: string;
  onSend: (text: string) => void;
  chatHistoryLoaded: boolean;
}

/**
 * Component that automatically sends the initial prompt when the chat history is loaded
 * Uses a combination of sessionStorage and localStorage to ensure the prompt is only sent once
 */
const AutoSendPrompt: React.FC<AutoSendPromptProps> = ({ initialPrompt, onSend, chatHistoryLoaded }) => {
  const [hasSent, setHasSent] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const maxAttempts = 30;
  const attemptInterval = 1000; // 1 second

  useEffect(() => {
    // Check if this prompt has already been sent by checking localStorage
    const promptSentKey = `prompt-sent-${window.location.pathname}`;
    const hasBeenSent = localStorage.getItem(promptSentKey) === 'true';

    if (hasBeenSent || hasSent || !initialPrompt) {
      // If the prompt has already been sent, clear it from sessionStorage
      if (hasBeenSent) {
        console.log('This prompt has already been sent, clearing from sessionStorage');
        sessionStorage.removeItem('initialChatPrompt');
      }
      return;
    }

    if (chatHistoryLoaded) {
      console.log('Chat history loaded, sending initial prompt...');
      try {
        onSend(initialPrompt);
        console.log('Initial prompt sent successfully!');
        setHasSent(true);

        // Mark this prompt as sent in localStorage to prevent it from being sent again on refresh
        localStorage.setItem(promptSentKey, 'true');

        // Clear the sessionStorage item to prevent it from being used again
        sessionStorage.removeItem('initialChatPrompt');
      } catch (error) {
        console.error('Error sending initial prompt:', error);
      }
    } else if (attempts < maxAttempts) {
      console.log(`Chat history not loaded yet, waiting... (attempt ${attempts + 1}/${maxAttempts})`);
      const timer = setTimeout(() => {
        setAttempts(attempts + 1);
      }, attemptInterval);
      return () => clearTimeout(timer);
    } else {
      console.error('Max attempts reached, chat history not loaded');
      // Clear the sessionStorage item to prevent it from being used again
      sessionStorage.removeItem('initialChatPrompt');
    }
  }, [initialPrompt, onSend, chatHistoryLoaded, hasSent, attempts]);

  // This component doesn't render anything
  return null;
};

export default AutoSendPrompt;
