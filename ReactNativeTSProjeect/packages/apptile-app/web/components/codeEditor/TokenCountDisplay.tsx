import React from 'react';
import {ThemeColors} from './themetype';

interface TokenCountDisplayProps {
  themeColors: ThemeColors;
  usedTokens: number | undefined;
  maxTokens: number | undefined;
}

const TokenCountDisplay: React.FC<TokenCountDisplayProps> = ({themeColors, usedTokens, maxTokens}) => {
  const usedCredits = Math.round(Number(usedTokens) / 1000);
  const maxCredits = Number(maxTokens) / 1000;
  if (usedTokens === undefined || maxTokens === undefined) {
    return null;
  }

  // Calculate percentage of tokens used
  const percentageUsed = Math.min(100, Math.round((usedTokens / maxTokens) * 100));

  // Determine color based on usage
  const getProgressColor = () => {
    if (percentageUsed < 70) return themeColors.EDITOR_ACCENT_COLOR;
    if (percentageUsed < 90) return '#FFA500'; // Orange
    return '#FF4500'; // Red-orange
  };

  return (
    <div
      style={{
        position: 'absolute',
        right: '16px',
        bottom: '100px', // Position above the input area
        backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
        borderRadius: '8px',
        padding: '8px 12px',
        boxShadow: `0px 2px 8px rgba(0,0,0,0.2)`,
        color: themeColors.EDITOR_FOREGROUND_COLOR,
        fontSize: '12px',
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
        width: '230px',
        opacity: 0.9,
        transition: 'all 0.2s ease',
        border: `1px solid ${themeColors.CHAT_BORDER_COLOR || 'rgba(255,255,255,0.1)'}`,
        fontFamily: 'Work Sans, sans-serif',
        zIndex: 10, // Ensure it appears above other elements
        backdropFilter: 'blur(5px)', // Add a subtle blur effect for a modern look
      }}
      onMouseEnter={e => {
        e.currentTarget.style.opacity = '1';
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = `0px 4px 10px rgba(0,0,0,0.25)`;
      }}
      onMouseLeave={e => {
        e.currentTarget.style.opacity = '0.9';
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = `0px 2px 8px rgba(0,0,0,0.2)`;
      }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          fontWeight: 600,
        }}>
        <span style={{fontSize: '11px'}}>Credits:</span>
        <span>
          {usedCredits.toLocaleString()} / {maxCredits.toLocaleString()}
        </span>
      </div>
      <div
        style={{
          width: '100%',
          height: '4px',
          backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
          borderRadius: '2px',
          overflow: 'hidden',
          marginTop: '2px',
          marginBottom: '2px',
        }}>
        <div
          style={{
            width: `${percentageUsed}%`,
            height: '100%',
            backgroundColor: getProgressColor(),
            transition: 'width 0.5s ease-out',
            borderRadius: '2px',
          }}
        />
      </div>
    </div>
  );
};

export default TokenCountDisplay;
