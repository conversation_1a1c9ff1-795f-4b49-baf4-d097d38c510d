export enum EditorModes {
  TEXT = 'text/plain',
  SQL = 'text/x-sql',
  JSON = 'application/x-json',
  JAVASCRIPT = 'text/x-javascript',
  TEXT_WITH_BINDING = 'text-js',
  JSON_WITH_BINDING = 'json-js',
  SQL_WITH_BINDING = 'sql-js',
}

export default function InitModes(CodeMirror: any): void {
  CodeMirror.defineMode(EditorModes.TEXT_WITH_BINDING, function (config) {
    return CodeMirror.default.multiplexingMode(CodeMirror.getMode(config, EditorModes.TEXT), {
      open: '{{',
      close: '}}',
      mode: CodeMirror.getMode(config, {
        name: 'javascript',
      }),
    });
  });

  CodeMirror.defineMode(EditorModes.JSON_WITH_BINDING, function (config) {
    return CodeMirror.default.multiplexingMode(CodeMirror.getMode(config, {name: EditorModes.JSON, json: true}), {
      open: '{{',
      close: '}}',
      mode: CodeMirror.getMode(config, {
        name: 'javascript',
        json: true,
      }),
      delimStyle: 'liquid variable variable-2',
      innerStyle: 'liquid variable variable-2',
    });
  });

  CodeMirror.defineMode(EditorModes.SQL_WITH_BINDING, function (config) {
    return CodeMirror.default.multiplexingMode(CodeMirror.getMode(config, {name: EditorModes.SQL, json: true}), {
      open: '{{',
      close: '}}',
      mode: CodeMirror.getMode(config, {
        name: 'javascript',
      }),
      delimStyle: 'liquid',
      innerStyle: 'liquid',
    });
  });
}
