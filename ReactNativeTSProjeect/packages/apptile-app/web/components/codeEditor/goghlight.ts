import {EditorView} from '@codemirror/view';
import {Extension} from '@codemirror/state';
import {HighlightStyle, syntaxHighlighting} from '@codemirror/language';
import {tags as t} from '@lezer/highlight';
import {ThemeColors} from './themetype';

// Tokyo Night Gogh Light–inspired color palette for a VSCode–like light theme

// Editor UI Colors
const background = '#ffffff'; // Editor background
const foreground = '#383a42'; // Default text color
const darkBackground = '#f3f3f3'; // Panels, gutters background
const tooltipBackground = '#f0f0f0'; // Tooltip background
const selection = '#d0d0d0'; // Selection background
const cursor = foreground; // Cursor color

// Syntax token colors
const commentColor = '#a0a1a7'; // Comments (muted gray)
const keywordColor = '#4078f2'; // Keywords (blue)
const nameColor = '#383a42'; // Names (default text)
const variableColor = '#383a42'; // Variables (default text)
const functionColor = '#a626a4'; // Function names (purple)
const labelColor = '#4078f2'; // Labels (blue)
const constantColor = '#d19a66'; // Constants & numbers (orange)
const typeColor = '#0184bc'; // Types and classes (cyan/blue)
const operatorColor = '#383a42'; // Operators (default text)
const tagColor = '#a626a4'; // Tag names (purple)
const squareBracketColor = '#d16d9e'; // Square brackets (pinkish)
const angleBracketColor = '#d19a66'; // Angle brackets (orange)
const stringColor = '#50a14f'; // Strings (green)
const monospaceColor = '#a626a4'; // Monospace text (purple)

const invalidColor = '#e45649'; // Invalid/error (red)

// Editor theme definition
export const goghLightTheme = EditorView.theme(
  {
    '&': {
      color: foreground,
      backgroundColor: background,
    },

    '.cm-content': {
      caretColor: cursor,
    },

    '.cm-cursor, .cm-dropCursor': {
      borderLeftColor: cursor,
      borderLeftWidth: '2px',
      marginLeft: '-1.5px',
    },
    '&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection':
      {backgroundColor: selection},

    '.cm-panels': {backgroundColor: darkBackground, color: commentColor},
    '.cm-panels.cm-panels-top': {borderBottom: '2px solid #ccc'},
    '.cm-panels.cm-panels-bottom': {borderTop: '2px solid #ccc'},

    '.cm-searchMatch': {
      backgroundColor: 'transparent',
      outline: `1px solid ${keywordColor}`,
    },
    '.cm-searchMatch.cm-searchMatch-selected': {
      backgroundColor: foreground,
      color: background,
    },

    '.cm-activeLine': {backgroundColor: selection},
    '.cm-selectionMatch': {
      backgroundColor: foreground,
      color: darkBackground,
    },

    '&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket': {
      outline: `1px solid ${keywordColor}`,
    },

    '&.cm-focused .cm-matchingBracket': {
      backgroundColor: tooltipBackground,
      color: foreground,
    },

    '.cm-gutters': {
      backgroundColor: background,
      color: commentColor,
      border: 'none',
    },

    '.cm-activeLineGutter': {
      backgroundColor: selection,
      color: foreground,
    },

    '.cm-foldPlaceholder': {
      backgroundColor: 'transparent',
      border: 'none',
      color: '#ddd',
    },

    '.cm-tooltip': {
      border: 'none',
      backgroundColor: tooltipBackground,
    },
    '.cm-tooltip .cm-tooltip-arrow:before': {
      borderTopColor: 'transparent',
      borderBottomColor: 'transparent',
    },
    '.cm-tooltip .cm-tooltip-arrow:after': {
      borderTopColor: tooltipBackground,
      borderBottomColor: tooltipBackground,
    },
    '.cm-tooltip-autocomplete': {
      '& > ul > li[aria-selected]': {
        backgroundColor: selection,
        color: commentColor,
      },
    },
  },
  {dark: false},
);

// Syntax highlighting style
export const goghLightHighlightStyle = HighlightStyle.define([
  {tag: t.keyword, color: keywordColor},
  {
    tag: [t.name, t.deleted, t.character, t.propertyName, t.macroName],
    color: nameColor,
  },
  {tag: [t.variableName], color: variableColor},
  {tag: [t.function(t.variableName)], color: functionColor},
  {tag: [t.labelName], color: labelColor},
  {
    tag: [t.color, t.constant(t.name), t.standard(t.name)],
    color: constantColor,
  },
  {tag: [t.definition(t.name), t.separator], color: typeColor},
  {tag: [t.brace], color: variableColor},
  {
    tag: [t.annotation],
    color: invalidColor,
  },
  {
    tag: [t.number, t.changed, t.modifier, t.self, t.namespace],
    color: constantColor,
  },
  {
    tag: [t.typeName, t.className],
    color: typeColor,
  },
  {
    tag: [t.operator, t.operatorKeyword],
    color: operatorColor,
  },
  {
    tag: [t.tagName],
    color: tagColor,
  },
  {
    tag: [t.squareBracket],
    color: squareBracketColor,
  },
  {
    tag: [t.angleBracket],
    color: angleBracketColor,
  },
  {
    tag: [t.attributeName],
    color: typeColor,
  },
  {
    tag: [t.regexp],
    color: keywordColor,
  },
  {
    tag: [t.quote],
    color: constantColor,
  },
  {tag: [t.string], color: stringColor},
  {
    tag: t.link,
    color: stringColor,
    textDecoration: 'underline',
    textUnderlinePosition: 'under',
  },
  {
    tag: [t.url, t.escape, t.special(t.string)],
    color: variableColor,
  },
  {tag: [t.meta], color: nameColor},
  {tag: [t.monospace], color: monospaceColor, fontStyle: 'italic'},
  {tag: [t.comment], color: commentColor, fontStyle: 'italic'},
  {tag: t.strong, fontWeight: 'bold', color: keywordColor},
  {tag: t.emphasis, fontStyle: 'italic', color: keywordColor},
  {tag: t.strikethrough, textDecoration: 'line-through'},
  {tag: t.heading, fontWeight: 'bold', color: keywordColor},
  {tag: t.special(t.heading1), fontWeight: 'bold', color: keywordColor},
  {tag: t.heading1, fontWeight: 'bold', color: keywordColor},
  {
    tag: [t.heading2, t.heading3, t.heading4],
    fontWeight: 'bold',
    color: keywordColor,
  },
  {
    tag: [t.heading5, t.heading6],
    color: keywordColor,
  },
  {tag: [t.atom, t.bool, t.special(t.variableName)], color: angleBracketColor},
  {
    tag: [t.processingInstruction, t.inserted],
    color: variableColor,
  },
  {
    tag: [t.contentSeparator],
    color: typeColor,
  },
  {tag: t.invalid, color: invalidColor, borderBottom: `1px dotted ${invalidColor}`},
]);

/// Extension to enable the Tokyo Night Gogh Light theme (both editor and highlighting)
export const goghLight: Extension = [goghLightTheme, syntaxHighlighting(goghLightHighlightStyle)];

export const themeColors: ThemeColors = {
  EDITOR_BACKGROUND_COLOR: background,
  EDITOR_FOREGROUND_COLOR: foreground,
  EDITOR_DARK_BACKGROUND: darkBackground,
  EDITOR_ACCENT_COLOR: functionColor,
  EDITOR_CONTRAST_BACKGROUND: commentColor,
  EDITOR_CONSTANT_COLOR: constantColor,
  EDITOR_STRING_COLOR: stringColor,
};
