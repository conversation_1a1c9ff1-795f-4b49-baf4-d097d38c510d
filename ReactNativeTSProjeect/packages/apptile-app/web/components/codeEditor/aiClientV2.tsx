import React, {useRef, useState, RefObject, useCallback, ReactElement, forwardRef, useImperativeHandle} from 'react';
import {useDispatch} from 'react-redux';
import {EditorView} from 'codemirror';
import {ThemeColors} from './themetype';
import PrompterInput, {PrompterInputRefType as PrompterInputRefType} from './promptInput';
import {runChatCompletionRequest, SET_CHAT_RUNNING} from '../../actions/aiActions';
import TokenCountDisplay from './TokenCountDisplay';
import TokenLimitModal from './TokenLimitModal';
import TokenApis from '../../api/TokenApi';
import {useParams} from 'react-router';
import {renderAgentMessage, renderUserMessage, styles} from './aiClientV2Helpers';
import {store} from 'apptile-core';
import {setLandingPagePrompt} from '../../actions/editorActions';
import theme from '../../styles-v2/theme';
import {themeColors} from './darkTheme';
import {DEFAULT_MODEL, DEFAULT_PROVIDER} from './aiModels';
import useMountEffect from '../../common/hooks/useMountEffect';
import {debounce} from 'lodash';

export interface ChatMessagePre {
  id: number;
  chatId: number;
  role: 'user' | 'assistant';
  content_type: 'text' | 'tool_use' | 'tool_result' | 'text/plain' | 'image/jpg' | 'image/jpeg' | 'image/png';
  content: string;
  tool_call_id: string | null;
  createdAt: Date;
  htmlcontent: string | null;
  tool_name: string | null;
  showing_details: boolean;
}

export interface ChatMessage extends ChatMessagePre {
  pluginAgentMessages: ChatMessagePre[];
}

export type ChatHistory = {
  chat: {
    id: number;
    llm_model: string;
    llm_provider: 'openai' | 'claude';
    outputFile: string;
    type: 'widget' | 'datasource';
  };
  messages: ChatMessage[];
};

// Helper function to fetch chat history
export async function fetchChatMessages(
  historyApi: string,
  model: string,
  provider: 'openai' | 'claude',
): Promise<ChatHistory> {
  provider = provider || 'openai';
  const response = await fetch(`${historyApi}/provider/${provider}/model/${model}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch chat history: ${response.statusText}`);
  }
  const history: ChatHistory = await response.json();

  // Find the most recent nocodelayer_generate_code_for_plugin message
  let mostRecentPluginIndex = -1;
  for (let i = history.messages.length - 1; i >= 0; i--) {
    if (
      history.messages[i].tool_name === 'nocodelayer_generate_code_for_plugin' &&
      history.messages[i].content_type === 'tool_use'
    ) {
      mostRecentPluginIndex = i;
      break;
    }
  }

  // Set showing_details based on whether it's the most recent plugin message
  history.messages.forEach((message, index) => {
    if (
      message.tool_name === 'nocodelayer_generate_code_for_plugin' &&
      message.content_type === 'tool_use' &&
      index === mostRecentPluginIndex
    ) {
      // Only the most recent plugin message should be open
      message.showing_details = true;
    } else {
      message.showing_details = false;
    }
  });
  // Validate messages
  // history.messages = history.messages.reverse();
  return history;
}

export function ChatMessage(props: {
  message: ChatMessage;
  themeColors: ThemeColors;
  onToggleDetail: () => void;
  onlyAssistantMessage: boolean;
  setChatHistory?: React.Dispatch<React.SetStateAction<ChatHistory | null>>;
}) {
  const {message, themeColors, onlyAssistantMessage = false} = props;
  let result: ReactElement<any, any>;
  let key = message.id + message.content_type;
  switch (message.role) {
    case 'user':
      result = !onlyAssistantMessage ? (
        <div key={key} style={styles.messageContainer.userContainer} className="message-enter">
          {renderUserMessage(message)}
        </div>
      ) : (
        <div />
      );
      break;
    case 'assistant':
      result = (
        <div key={key} style={styles.messageContainer.assistantContainer} className="message-enter">
          {renderAgentMessage(message, themeColors, props)}
        </div>
      );
      break;
    default: {
      result = (
        <div
          key={key}
          style={{
            color: themeColors.EDITOR_FOREGROUND_COLOR,
          }}>
          Not a renderable message. Content type: {message.content_type}
        </div>
      );
    }
  }

  return result;
}

type AIClientProps = {
  themeColors: ThemeColors;
  historyApi: string;
  completionApi: string;
  editorView: RefObject<EditorView | null>;
  onCompletionRunDone: (error?: any) => void;
  appId: string;
  usePlanner: boolean;
  chatWidthInPercentage: string;
  agentName: string;
  onArchitectClick: () => void;
};

// CSS for the typing indicator using theme colors
const getTypingIndicatorCSS = (themeColors: ThemeColors) => `
@keyframes blink {
  0% { opacity: 0.2; }
  20% { opacity: 1; }
  100% { opacity: 0.2; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-enter {
  animation: fadeIn 0.3s ease-out forwards;
}

.typing-indicator-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 14px 18px;
  background-color: ${themeColors.CHAT_MESSAGE_BACKGROUND || '#313131'};
  border-radius: 16px;
  width: fit-content;
  border: 1px solid ${themeColors.CHAT_BORDER_COLOR || '#444444'};
  box-shadow: 0px 2px 6px rgba(0,0,0,0.15);
  transition: all 0.2s ease-in-out;
}

.typing-indicator {
  display: inline-block;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background-color: ${themeColors.EDITOR_ACCENT_COLOR};
  margin: 0 3px;
  animation: blink 1.4s infinite both;
}

.typing-indicator:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator:nth-child(3) {
  animation-delay: 0.4s;
}
`;

export const AIClientV2 = forwardRef<PrompterInputRefType, AIClientProps>(function(props: AIClientProps, ref) {
  // const [chatContinuationStatus, setChatContinuationStatus] = useState({status: 'notchecked', prompt: ''});
  const [chatHistory, setChatHistory] = useState<ChatHistory | null>(null);
  const [chatRunning, setChatRunning] = useState(false);
  const [tokenCount, setTokenCount] = useState<null | {used: number; max: number}>(null);
  const [showTokenLimitModal, setShowTokenLimitModal] = useState(false);
  const messageContainer = useRef<HTMLDivElement>(null);
  const prompterInputRef = useRef<PrompterInputRefType>(null);
  useImperativeHandle(ref, () => {
    return prompterInputRef.current;
  }, [prompterInputRef.current]);
  const dispatch = useDispatch();
  const isTokenLimitExpired = tokenCount && tokenCount.max >= 0 && tokenCount.used >= tokenCount.max;
  // Destructure props to use in useCallback
  const {historyApi, completionApi, appId, usePlanner, onCompletionRunDone} = props;
  const debouncedRef = useRef<any>(null);
  const updateChatHistory = useCallback(
    (historyApiUrl: string, model: string, provider: 'claude' | 'openai') => {
      if (!debouncedRef.current) {
        debouncedRef.current = debounce((url, modelName, providerName, resolve) => {
          fetchChatMessages(url, modelName, providerName)
            .then(history => {
              setChatHistory(history);
              setTimeout(() => {
                if (messageContainer.current) {
                  messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
                }
                resolve(history);
              }, 200);
            })
            .catch(err => {
              console.error('Failed to get chat history: ', err);
            });
        }, 300); // debounce delay
      }

      return new Promise(resolve => {
        debouncedRef.current(historyApiUrl, model, provider, resolve);
      });
    },
    [setChatHistory, messageContainer],
  );

  // Function to check token usage and update UI
  const params = useParams<{orgId?: string}>();
  const orgId = params.orgId;

  const checkTokenUsage = useCallback(async () => {
    if (!orgId) return;

    try {
      const response = await TokenApis.getTokenStats(orgId);
      const stats = response.data;
      // Set token count even if values are 0, to ensure component renders
      setTokenCount({
        used: stats.totalUsed,
        max: stats.totalAllocated, // Default to 1000 if not provided
      });

      // Show token limit modal if tokens are depleted
      if (stats.totalAllocated > 0 && stats.available <= 0) {
        setShowTokenLimitModal(true);
      }
    } catch (error) {
      console.error('Failed to fetch token usage:', error);
      setTokenCount(null);
    }
  }, [orgId]);

  // Handle sending a new message
  const handleSendMessage = useCallback(
    (historyApiArg: string, message: string, llm_model: string, llm_provider: string) => {
      if (!message.trim()) return false;

      // Check if chat is initialized, similar to V1
      if (!llm_model || !llm_provider) {
        console.error('llm_model or llm_provider is not set!');
        return false;
      }

      // Set status to running
      dispatch({type: SET_CHAT_RUNNING, payload: true});

      if (prompterInputRef.current) {
        prompterInputRef.current.setDisabled(true);
        prompterInputRef.current.clear();
      }

      // Send the message to the AI
      dispatch(
        runChatCompletionRequest({
          prompt: message,
          model: llm_model || DEFAULT_MODEL,
          provider: (llm_provider || DEFAULT_PROVIDER) as 'openai' | 'claude',
          completionUrl: completionApi,
          appId: appId,
          usePlanner: usePlanner,
          liveMessageSubscriber: (_contentHtml: string, _toolCall: string, callUpdateChatHistory: boolean) => {
            // Final update from the server, fetch the official chat history
            //TODO::You can listen to content html and tool call and add it realtime in state to show realtime chat and then finally call this function to fix the ui since it is not decodable.
            setChatRunning(true);
            if (callUpdateChatHistory) {
              updateChatHistory(
                historyApiArg,
                llm_model || DEFAULT_MODEL,
                (llm_provider || DEFAULT_PROVIDER) as 'openai' | 'claude',
              );
              checkTokenUsage();
            }
          },
          onCompleted: (error?: any) => {
            // Re-enable the input
            if (prompterInputRef.current) {
              prompterInputRef.current.setDisabled(false);
            }
            setChatRunning(false);

            // Set status to not running
            dispatch({type: SET_CHAT_RUNNING, payload: false});

            console.log('[AGENT] soft reloading');
            // Uncomment this line to match V1 behavior
            // dispatch(softRestartConfig());

            // Update chat history
            updateChatHistory(
              historyApiArg,
              llm_model || DEFAULT_MODEL,
              (llm_provider || DEFAULT_PROVIDER) as 'openai' | 'claude',
            ).then(() => {
              onCompletionRunDone(error);
            });

            // Check token usage after completion
            checkTokenUsage();
          },
        }),
      );
      return true;
    },
    [dispatch, completionApi, appId, usePlanner, onCompletionRunDone, checkTokenUsage],
  );

  // Handle model selection
  const handleModelSelect = useCallback(
    (model: string, provider: 'openai' | 'claude') => {
      updateChatHistory(historyApi, model, provider);
    },
    [historyApi],
  );

  // Toggle showing details for a message
  const toggleMessageDetails = useCallback(
    (messageId: number) => {
      setChatHistory(prev => {
        if (!prev) return prev;

        const newMessages = prev.messages.map(message => {
          if (message.id === messageId) {
            // Toggle showing_details for this message
            const newShowingDetails = !message.showing_details;

            // Also toggle the next message if it's a tool_result following a tool_use
            const messageIndex = prev.messages.findIndex(m => m.id === messageId);
            const nextMessage = prev.messages[messageIndex + 1];

            if (
              nextMessage &&
              message.content_type === 'tool_use' &&
              nextMessage.content_type === 'tool_result' &&
              message.tool_call_id === nextMessage.tool_call_id
            ) {
              // Update the next message in a separate step
              prev.messages[messageIndex + 1] = {
                ...nextMessage,
                showing_details: newShowingDetails,
              };
            }

            return {
              ...message,
              showing_details: newShowingDetails,
            };
          }
          return message;
        });

        return {
          ...prev,
          messages: newMessages,
        };
      });
    },
    [setChatHistory],
  );

  // Initialize chat history on component mount
  useMountEffect(() => {
    updateChatHistory(historyApi, DEFAULT_MODEL, DEFAULT_PROVIDER).then(() => {
      if (prompterInputRef.current) {
        prompterInputRef.current.clear();
        let initialPrompt = store.getState().editor.get('promptFromLandingPage');
        const div = document.getElementById('prompter-input');
        //Check if token expired
        if (isTokenLimitExpired) {
          setShowTokenLimitModal(true);
          return;
        }
        if (initialPrompt && div) {
          div.innerHTML = initialPrompt;
          handleSendMessage(historyApi, initialPrompt, DEFAULT_MODEL, DEFAULT_PROVIDER);
          dispatch(setLandingPagePrompt(''));
        }
      }
      checkTokenUsage();
    });
  });

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: props.chatWidthInPercentage,
        position: 'relative',
        fontFamily: theme.FONT_FAMILY_BODY,
      }}>
      <div
        style={{
          color: props.themeColors.EDITOR_FOREGROUND_COLOR,
          borderBottomStyle: 'solid',
          borderBottomWidth: 1,
          borderBottomColor: props.themeColors.EDITOR_FOREGROUND_COLOR,
        }}>
        {props.agentName !== 'Architect' && (
          <span>
            <button onClick={props.onArchitectClick}>Architect</button>&nbsp;/&nbsp;
          </span>
        )}
        {props.agentName}
      </div>
      {/* Add the typing indicator CSS */}
      <style>{getTypingIndicatorCSS(props.themeColors)}</style>

      {/* Token limit modal */}
      <TokenLimitModal
        themeColors={themeColors}
        isOpen={showTokenLimitModal}
        onClose={() => setShowTokenLimitModal(false)}
      />

      {/* Chat messages container */}
      <div
        ref={messageContainer}
        style={{
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          padding: 24,
          display: 'flex',
          flexDirection: 'column',
          scrollBehavior: 'smooth',
          backgroundColor: props.themeColors.CHAT_MESSAGE_BACKGROUND || props.themeColors.EDITOR_DARK_BACKGROUND,
          fontFamily: theme.FONT_FAMILY_BODY,
        }}>
        {chatHistory?.messages.map(message => (
          <ChatMessage
            key={message.id}
            message={message}
            themeColors={props.themeColors}
            onToggleDetail={() => toggleMessageDetails(message.id)}
            onlyAssistantMessage={false}
            setChatHistory={setChatHistory}
          />
        ))}
        <div className="message-status">
          {chatRunning && (
            <div className="typing-indicator-container">
              <span className="typing-indicator" />
              <span className="typing-indicator" />
              <span className="typing-indicator" />
            </div>
          )}
        </div>
      </div>

      {/* Bottom section with input */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          padding: '16px',
          borderTop: `1px solid ${props.themeColors.CHAT_BORDER_COLOR || 'rgba(255,255,255,0.1)'}`,
          backgroundColor: props.themeColors.EDITOR_DARK_BACKGROUND,
          position: 'relative', // Add relative positioning to the parent
        }}>
        {/* Token count display with absolute positioning */}
        <TokenCountDisplay themeColors={props.themeColors} usedTokens={tokenCount?.used} maxTokens={tokenCount?.max} />

        {/* Input component */}
        <PrompterInput
          ref={prompterInputRef}
          themeColors={props.themeColors}
          llmProvider={chatHistory?.chat?.llm_provider || DEFAULT_PROVIDER}
          llmModel={chatHistory?.chat?.llm_model || DEFAULT_MODEL}
          isTokenLimitExpired={isTokenLimitExpired ?? false}
          onTokenLimitModalOpen={() => setShowTokenLimitModal(true)}
          onSend={message =>
            handleSendMessage(
              historyApi,
              message,
              chatHistory?.chat?.llm_model || DEFAULT_MODEL,
              chatHistory?.chat?.llm_provider || DEFAULT_PROVIDER,
            )
          }
          onModelSelect={handleModelSelect}
        />
      </div>
    </div>
  );
});
