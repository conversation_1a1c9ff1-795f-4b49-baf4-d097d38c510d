import {ApptileMFAuthDsName} from 'apptile-datasource';
import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';
import {
  selectApptileMFAuthDsConfig,
  selectApptileMFAuthDsModel,
  selectApptileMFAuthDsQueries,
} from '../../selectors/EditorSelectors';
import PluginPropertyEditor from '../../views/propertyInspector/components/PluginPropertyEditor';
import {MFAuthQueryEditorProps} from '../pluginEditorComponents';

const styleSelect = {flex: 1, padding: 8, borderRadius: 4, borderColor: '#ccc'};

const MFAuthQueryInspector: React.FC<MFAuthQueryEditorProps> = ({
  pluginId,
  name,
  value,
  defaultValue,
  configProps,
  pageId,
  entityConfig,
  config,
  configPathSelector,
  onChange,
  onCustomPropChange,
  onUpdateRawValue,
}) => {
  const customActions = useSelector(state => selectApptileMFAuthDsQueries(state));
  const dsModel = useSelector(state => selectApptileMFAuthDsModel(state));
  const dsConfig = useSelector(state => selectApptileMFAuthDsConfig(state));

  const actionName = config?.get('actionName', null);
  const selectedPluginConfigInputParams = config.get('params');
  const queryInputEditorConfig = dsModel ? dsModel.getQueryInputParams(actionName) : {};

  const onQueryChange = (e: any) => {
    const v = e.target.value;
    onCustomPropChange('datasource', dsConfig?.id);
    onCustomPropChange('actionName', v);
  };

  return (
    <View key={pluginId + name}>
      {!customActions && (
        <>
          <View style={styles.editorInputRow}>
            <Text>{`Please setup ${ApptileMFAuthDsName} datasource to enable OTP based Login`} </Text>
          </View>
        </>
      )}

      {customActions && (
        <>
          <View style={styles.editorInputItem}>
            <Text style={styles.editorInputLabel}>Action Type</Text>
            <select name="queryName" style={styleSelect} defaultValue={actionName} onChange={onQueryChange}>
              <option>Select an option</option>
              {customActions &&
                customActions?.map((customAction: string) => {
                  return (
                    <option value={customAction} key={customAction}>
                      {customAction}
                    </option>
                  );
                })}
            </select>
          </View>

          {queryInputEditorConfig &&
            queryInputEditorConfig.map(propEditor => {
              const selector = configPathSelector.slice(0, -1).concat(['params', propEditor.name]);
              return (
                <PluginPropertyEditor
                  key={pluginId + propEditor.name}
                  editor={propEditor}
                  entityConfig={entityConfig}
                  config={selectedPluginConfigInputParams}
                  pageId={pageId}
                  configPathSelector={selector}
                  pluginId={pluginId}
                />
              );
            })}
        </>
      )}
    </View>
  );
};

export default MFAuthQueryInspector;

const styles = StyleSheet.create({
  editorPopup: {
    flex: 1,
    backgroundColor: '#fff',
    width: 300,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    flexBasis: 'auto',
    flexDirection: 'column',
    flexShrink: 0,
  },
  editorHeader: {padding: 5, backgroundColor: '#e3e1e1', flexDirection: 'row', justifyContent: 'space-between'},
  editorWrapper: {
    margin: 10,
  },
  editorInputItem: {flex: 1, flexDirection: 'column', flexWrap: 'wrap', margin: 5},
  editorInputRow: {flex: 1, flexDirection: 'row', flexWrap: 'wrap', margin: 3},
  editorInputLabel: {margin: 5},
  buttonBox: {
    width: '40%',
    alignSelf: 'flex-end',
  },
});
