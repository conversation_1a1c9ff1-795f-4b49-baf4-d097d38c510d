import React from 'react';
import JsonInputControl from '../jsonEditor/JsonInputControl';
import SchemaEditorControl from './schemaBuilder';

type JsonBuilderControlProps = {
  label: string;
  onChange: (value: string) => void;
  onCustomPropChange: (key: string, value: string) => void;
  schema: string;
  value: string;
};

const JsonBuilderControl: React.FC<JsonBuilderControlProps> = ({
  label,
  onChange,
  onCustomPropChange,
  schema = '[]',
  value = '[]',
}) => {
  const parsedSchema = JSON.parse(schema);
  return (
    <>
      <SchemaEditorControl label="Schema Builder" onChange={onCustomPropChange} value={schema} />
      <JsonInputControl label="JSON Builder" onChange={onChange} value={value} schema={parsedSchema} />
    </>
  );
};

export default JsonBuilderControl;
