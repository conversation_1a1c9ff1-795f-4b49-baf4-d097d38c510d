import {MaterialCommunityIcons} from 'apptile-core';
import React, {useEffect} from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';

import DropDownControl from '../DropDownControl';
import TextInput from '../../../components-v2/base/TextInput';
import theme from '@/root/web/styles-v2/theme';

type SchemaEditorControlProps = {
  label: string;
  onChange: (key: string, value: string) => void;
  value: string;
};

const types = ['codeInput', 'checkbox', 'screenSelector'];

const SchemaEditorControl: React.FC<SchemaEditorControlProps> = ({label, onChange, value = '[]'}) => {
  const [dataItems, updateDataItems] = React.useState([]);

  useEffect(() => {
    updateDataItems(Array.isArray(value) ? value : JSON.parse(value) || []);
  }, []);

  const handleOnAdd = (dataItems: any = []) => {
    return () => {
      const existingItems = cloneDeep(dataItems);
      const obj = {key: null, type: null};
      existingItems.push(obj);
      updateDataItems(existingItems);
    };
  };

  const onDelete = (index: number) => {
    return () => {
      const existingItems = cloneDeep(dataItems);
      existingItems.splice(index, 1);
      updateDataItems(existingItems);
    };
  };

  const handleOnChange = (index: number, text: string, key: string) => {
    const existingItems = cloneDeep(dataItems);
    existingItems[index][key] = text;
    updateDataItems(existingItems);
  };

  const debounceFn = React.useCallback(debounce(onChange, 150), []);

  useEffect(() => {
    debounceFn('schema', JSON.stringify(dataItems));
  }, [dataItems]);

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.labelText}>{label}</Text>
        <TouchableOpacity onPress={handleOnAdd(dataItems)}>
          <MaterialCommunityIcons name="plus-circle-outline" size={12} />
        </TouchableOpacity>
      </View>
      {dataItems.map((itemRow, itemIndex) => {
        return (
          <View style={styles.itemContainer} key={itemIndex}>
            <TouchableOpacity onPress={onDelete(itemIndex)} style={styles.deleteIcon}>
              <MaterialCommunityIcons name="delete-circle-outline" size={18} />
            </TouchableOpacity>
            <View style={styles.rowStyle}>
              <TextInput
                placeholder={'Key'}
                label="Key"
                onChange={e => handleOnChange(itemIndex, e.target.value, 'key')}
                value={itemRow.key}
              />
            </View>
            <View style={styles.rowStyle}>
              <DropDownControl
                defaultValue={''}
                label={'Type'}
                onChange={value => handleOnChange(itemIndex, value, 'type')}
                options={types}
                value={itemRow.type}
              />
            </View>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  deleteIcon: {
    alignSelf: 'flex-end',
    flexGrow: 0,
    flexBasis: 'auto',
    marginLeft: 'auto',
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
    maxHeight: 300,
    overflow: 'scroll',
  },
  labelContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowStyle: {
    flex: 1,
    flexDirection: 'row',
  },
  itemContainer: {
    alignContent: 'flex-start',
    padding: 5,
    flexDirection: 'column',
    flexBasis: 'auto',
    backgroundColor: '#FFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    marginBottom: 8,
  },
  labelText: {
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
});

export default SchemaEditorControl;
