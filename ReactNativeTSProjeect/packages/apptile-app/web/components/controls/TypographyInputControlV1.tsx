import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Immutable from 'immutable';
import _ from 'lodash';

import {useTheme} from 'apptile-core';

import PluginPropertyEditor from '@/root/web/views/propertyInspector/components/PluginPropertyEditor';
import ThemePropertyEditor from '@/root/web/views/propertyInspector/components/themePropertyEditor';

import {TypographyInputProps} from '../pluginEditorComponents';
import {strsel} from 'apptile-core';
import {pluginConfigUpdatePath} from 'apptile-core';
import {defaultTypographyEditors} from 'apptile-core';
import {setThemeConfig} from '../../actions/themeActions';

import {selectActiveThemeConfig} from 'apptile-core';
import {ITypographyItem} from 'apptile-core';

import {normalizeFonts} from 'apptile-core';
import {IFontRecord} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {toRespectiveValue} from 'apptile-core';

import Button from '../../components-v2/base/Button';
import DropDownControl from './DropDownControl';
import CodeInputControl from './CodeInputControl';
import DecorationControl from './TextDecorationControl';
import TextTransformControl from './TextTransformControl';
import EditorSectionHeader from './EditorSectionHeader';

interface TypographyInputControlProps extends Omit<TypographyInputProps, 'configProps'> {
  label: string;
  inTheme?: boolean;
  disableExport?: boolean;
}

const TypographyInputControlV1: React.FC<TypographyInputControlProps> = props => {
  const {
    pluginId,
    pageId,
    configPathSelector,
    config,
    value,
    name,
    inTheme,
    disableExport = false,
    onUpdateRawValue,
    label,
    entityConfig,
  } = props;

  const {themeEvaluator} = useTheme();
  const dispatch = useDispatch();
  const themeConfig = useSelector(selectActiveThemeConfig);

  const themeProfile = ['typography'];
  const themePresets = themeProfile ? themeEvaluator(strsel(themeProfile)) : {};
  const presets = Object.keys(themePresets).map(preset => ({name: _.startCase(preset), value: `typography.${preset}`}));

  const updatePreset = useCallback(
    (val: string) => {
      if (!inTheme) dispatch(pluginConfigUpdatePath(pluginId, pageId, configPathSelector.slice(1), {_inherit: val}));
      else
        dispatch(
          setThemeConfig({
            selector: configPathSelector.concat('dependencies'),
            value: [val],
          }),
        );
    },
    [configPathSelector, dispatch, inTheme, pageId, pluginId],
  );

  const [presetValue, setPresetValue] = useState<string>();
  useEffect(() => {
    let preset = inTheme
      ? _.first(_.get(themeConfig, configPathSelector.concat('dependencies'), []))
      : config?.getIn([name, '_inherit']);
    setPresetValue((preset as string) ?? '');
  }, [config, configPathSelector, inTheme, name, themeConfig]);

  const editorConfig = defaultTypographyEditors;

  return (
    <View style={[!disableExport && {margin: 4, padding: 4, borderWidth: 1.5, borderColor: '#0005', borderRadius: 4}]}>
      {label && (
        <View style={{flexDirection: 'row'}}>
          <EditorSectionHeader name={''} label={label} />
          <Button
            variant="TEXT"
            color="SECONDARY"
            size="SMALL"
            icon="restore"
            onPress={() => onUpdateRawValue(Immutable.Map())}>
            Reset
          </Button>
        </View>
      )}
      <DropDownControl
        label="Presets"
        options={presets}
        value={presetValue ?? ''}
        nameKey="name"
        valueKey="value"
        onChange={str => updatePreset(str)}
        defaultValue={presetValue ?? ''}
        disableBinding={true}
      />
      {editorConfig.map((propEditor, idx) => {
        const selector = configPathSelector.concat([propEditor.name]);

        if (inTheme) {
          return <ThemePropertyEditor editor={propEditor} key={idx + propEditor.name} configPathSelector={selector} />;
        }

        return (
          <PluginPropertyEditor
            key={pluginId + propEditor.name}
            editor={propEditor}
            entityConfig={entityConfig}
            config={value}
            pageId={pageId}
            pluginId={pluginId}
            configPathSelector={selector}
            hideExposePropButton={disableExport}
          />
        );
      })}
    </View>
  );
};

interface IBrandTypographyPickerProps {
  value: ITypographyItem;
  onChange: (value: any) => void;
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DADADA',
  },
});

export const BrandTypographyInputControl: React.FC<IBrandTypographyPickerProps> = ({value, onChange}) => {
  const appConfig = useSelector(selectAppConfig);

  let typographyObj: Record<string, any> = value;

  const fonts = (appConfig?.getFonts().toJS() ?? {}) as Record<string, IFontRecord>;
  const normalizedFonts = normalizeFonts(fonts);

  const {fontWeight, fontStyle} = normalizedFonts[typographyObj.fontFamily] ?? {};

  const fontFamilyOptions = Object.keys(normalizedFonts);
  const fontWeightOptions = fontWeight ?? [];
  const fontStyleOptions = fontStyle ?? [];

  const updateTypographyHandler = (styleName: string, styleValue: string) => {
    const construct: Record<string, any> = {};

    for (const key in typographyObj) {
      construct[key] = toRespectiveValue(typographyObj[key]);
    }

    const update = {
      ...construct,
      [styleName]: toRespectiveValue(styleValue),
    };

    onChange(update);
  };

  const updateDropdownVal = (key: string, val: string) => {
    if (val) {
      updateTypographyHandler(key, val);
    }
  };

  return (
    <View style={styles.container}>
      <DropDownControl
        label="Font"
        defaultValue=""
        value={typographyObj.fontFamily}
        onChange={val => updateDropdownVal('fontFamily', val)}
        options={fontFamilyOptions}
        disableBinding
      />
      <DropDownControl
        label="Weight"
        defaultValue=""
        value={_.toString(typographyObj.fontWeight)}
        onChange={val => updateDropdownVal('fontWeight', val)}
        options={fontWeightOptions}
        disableBinding
      />
      <DropDownControl
        label="Style"
        defaultValue=""
        value={typographyObj.fontStyle}
        onChange={val => updateDropdownVal('fontStyle', val)}
        options={fontStyleOptions}
        disableBinding
      />
      <CodeInputControl
        label="Size"
        value={typographyObj.fontSize}
        placeholder="0"
        onChange={val => updateTypographyHandler('fontSize', val)}
        name="fontSize"
      />
      <CodeInputControl
        label="Height"
        value={typographyObj.lineHeight}
        placeholder="0"
        onChange={val => updateTypographyHandler('lineHeight', val)}
        name="lineHeight"
      />
      <CodeInputControl
        label="Spacing"
        value={typographyObj.letterSpacing}
        placeholder="0"
        onChange={val => updateTypographyHandler('letterSpacing', val)}
        name="letterSpacing"
      />
      <DecorationControl
        label="Decoration"
        defaultValue=""
        value={typographyObj.textDecorationLine}
        onChange={val => updateDropdownVal('textDecorationLine', val)}
      />
      <TextTransformControl
        label="Transform"
        defaultValue=""
        value={typographyObj.textTransform}
        onChange={val => updateDropdownVal('textTransform', val)}
      />
    </View>
  );
};

export default TypographyInputControlV1;
