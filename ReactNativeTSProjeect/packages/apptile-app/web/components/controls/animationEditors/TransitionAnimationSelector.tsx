import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import DropDownControl from '../DropDownControl';
import {AnimatedTransitionRecord} from 'apptile-core';
import {
  EnteringAnimationNames,
  ExitingAnimationNames,
  LayoutAnimationNames,
  TransitionAnimationType,
} from 'apptile-core';

export interface TransitionAnimationSelectorProps {
  label: string;
  transitionConfig?: AnimatedTransitionRecord;
  transitionType: TransitionAnimationType;
  onUpdate: (config: AnimatedTransitionRecord | undefined) => void;
}
const transitionTypesList: Record<TransitionAnimationType, string[]> = {
  entering: ['undefined'].concat(EnteringAnimationNames),
  exiting: ['undefined'].concat(ExitingAnimationNames),
  layout: ['undefined'].concat(LayoutAnimationNames),
};

const TransitionAnimationSelector: React.FC<TransitionAnimationSelectorProps> = props => {
  const {label, transitionConfig, transitionType, onUpdate} = props;
  const [configRecord, setConfigRecord] = useState<AnimatedTransitionRecord | undefined>(transitionConfig);
  const [transitionsList, setTransitionsList] = useState<string[]>(transitionTypesList[transitionType]);
  const [selectedTransition, setSelectedTransition] = useState<string>('undefined');

  useEffect(() => {
    if (transitionConfig === undefined) {
      setSelectedTransition('undefined');
    } else {
      setSelectedTransition(transitionConfig?.transition);
    }
  }, [transitionConfig]);

  const onValueChange = useCallback(
    val => {
      setSelectedTransition(val);
      if (val !== 'undefined') {
        onUpdate(
          transitionConfig ? transitionConfig.set('transition', val) : new AnimatedTransitionRecord({transition: val}),
        );
      } else onUpdate(undefined);
    },
    [onUpdate, transitionConfig],
  );

  return (
    <View style={styles.container}>
      <DropDownControl
        label={label}
        value={selectedTransition}
        onChange={onValueChange}
        options={transitionsList}
        disableBinding={true}
        defaultValue={selectedTransition}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  valueInput: {
    width: 32,
    height: 32,
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    textAlign: 'center',
  },
});
export default TransitionAnimationSelector;
