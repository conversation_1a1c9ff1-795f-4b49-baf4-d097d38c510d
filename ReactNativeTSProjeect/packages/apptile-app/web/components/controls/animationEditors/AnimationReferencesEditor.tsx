import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import DropDownControl from '../DropDownControl';
import {WidgetType} from 'apptile-core';
import {AnimSelector} from '@/root/app/common/Animations/apptileAnimationTypes';
import Immutable from 'immutable';
import {resolvePluginAnimations} from 'apptile-core';
import {objsel, strsel} from 'apptile-core';
import {AnimationsEditorProps} from '../../pluginEditorComponents';
import {useSelector} from 'react-redux';
import {selectAnimatedValuesForNamespace} from '@/root/web/selectors/PluginSelectors';

export type AnimationReferencesEditorProps = AnimationsEditorProps & {
  pluginType: WidgetType;
  references: Immutable.Map<string, AnimSelector | undefined> | undefined;
  onUpdate: (references: Immutable.Map<string, AnimSelector | undefined> | undefined) => void;
};

const AnimationReferencesEditor: React.FC<AnimationReferencesEditorProps> = props => {
  const {pluginType, references, onUpdate, entityConfig, pageId} = props;
  const pluginAnimations = resolvePluginAnimations(pluginType);
  const [configMap, setConfigMap] = useState<Immutable.Map<string, AnimSelector | undefined> | undefined>(references);
  const animSelectors = useSelector(state => selectAnimatedValuesForNamespace(state, pageId, entityConfig?.namespace));
  const animationValues = ['undefined'].concat(animSelectors.map(v => strsel(v)));

  useEffect(() => {
    if (references === undefined || references.count() === 0) {
      if (pluginAnimations) {
        let initMap = {};
        for (const [key, conf] of Object.entries(pluginAnimations?.references ?? {})) {
          _.set(initMap, [conf?.name], undefined);
        }
        setConfigMap(Immutable.Map(initMap));
      }
    } else {
      setConfigMap(references);
    }
  }, [pluginAnimations, references]);

  const onValueChange = useCallback(
    (name, val) => {
      let newConfigMap = configMap?.set(name, val !== 'undefined' ? objsel(val) : undefined);
      setConfigMap(newConfigMap);
      onUpdate(newConfigMap);
    },
    [configMap, onUpdate],
  );

  return configMap?.count() ? (
    <View style={styles.container}>
      {configMap
        .entrySeq()
        .toArray()
        .map(([name, animSel]) => (
          <DropDownControl
            label={name}
            value={animSel ? strsel(animSel) : 'undefined'}
            onChange={val => onValueChange(name, val)}
            options={animationValues}
            disableBinding={true}
            defaultValue={animSel ? strsel(animSel) : 'undefined'}
          />
        ))}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  valueInput: {
    width: 32,
    height: 32,
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    textAlign: 'center',
  },
});
export default AnimationReferencesEditor;
