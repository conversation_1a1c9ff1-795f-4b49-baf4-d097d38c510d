import {MaterialCommunityIcons} from 'apptile-core';
import {isEmpty} from 'lodash';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import Immutable from 'immutable';
import Button from '@/root/web/components-v2/base/Button';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import {useTheme} from 'apptile-core';
import {strsel} from 'apptile-core';
import _ from 'lodash';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type PresetControlProps = {
  defaultValue: string;
  label?: string;
  onChange: (value: string) => void;
  value: string;
  onUpdateRawValue: (value: any) => void;
};

const PresetControl: React.FC<PresetControlProps> = ({value, label, onChange, defaultValue, onUpdateRawValue}) => {
  const {themeEvaluator} = useTheme();
  const themePresets = themeEvaluator(strsel(['typography']));
  const options = Object.keys(themePresets).map(preset => ({
    name: _.startCase(preset),
    value: `typography.${preset}`,
    theme: preset,
  }));
  const [selValue, setSelValue] = useState<string>(value);
  const [showPopover, setShowPopover] = useState(false);
  let currentItemName = value || defaultValue;
  let currentItem = null;
  const filteredOption = options.filter(v => v.value === value)[0];
  currentItemName = filteredOption ? filteredOption.name : '';
  currentItem = filteredOption;

  const [currentOption, setCurrentOption] = useState<string>(currentItemName);

  useEffect(() => {
    if (isEmpty(value)) {
      setSelValue(defaultValue);
    } else {
      setSelValue(value);
    }

    let currentItemName = value || defaultValue;
    if (Array.isArray(options) && options.find(v => v.value === value)) {
      const filteredOption = options.filter(v => v.value === value)[0];
      currentItemName = filteredOption ? filteredOption.name : '';
      currentItem = filteredOption;
    }
    setCurrentOption(currentItemName);
  }, [value, options, defaultValue]);

  const onValueChange = useCallback(
    newVal => {
      onChange(newVal);
    },
    [onChange],
  );
  const buttonRef = useRef(null);
  const containerRef = useRef(null);
  return (
    <View ref={containerRef} style={styles.editorInputItem}>
      {label && (
        <View style={commonStyles.labelContainer}>
          <Text style={[commonStyles.labelText]}>{label}</Text>
        </View>
      )}
      <View style={[label ? commonStyles.inputContainer : {width: '100%'}, styles.inputContainer]}>
        <PopoverComponent
          positions={['bottom', 'left', 'right', 'top']}
          visible={showPopover}
          onVisibleChange={setShowPopover}
          trigger={
            <View
              ref={buttonRef}
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: showPopover ? 7 : 8,
                  borderWidth: 1,
                  borderColor: theme.INPUT_BORDER,
                },
                commonStyles.input,
              ]}>
              <Text style={[commonStyles.inputText, {flex: 1, overflow: 'hidden'}]}>
                {currentOption || selValue || 'Select Value'}
              </Text>
              <Text style={{width: 15}}>
                <MaterialCommunityIcons
                  name={showPopover ? 'chevron-up' : 'chevron-down'}
                  size={14}
                  color={theme.SECONDARY_COLOR}
                />
              </Text>
            </View>
          }>
          <View
            style={[
              styles.dropdownOptions,
              buttonRef?.current && {width: buttonRef?.current?.getBoundingClientRect()?.width},
              containerRef?.current && {
                maxHeight: 170,
              },
            ]}>
            <View style={[{flex: 1, height: '100%', overflow: 'scroll'}]}>
              {options.map((item: any, index: number) => {
                const itemName = item.name;
                return (
                  <View key={item.value + '-' + index}>
                    <Pressable
                      style={styles.popoverText}
                      onPress={() => {
                        setShowPopover(false);
                        setCurrentOption(itemName);
                        onValueChange(item.value);
                      }}>
                      <View style={{width: 20}}>
                        {value == item.value ? (
                          <MaterialCommunityIcons name={'check'} size={15} color={theme.PRIMARY_COLOR} />
                        ) : (
                          <MaterialCommunityIcons name={'blank'} size={15} color={theme.PRIMARY_COLOR} />
                        )}
                      </View>
                      <Text style={[themePresets[item.theme], {fontSize: 14, lineHeight: 18}]}>{itemName}</Text>
                    </Pressable>
                  </View>
                );
              })}
            </View>
          </View>
        </PopoverComponent>
        <Button
          variant="FILLED"
          color="TERTIARY"
          backgroundColor="TERTIARY"
          size="SMALL"
          icon="restore"
          onPress={() => onUpdateRawValue(Immutable.Map())}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  editorInputItem: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  dropdownOptions: {
    width: 170,
    maxHeight: 170,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    borderRadius: 9,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    marginTop: 5,
  },
  popoverText: {
    flexDirection: 'row',
    padding: 8,
    textAlign: 'left',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    alignItems: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});

export default PresetControl;
