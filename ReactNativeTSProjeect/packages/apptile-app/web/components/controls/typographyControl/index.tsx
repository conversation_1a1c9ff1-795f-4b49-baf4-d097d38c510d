import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {View, Text, StyleSheet, Pressable, TextInput as TextInputReact} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import Immutable from 'immutable';
import _, {debounce} from 'lodash';

import {useTheme} from 'apptile-core';

import PluginPropertyEditor from '@/root/web/views/propertyInspector/components/PluginPropertyEditor';
import ThemePropertyEditor from '@/root/web/views/propertyInspector/components/themePropertyEditor';

import {TypographyInputProps} from '../../pluginEditorComponents';
import {strsel} from 'apptile-core';
import {pluginConfigUpdatePath} from 'apptile-core';
import {defaultTypographyEditors} from 'apptile-core';
import {setThemeConfig} from '../../../actions/themeActions';

import Fuse from 'fuse.js';
import {selectActiveThemeConfig} from 'apptile-core';
import {ITypographyItem} from 'apptile-core';

import loadedFontList from '@/root/web/fonts/googleFonts.json';
import {toRespectiveValue} from 'apptile-core';

import TextInput from '@/root/web/components-v2/base/TextInput';
import TextElement from '@/root/web/components-v2/base/TextElement';
import Button from '../../../components-v2/base/Button';
import DropDownControl from '../DropDownControl';
import EditorSectionHeader from '../EditorSectionHeader';

import {pickWeightAndStyleType} from 'apptile-core';
import {addFont} from '@/root/web/actions/themeActions';
import {IFontRecord} from 'apptile-core';
import VisibilitySensor from 'react-visibility-sensor';

import {GenerateFontStyleSheet} from '@/root/web/views/settings/brand/fonts/webFontLoader';
import {selectAppConfig} from 'apptile-core';
import PresetControl from './PresetControl';
import RadioGroupControl from '../RadioGroupControl';
import {bindActionCreators} from 'redux';
import {MaterialCommunityIcons} from 'apptile-core';
import {getFontDetailsFromURL} from '@/root/web/fonts/googleFontUtils';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DADADA',
  },
  iconContainerStyle: {
    backgroundColor: 'transparent',
    marginRight: 5,
  },
  searchContainer: {
    borderColor: '#DADADA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    paddingTop: 13,
    paddingBottom: 0,
    marginTop: 8,
  },
  bodyFont: {fontSize: 13},
  fontSearchItem: {
    marginBottom: 13,
  },
  fontSearchContainer: {
    borderColor: '#DADADA',
  },
  fontSizeContainer: {
    width: 50,
  },
  fontChooser: {
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    flexGrow: 1,
    marginRight: 5,
  },
  rowSpaceLayout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  fontChooserHeading: {
    fontSize: 13,
    marginBottom: 5,
  },
  editorInputItem: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  inputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  sizeContainer: {
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    overflow: 'hidden',
  },
  sizeButtons: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: theme.INPUT_BACKGROUND,
    userSelect: false,
  },
  sizeInput: {
    width: 28,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
});

const textInputStyles = {
  textInputStyles: {outline: 'none', paddingVertical: 7},
  inputWrapperStyles: {alignItems: 'center'},
};

interface TypographyInputControlProps extends Omit<TypographyInputProps, 'configProps'> {
  label: string;
  inTheme?: boolean;
  disableExport?: boolean;
}

const TypographyInputControl: React.FC<TypographyInputControlProps> = props => {
  const {
    pluginId,
    pageId,
    configPathSelector,
    config,
    value,
    name,
    inTheme,
    disableExport = false,
    onUpdateRawValue,
    label,
    entityConfig,
  } = props;

  const {themeEvaluator} = useTheme();
  const dispatch = useDispatch();
  const themeConfig = useSelector(selectActiveThemeConfig);

  const themeProfile = ['typography'];
  const themePresets = themeProfile ? themeEvaluator(strsel(themeProfile)) : {};
  const presets = Object.keys(themePresets).map(preset => ({
    name: _.startCase(preset),
    value: `typography.${preset}`,
    theme: preset,
  }));

  const updatePreset = useCallback(
    (val: string) => {
      if (!inTheme) dispatch(pluginConfigUpdatePath(pluginId, pageId, configPathSelector.slice(1), {_inherit: val}));
      else
        dispatch(
          setThemeConfig({
            selector: configPathSelector.concat('dependencies'),
            value: [val],
          }),
        );
    },
    [configPathSelector, dispatch, inTheme, pageId, pluginId],
  );

  const [presetValue, setPresetValue] = useState<string>();
  useEffect(() => {
    let preset = inTheme
      ? _.first(_.get(themeConfig, configPathSelector.concat('dependencies'), []))
      : config?.getIn([name, '_inherit']);
    setPresetValue((preset as string) ?? '');
  }, [config, configPathSelector, inTheme, name, themeConfig]);

  const editorConfig = defaultTypographyEditors;
  const platform = useSelector(state => state.platform);
  // const [isEmbeddedInShopify] = useState(platform?.isEmbeddedInShopify);
  const isEmbeddedInShopify = true;

  const {pluginConfigUpdatePath: updateConfig} = useMemo(
    () => bindActionCreators({pluginConfigUpdatePath}, dispatch),
    [dispatch],
  );

  const debouncedUpdateConfig = debounce(
    (widgetId, pageId, selector, update, remove) => updateConfig(widgetId, pageId, selector, update, remove),
    300,
  );
  const onChange = useCallback(
    (selector: string[], name, v: unknown, debounced = false, remove = false) => {
      if (debounced) {
        debouncedUpdateConfig(pluginId, pageId, selector, remove ? {} : {[name]: v}, remove ? [name] : undefined);
      } else {
        updateConfig(pluginId, pageId, selector, remove ? {} : {[name]: v}, remove ? [name] : undefined);
      }
    },
    [debouncedUpdateConfig, pluginId, pageId, updateConfig],
  );

  const debouncedOnChange = debounce((selector: string[], v: any) => {
    let fontSize = Number(v);
    fontSize = isNaN(fontSize) ? 10 : fontSize;
    onChange(selector, 'fontSize', fontSize);
    onChange(selector, 'lineHeight', fontSize * 1.5);
  }, 400);

  const currentTheme = presets.find(e => e.value == presetValue)?.theme;

  const [size, setSize] = useState(value?.get('fontSize') ?? themePresets[currentTheme]?.fontSize);

  useEffect(() => {
    const fontSize = Number(value?.get('fontSize') ?? themePresets[currentTheme]?.fontSize);
    const newFontSize = isNaN(fontSize) ? `${themePresets?.body?.fontSize}` : `${fontSize}`;
    setSize(newFontSize);
  }, [currentTheme, value]);
  return isEmbeddedInShopify ? (
    <View style={[!disableExport && {margin: 4, padding: 4, borderWidth: 1.5, borderColor: '#0005', borderRadius: 4}]}>
      {label && <EditorSectionHeader name={''} label={label} />}
      <PresetControl
        label="Presets"
        value={presetValue ?? ''}
        onChange={str => updatePreset(str)}
        defaultValue={presetValue ?? ''}
        onUpdateRawValue={onUpdateRawValue}
      />
      <View style={styles.editorInputItem}>
        <View style={commonStyles.labelContainer}>
          <Text style={[commonStyles.labelText]}>Styling</Text>
        </View>
        <View style={[commonStyles.inputContainer, styles.inputContainer]}>
          <View style={[styles.sizeContainer, {borderRadius: theme.INPUT_BORDER_RADIUS}]}>
            <Pressable
              style={[styles.sizeButtons]}
              onPress={() => {
                let fontSize = Number(size);
                fontSize = isNaN(fontSize) ? themePresets?.body?.fontSize : fontSize;
                fontSize = fontSize - 1;
                onChange(configPathSelector.slice(1), 'fontSize', fontSize);
                onChange(configPathSelector.slice(1), 'lineHeight', fontSize * 1.5);
              }}>
              <MaterialCommunityIcons name="minus" size={12} />
            </Pressable>
            <TextInputReact
              value={size}
              style={[commonStyles.inputText, styles.sizeInput]}
              onChange={change => {
                setSize(change?.target?.value);
                debouncedOnChange(configPathSelector.slice(1), change?.target?.value);
              }}
            />
            <Pressable
              style={[styles.sizeButtons]}
              onPress={() => {
                let fontSize = Number(size);
                fontSize = isNaN(fontSize) ? themePresets?.body?.fontSize : fontSize;
                fontSize = fontSize + 1;
                onChange(configPathSelector.slice(1), 'fontSize', fontSize);
                onChange(configPathSelector.slice(1), 'lineHeight', fontSize * 1.5);
              }}>
              <MaterialCommunityIcons name="plus" size={12} />
            </Pressable>
          </View>
          <RadioGroupControl
            value={value?.get('textDecorationLine')}
            options={[
              {
                icon: 'format-underline',
                text: null,
                value: 'underline',
              },
              {
                icon: 'format-strikethrough-variant',
                text: null,
                value: 'line-through',
              },
            ]}
            allowDeselect={true}
            onChange={function (value: string): void {
              onChange(configPathSelector.slice(1), 'textDecorationLine', value == '' ? 'none' : value);
            }}
          />
        </View>
      </View>
    </View>
  ) : (
    <View style={[!disableExport && {margin: 4, padding: 4, borderWidth: 1.5, borderColor: '#0005', borderRadius: 4}]}>
      {label && (
        <View style={{flexDirection: 'row'}}>
          <EditorSectionHeader name={''} label={label} />
          <Button
            variant="TEXT"
            color="SECONDARY"
            size="SMALL"
            icon="restore"
            onPress={() => onUpdateRawValue(Immutable.Map())}>
            Reset
          </Button>
        </View>
      )}
      <DropDownControl
        label="Presets"
        options={presets}
        value={presetValue ?? ''}
        nameKey="name"
        valueKey="value"
        onChange={str => updatePreset(str)}
        defaultValue={presetValue ?? ''}
        disableBinding={true}
      />
      {editorConfig.map((propEditor, idx) => {
        const selector = configPathSelector.concat([propEditor.name]);

        if (inTheme) {
          return <ThemePropertyEditor editor={propEditor} key={idx + propEditor.name} configPathSelector={selector} />;
        }

        return (
          <PluginPropertyEditor
            key={pluginId + propEditor.name}
            editor={propEditor}
            entityConfig={entityConfig}
            config={value}
            pageId={pageId}
            pluginId={pluginId}
            configPathSelector={selector}
            hideExposePropButton={disableExport}
          />
        );
      })}
    </View>
  );
};

interface IBrandTypographyPickerProps {
  value: ITypographyItem;
  onChange: (value: any) => void;
  setPopAsActive: (value: boolean) => void;
}

const STANDARD_LINE_HEIGHT = 1.5;

export const FontChooserControl: React.FC<IBrandTypographyPickerProps> = ({value, onChange, setPopAsActive}) => {
  let typographyObj: Record<string, any> = value;

  const updateTypographyHandler = (styleName: string, styleValue: string) => {
    const update = {
      ...transformDatatype(typographyObj),
      [styleName]: toRespectiveValue(styleValue),
    };

    onChange(update);
  };

  const updateRawTypographyHandler = (update: Record<string, string>) => {
    const updatedTypography = _.omit(
      {
        ...transformDatatype(typographyObj),
        ...transformDatatype(update),
      },
      ['fontWeight', 'fontStyle'],
    );

    onChange(updatedTypography);
  };

  const [fontName, setFontName] = React.useState('');

  return (
    <View style={styles.container}>
      <View style={styles.rowSpaceLayout}>
        <View style={{flexGrow: 1}}>
          <TextElement color="SECONDARY" style={[styles.fontChooserHeading]}>
            Font
          </TextElement>

          <View style={{marginRight: 5}}>
            <TextInput
              textInputStyles={textInputStyles.textInputStyles}
              inputWrapperStyles={textInputStyles.inputWrapperStyles}
              value={fontName}
              onChangeText={val => setFontName(val)}
              iconPosition="RIGHT"
              autoFocus={true}
              placeholder="Search Fonts"
              iconContainerStyle={styles.iconContainerStyle}
              iconColor="#3A3A3A"
              icon="magnify"
            />
          </View>
        </View>

        <View>
          <TextElement color="SECONDARY" style={[styles.fontChooserHeading]}>
            Size
          </TextElement>
          <TextInput
            textInputStyles={textInputStyles.textInputStyles}
            inputWrapperStyles={textInputStyles.inputWrapperStyles}
            defaultValue={typographyObj.fontSize}
            onChangeText={val =>
              updateRawTypographyHandler({fontSize: val, lineHeight: String(Number(val) * STANDARD_LINE_HEIGHT)})
            }
            placeholder="16"
            containerStyle={styles.fontSizeContainer}
          />
        </View>
      </View>

      <FontList
        searchVal={fontName}
        updateTypography={(val: Record<string, string>) => {
          updateRawTypographyHandler(val);
          setFontName('');
          setPopAsActive(false);
        }}
      />
    </View>
  );
};

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  ignoreLocation: true,
  keys: ['family'],
};

interface IFontList {
  updateTypography: (val: Record<string, string>) => void;
  searchVal: string;
}

const FontList: React.FC<IFontList> = props => {
  const {updateTypography, searchVal} = props;

  const appConfig = useSelector(selectAppConfig);
  const fontList = appConfig?.getFonts();

  const customFontsList = getCustomFonts(fontList);
  const [customFontsSearch] = React.useState(new Fuse(customFontsList, FUSE_OPTIONS));
  const [googleFontsSearch] = React.useState(new Fuse(loadedFontList, FUSE_OPTIONS));
  const [googleFontsFilteredItems, setGoogleFontsFilteredItems] = React.useState(googleFontsSearch?.search(searchVal));
  const [customFontsFilteredItems, setCustomFontsFilteredItems] = React.useState(customFontsSearch?.search(searchVal));

  React.useEffect(() => {
    setGoogleFontsFilteredItems(googleFontsSearch.search(searchVal, {limit: 5}));
    setCustomFontsFilteredItems(customFontsSearch.search(searchVal, {limit: 5}));
  }, [customFontsSearch, searchVal, googleFontsSearch]);

  return (
    <View style={{marginTop: 4}}>
      {googleFontsFilteredItems.length !== 0 && (
        <View>
          {customFontsFilteredItems.map((entry, filterIdx) => (
            <FontItem
              key={`Custom-${filterIdx}`}
              family={entry.item.family}
              variants={entry.item.variants}
              category={entry.item.category}
              files={entry.item.files}
              updateTypography={updateTypography}
              provider="apptileFonts"
            />
          ))}
          {googleFontsFilteredItems.map((entry, filterIdx) => (
            <FontItem
              key={`Google-${filterIdx}`}
              family={entry.item.family}
              variants={entry.item.variants}
              category={entry.item.category}
              files={entry.item.files}
              updateTypography={updateTypography}
              provider="googleFonts"
            />
          ))}
        </View>
      )}
    </View>
  );
};

type FontItemProps = {
  family: string;
  variants: string[];
  category: string;
  files: Record<string, any>;
  provider: string;
  updateTypography: (val: Record<string, string>) => void;
};
const FontItem: React.FC<FontItemProps> = props => {
  const {family, variants, category, files, provider, updateTypography} = props;
  const [isCollapsed, setCollapsed] = React.useState(true);

  const appConfig = useSelector(selectAppConfig);
  const fontList = appConfig?.getFonts().toJS() ?? {};

  const dispatch = useDispatch();

  const constructFontRecord = (variant: string) => {
    return {
      category: category,
      family: family,
      fileUrl: _.get(files, variant),
      provider: provider,
      variant: variant,
    };
  };

  const saveFont = async (variant: string) => {
    const constructedFont = constructFontRecord(variant);
    const isFontAlreadyExist = _.findKey(fontList, constructedFont);
    const fontInfo = await getFontDetailsFromURL(constructedFont.fileUrl);

    if (!isFontAlreadyExist) {
      dispatch(addFont({...constructedFont, postScriptName: fontInfo.postScriptName}));
    }

    updateTypography({
      fontFamily: fontInfo.postScriptName,
      // fontWeight: _.get(pickWeightAndStyleType(variant), 'fontWeight'),
      // fontStyle: _.get(pickWeightAndStyleType(variant), 'fontStyle'),
    });
  };

  return (
    <View style={styles.searchContainer}>
      <Pressable onPress={() => setCollapsed(prev => !prev)} style={styles.rowSpaceLayout}>
        <TextElement style={[styles.bodyFont, styles.fontSearchItem]} color="SECONDARY">
          {family}
        </TextElement>
      </Pressable>

      {!isCollapsed &&
        variants.map((variant, variantIdx) => (
          <FontVariantItem key={variantIdx} onPress={() => saveFont(variant)} font={constructFontRecord(variant)} />
        ))}
    </View>
  );
};

type FontVariantItemProps = {
  onPress: () => void;
  font: IFontRecord;
};

const FontVariantItem: React.FC<FontVariantItemProps> = props => {
  const {onPress, font} = props;
  const [isVisible, setVisible] = React.useState(false);
  const {fontStyle, fontWeight} = pickWeightAndStyleType(font.variant);

  const generateStyle = {
    fontFamily: font.postScriptName,
  };
  return (
    <>
      {isVisible && <GenerateFontStyleSheet font={font} />}
      <VisibilitySensor
        partialVisibility
        offset={{top: 10}}
        onChange={visibility => {
          if (visibility) {
            setVisible(visibility);
          }
        }}>
        <Pressable onPress={onPress}>
          <TextElement
            style={[styles.bodyFont, styles.fontSearchItem, generateStyle]}
            color="EDITOR_LIGHT_BLACK"
            fontWeight="500">
            {`${_.get(fontCommonWeightName, fontWeight)} ${fontWeight} ${fontStyle === 'italic' ? 'italic' : ''}`}
          </TextElement>
        </Pressable>
      </VisibilitySensor>
    </>
  );
};

export const fontCommonWeightName = {
  '100': 'Thin',
  '200': 'ExtraLight',
  '300': 'Light',
  '400': 'Regular',
  '500': 'Medium',
  '600': 'SemiBold',
  '700': 'Bold',
  '800': 'ExtraBold',
  '900': 'Black',
};

const getCustomFonts = (font: Immutable.Map<string, IFontRecord>) => {
  const fonts = font.toJS() ?? {};
  const fontGrpByFamily = _.groupBy(_.filter(fonts, {provider: 'apptileFonts'}), 'family');

  const constructedFontList: {
    family: string;
    category: string;
    variants: string[];
    files: Record<string, string>;
  }[] = [];

  Object.entries(fontGrpByFamily).forEach(entry => {
    const [family, fontObjArr] = entry;

    const variants: string[] = [];
    const files: Record<string, string> = {};
    let category = '';

    fontObjArr.forEach(fontVariantEntry => {
      category = fontVariantEntry.category;
      variants.push(fontVariantEntry.variant);
      files[fontVariantEntry.variant] = fontVariantEntry.fileUrl;
    });

    constructedFontList.push({
      family,
      category,
      variants,
      files,
    });
  });

  return constructedFontList;
};

const transformDatatype = (value: Record<string, any>) => {
  return _.transform(
    value,
    (result, val, key) => {
      result[key] = toRespectiveValue(val);
    },
    {},
  );
};

export default TypographyInputControl;
