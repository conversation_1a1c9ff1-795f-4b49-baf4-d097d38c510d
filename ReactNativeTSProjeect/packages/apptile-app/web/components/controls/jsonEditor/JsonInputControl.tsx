import React, {useEffect} from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';

import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import {pluginEditorComponents} from '../../pluginEditorComponents';

type JsonInputControlProps = {
  label?: string;
  onChange: (value: string) => void;
  value: string;
  schema: {key: string; type: string, props?: any}[];
};

const JsonInputControl: React.FC<JsonInputControlProps> = ({label, onChange, value = '[]', schema = []}) => {
  const [dataItems, updateDataItems] = React.useState([]);

  useEffect(() => {
    try {
      updateDataItems(Array.isArray(value) ? value : JSON.parse(value) || []);
    } catch (ex) {
      updateDataItems([]);
    }
  }, []);

  const handleOnAdd = (dataItems: any = []) => {
    return () => {
      const existingItems = cloneDeep(dataItems);
      const obj = {};
      schema.forEach((item: any) => {
        const {key = ''} = item;
        Object.assign(obj, {[key]: null});
      });
      existingItems.push(obj);
      updateDataItems(existingItems);
    };
  };

  const onDelete = (index: number) => {
    return () => {
      const existingItems = cloneDeep(dataItems);
      existingItems.splice(index, 1);
      updateDataItems(existingItems);
    };
  };

  const handleOnChange = (text: string, index: number, name: string) => {
    const existingItems = cloneDeep(dataItems);
    existingItems[index][name] = text;
    updateDataItems(existingItems);
  };

  const debounceFn = React.useCallback(debounce(onChange, 150), []);

  useEffect(() => {
    debounceFn(JSON.stringify(dataItems));
  }, [dataItems]);

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.labelText}>{label}</Text>
        <TouchableOpacity onPress={handleOnAdd(dataItems)}>
          <MaterialCommunityIcons name="plus-circle-outline" size={16} />
        </TouchableOpacity>
      </View>
      {!!dataItems.length &&
        dataItems.map((itemRow, itemIndex) => {
          return (
            <View style={styles.itemContainer} key={itemIndex}>
              <TouchableOpacity onPress={onDelete(itemIndex)} style={styles.deleteIcon}>
                <MaterialCommunityIcons name="delete-circle-outline" size={18} />
              </TouchableOpacity>
              {Object.keys(itemRow).map((item, rowIndex) => {
                const selectedRow = schema.find(({key = ''}) => key === item);
                let type = 'codeInput';
                let inputProps = {};
                if (selectedRow && selectedRow.type) {
                  type = selectedRow.type;
                }
                if (selectedRow && selectedRow.props) {
                  inputProps = selectedRow.props;
                }
                const EditorComponent = pluginEditorComponents[type];
                return (
                  <View style={styles.itemRow} key={`${item}-${rowIndex}`}>
                    <EditorComponent
                      configProps={{label: item, placeholder: item, ...inputProps}}
                      onChange={(text: string) => handleOnChange(text, itemIndex, item)}
                      value={itemRow[item]}
                    />
                  </View>
                );
              })}
            </View>
          );
        })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
    maxHeight: 300,
    overflow: 'scroll',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  itemContainer: {
    alignContent: 'flex-start',
    padding: 5,
    flexDirection: 'column',
    flexBasis: 'auto',
    backgroundColor: '#FFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    marginBottom: 8,
  },
  labelContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    marginVertical: 8,
  },
  deleteIcon: {
    alignSelf: 'flex-end',
    flexGrow: 0,
    flexBasis: 'auto',
    marginLeft: 'auto',
  },
  itemRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemLabel: {
    marginRight: 8,
  },
  labelText: {
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
});

export default JsonInputControl;
