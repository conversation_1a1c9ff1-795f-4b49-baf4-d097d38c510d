import {PageConfig} from 'apptile-core';
import {pageConfigsSelector} from 'apptile-core';
import Immutable from 'immutable';
import React, {useCallback, useEffect, useState} from 'react';
import {connect} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import DropDownControl from './DropDownControl';

type HeaderIdSelectorProps = {
  defaultValue: string;
  label: string;
  value: string;
  pages: Immutable.Map<string, PageConfig>;
  disableBinding?: boolean;
  onCustomPropChange: (key: string, value: any) => void;
};

const HeaderIdSelectorControl: React.FC<HeaderIdSelectorProps> = ({
  value,
  label,
  defaultValue,
  disableBinding,
  pages,
  onCustomPropChange,
}) => {
  const [headerOptions, setHeaderOptions] = useState([]);

  useEffect(() => {
    setHeaderOptions(
      pages
        ?.toList()
        .toJS()
        .filter(page => page.type === 'Header'),
    );
  }, [pages]);

  const onValueChange = useCallback(
    newVal => {
      onCustomPropChange('header', newVal);
    },
    [onCustomPropChange],
  );

  return (
    <DropDownControl
      label={label}
      value={value}
      onChange={onValueChange}
      options={headerOptions}
      valueKey="pageId"
      defaultValue={''}
      disableBinding={disableBinding}
    />
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {};
};
const mapStateToProps = (state: EditorRootState) => {
  return {
    pages: pageConfigsSelector(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(HeaderIdSelectorControl);
