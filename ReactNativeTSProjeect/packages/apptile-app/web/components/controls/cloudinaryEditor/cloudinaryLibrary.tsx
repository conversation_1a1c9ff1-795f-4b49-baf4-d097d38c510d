import theme from '@/root/web/styles-v2/theme';
import {default as React, useEffect} from 'react';
import {ActivityIndicator, FlatList, Image, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import VideoCard from './videoCard';
import {ICloudinaryVideo} from '@/root/web/api/ApiTypes';
import {videoAssetsMetadataSelector, videoAssetsSelector} from '@/root/web/selectors/AssetsSelector';
import {fetchVideoAssets} from '@/root/web/actions/editorActions';

interface IVideoLibrary {
  setSelectedVideo: (videoUrl: string) => void;
  selectedVideo: string;
}

const CloudinaryLibrary: React.FC<IVideoLibrary> = props => {
  const {setSelectedVideo, selectedVideo} = props;
  const videosByUrl = useSelector(videoAssetsSelector);
  const dispatch = useDispatch();
  const videoList = Object.values(videosByUrl);
  //Sort on the basis of created date
  videoList.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  //set selectedVideo with the first video
  useEffect(() => {
    if (videoList.length > 0) {
      setSelectedVideo(videoList[0].url);
    }
  }, []);

  const fetching = useSelector(videoAssetsMetadataSelector).isFetching;
  const nextCursor = useSelector(videoAssetsMetadataSelector).nextCursor;
  const handleOnEndReached = () => {
    if (nextCursor) {
      dispatch(fetchVideoAssets(nextCursor));
    }
  };

  const RenderItem = ({item, index}: {item: ICloudinaryVideo; index: number}) => {
    return <VideoCard {...{selectedVideo, video: item, onSelectVideo: setSelectedVideo}} index={index} />;
  };

  return (
    <View style={styles.container}>
      {!fetching && videoList.length === 0 && (
        <Image style={styles.emptyImage} source={require('../../../assets/images/snapshot-no-result.png')} />
      )}
      {(fetching || videoList.length > 0) && (
        <FlatList
          data={videoList}
          style={{
            height: 350,
          }}
          renderItem={RenderItem}
          numColumns={5}
          keyExtractor={(item, index) => String(index)}
          ListFooterComponent={() => {
            if (fetching) {
              return <ActivityIndicator />;
            } else {
              return null;
            }
          }}
          horizontal={false}
          onEndReached={handleOnEndReached}
          onEndReachedThreshold={0}
          contentContainerStyle={styles.list}
          columnWrapperStyle={styles.column}
        />
      )}
    </View>
  );
};

export default CloudinaryLibrary;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyImage: {
    width: 200,
    height: 200,
    alignSelf: 'center',
  },
  list: {
    flex: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  column: {
    flexShrink: 1,
    justifyContent: 'flex-start',
    gap: 24,
  },
  listItemWrapper: {
    width: '22%',
    borderWidth: 2,
    borderRadius: 8,
    borderColor: '#ccc',
    overflow: 'hidden',
    margin: 10,
    padding: 4,
  },
  listItemSelected: {
    borderWidth: 2,
    borderColor: '#2196f3',
  },
  listItemImage: {
    width: '100%',
    minHeight: 150,
  },
  headingContainer: {
    flexDirection: 'row',
  },
  heading: {
    flex: 1,
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '700',
    fontSize: 15,
    lineHeight: 17,
    marginTop: 8,
    marginBottom: 16,
  },
});
