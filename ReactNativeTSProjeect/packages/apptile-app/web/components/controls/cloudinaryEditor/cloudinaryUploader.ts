import {fetchVideoAssets} from '@/root/web/actions/editorActions';
import {store} from 'apptile-core';
import {CLOUDINARY_CLOUD_NAME, CLOUDINARY_UPLOAD_PRESET} from '@/root/.env.json';

export default function (appId: string) {
  return window.cloudinary.createUploadWidget(
    {
      cloudName: CLOUDINARY_CLOUD_NAME,
      uploadPreset: CLOUDINARY_UPLOAD_PRESET,
      sources: ['local', 'url'],
      folder: appId,
      multiple: false,
    },
    function (error, result) {
      if (result.info.playback_url) {
        store.dispatch(fetchVideoAssets());
      }
      if (error) {
        console.log('error', error);
      }
    },
  );
}
