import {ICloudinaryVideo} from '@/root/web/api/ApiTypes';
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect} from 'react';
import {Image, ImageSourcePropType, StyleSheet, TouchableOpacity, View} from 'react-native';

export interface IVideoCardProps {
  selectedVideo: string;
  onSelectVideo: (VideoId: string) => void;
  video: ICloudinaryVideo;
  index: number;
}

const VideoCard: React.FC<IVideoCardProps> = props => {
  const {selectedVideo, onSelectVideo, index, video} = props;
  const {url: videoUrl, thumbnail} = video;
  const isSelected = videoUrl === selectedVideo;

  return (
    <TouchableOpacity
      style={[
        styles.CardWrapper,
        isSelected || (selectedVideo && isSelected) || !selectedVideo ? styles.selectedImageCard : {},
      ]}
      onPress={() => onSelectVideo(videoUrl)}>
      {thumbnail && (
        <>
          <Image style={styles.cardImage} resizeMode="contain" source={thumbnail as ImageSourcePropType} />
          {isSelected && (
            <View style={{position: 'absolute', top: 0, left: 0, height: 112, width: 112, backgroundColor: '#0008'}} />
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

export default VideoCard;

const styles = StyleSheet.create({
  CardWrapper: {
    width: 112,
    height: 112,
    borderRadius: 4,
    overflow: 'hidden',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  selectedImageCard: {
    borderWidth: 2,
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  cardImage: {
    height: 112,
    width: 112,
  },
  cardActiveColor: {color: '#fff', textAlign: 'center'},
});
