import React, {useState, useEffect} from 'react';
import {StyleSheet, Text, View, Pressable} from 'react-native';
import {debounce} from 'lodash';
import {assetEditorProps} from '../../pluginEditorComponents';
import VideoChooseDialog from './videoChooseDialog';
import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import CodeInputControlV2 from '../../controls-v2/CodeInputControl';
import {useSelector} from 'react-redux';
import {videoAssetsSelector} from '@/root/web/selectors/AssetsSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


interface assetEditorCustomProps extends assetEditorProps {
  setOpened?: any;
  configProps: {
    urlProperty: string;
    label: string;
    disableBinding?: boolean;
  };
  config: any;
  onCustomPropChange: (key: string, value: any) => void;
}

const CloudinaryEditor: React.FC<assetEditorCustomProps> = props => {
  const {config, configProps, onCustomPropChange, setOpened} = props;
  const videosList = useSelector(videoAssetsSelector);

  const {urlProperty, label, disableBinding} = configProps;
  const [showPopover, setShowPopover] = useState(false);

  useEffect(() => {
    if (setOpened) setOpened(showPopover);
  }, [setOpened, showPopover]);

  const onChooseVideo = (videoUrl: string) => {
    if (videoUrl) {
      onCustomPropChange(urlProperty, videoUrl);
      setShowPopover(false);
    }
  };

  const [isBinding, setIsBinding] = useState(false);
  const debouncedOnValueChange = debounce((newVal, type) => {
    onCustomPropChange(configProps[type], newVal);
  }, 450);

  const thumbnailUrl = videosList[config.get(urlProperty)]?.thumbnail || '';
  return (
    <>
      <View style={styles.container}>
        {label && (
          <View style={[commonStyles.labelContainer, {alignSelf: 'flex-start', paddingTop: 2}]}>
            {!disableBinding && !isBinding && (
              <Pressable onPress={() => setIsBinding(true)}>
                <MaterialCommunityIcons name="flash" size={18} />
              </Pressable>
            )}
            {!disableBinding && isBinding && (
              <Pressable onPress={() => setIsBinding(false)}>
                <MaterialCommunityIcons name="flash-off" size={18} />
              </Pressable>
            )}
            {!isBinding && (
              <View style={styles.labelTextWrapper}>
                <Text style={[commonStyles.labelText]}>{label}</Text>
              </View>
            )}
          </View>
        )}
        {!isBinding && (
          <>
            <Pressable
              onPress={() => {
                if (setOpened) setOpened(true);
                setShowPopover(true);
              }}
              style={[styles.inputStyle, label ? commonStyles.inputContainer : {width: '100%'}]}>
              <View style={[styles.inputImage]}>
                {thumbnailUrl && <img src={thumbnailUrl} style={{width: '100%', height: '100%'}} />}
                {!thumbnailUrl && (
                  <MaterialCommunityIcons name="video" size={50} color={theme.CONTROL_PLACEHOLDER_COLOR} />
                )}
              </View>
              <View style={[commonStyles.input, styles.editButton]}>
                <ApptileWebIcon name={'edit-icon'} size={16} />
                <Text style={[commonStyles.baseText, {justifyContent: 'center'}]}> Edit</Text>
              </View>
            </Pressable>

            <VideoChooseDialog
              onChooseVideo={onChooseVideo}
              onCloseDialog={value => {
                if (setOpened) setOpened(value);
                setShowPopover(value);
              }}
              showDialog={showPopover}
              {...props}
            />
          </>
        )}
        <View style={{flexDirection: 'column'}}>
          {isBinding && (
            <View style={[{flexDirection: 'row', alignItems: 'center'}, commonStyles.controlContainer]}>
              <CodeInputControlV2
                label={'url'}
                defaultValue={config.get(urlProperty)}
                value={config.get(urlProperty)}
                placeholder="{{item.value}}"
                onChange={(val: string) => debouncedOnValueChange(val, 'value')}
              />
            </View>
          )}
        </View>
      </View>
    </>
  );
};

export default CloudinaryEditor;

const styles = StyleSheet.create({
  codeInputStyle: {
    flex: 4,
    borderRadius: 2,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  textInputStyle: {
    fontSize: 11,
    padding: 4,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  container: {
    flex: 1,
    flexShrink: 0,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: 4,
  },
  labelTextWrapper: {
    flexDirection: 'column',
    width: '100%',
  },
  aspectText: {
    fontSize: 10,
    color: 'grey',
    marginTop: 5,
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  inputStyle: {
    flexDirection: 'row',
    borderRadius: 6,
    backgroundColor: theme.INPUT_BACKGROUND,
    height: 172,
    overflow: 'hidden',
  },
  inputImage: {
    height: 172,
    width: '100%',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  urlText: {
    width: 'calc( 100% - 78px)',
    paddingVertical: '22px',
    paddingHorizontal: '2px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  rowContainer: {
    padding: 2,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btn: {
    padding: 4,
    fontSize: 10,
    backgroundColor: '#0091bc',
    color: '#fff',
    flex: 1,
  },
  editButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 8,
    flex: 1,
    padding: 4,
    borderRadius: 20,
    position: 'absolute',
    right: 10,
    bottom: 10,
  },
});
