import Immutable from 'immutable';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useDerivedValue, useSharedValue, withTiming} from 'react-native-reanimated';
import {ThemeEditorBaseProps, PropertyEditorProps} from '@/root/app/common/EditorControlTypes';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';

import {getTheme} from '../../utils/themeSelector';
import {getCommonStyles} from '../../utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type TRBLProps = {
  value: string | number;
  defaultValue: string;
  name: string;
  label: string;
  config: Immutable.Map<string, string>;
  options: [string, string, string, string];
  onChange: (value: boolean) => void;
  onCustomPropChange: (key: string, value: string, debounced: boolean, remove?: boolean) => void;
} & ThemeEditorBaseProps &
  PropertyEditorProps;

const TRBLControl: React.FC<TRBLProps> = props => {
  const {
    value,
    label,
    name,
    config,
    options,
    onChange,
    onCustomPropChange,
    themeIndication,
    getThemeObj,
    configPathSelector,
  } = props;
  const [mainValue, setMainValue] = useState('');
  const [topValue, setTopValue] = useState('');
  const [rightValue, setRightValue] = useState('');
  const [bottomValue, setBottomValue] = useState('');
  const [leftValue, setLeftValue] = useState('');
  const [isExpanded, setIsExpanded] = useState<boolean>(!!topValue || !!rightValue || !!bottomValue || !!leftValue);

  useEffect(() => {
    setMainValue(value ? String(value) : '');
    setTopValue(config?.get(options[0]) ? String(config.get(options[0])) : '');
    setRightValue(config?.get(options[1]) ? String(config.get(options[1])) : '');
    setBottomValue(config?.get(options[2]) ? String(config.get(options[2])) : '');
    setLeftValue(config?.get(options[3]) ? String(config.get(options[3])) : '');
  }, [config, options, value]);

  const debouncedOnCustomPropChange = _.debounce(
    (key: string, val: string, debounced: boolean, remove?: boolean) => onCustomPropChange(key, val, debounced, remove),
    250,
  );
  const icon_rotate = useSharedValue(0);
  const rotateZ = useDerivedValue(() => {
    return withTiming(icon_rotate.value * 90 - 90, {
      duration: 250,
      easing: Easing.linear,
    });
  });
  const animatedRotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{rotateZ: `${rotateZ.value}deg`}],
    };
  });

  // Cleaning the entries in config while toggle
  const cleanUp = _.debounce((cleanup: 'main' | 'edge') => {
    if (cleanup === 'main') {
      if (!_.isEmpty(mainValue)) {
        setMainValue('');
        onCustomPropChange(name, '');
      }
    } else {
      if (!_.isEmpty(topValue)) {
        setTopValue('');
        onCustomPropChange(options[0], '');
      }

      if (!_.isEmpty(rightValue)) {
        setRightValue('');
        onCustomPropChange(options[1], '');
      }

      if (!_.isEmpty(bottomValue)) {
        setBottomValue('');
        onCustomPropChange(options[2], '');
      }

      if (!_.isEmpty(leftValue)) {
        setLeftValue('');
        onCustomPropChange(options[3], '');
      }
    }
  });

  const onMainValueChange = useCallback(
    (val: string) => {
      let newVal = Number(val).toString();
      if (val.trim() == '') newVal = '';
      else if (val.trim().endsWith('.')) newVal += '.';
      if (!isNaN(newVal)) {
        setMainValue(newVal);
        debouncedOnCustomPropChange(name, newVal, true, _.isEmpty(_.trim(newVal)));
        cleanUp('edge');
      }
    },
    [cleanUp, debouncedOnCustomPropChange, name],
  );
  // const debouncedMainValueChange = _.debounce(newVal => onMainValueChange(newVal), 450);

  const onEdgeValueChange = useCallback(
    (val, idx) => {
      let newVal = Number(val).toString();
      if (val.trim() == '') newVal = '';
      else if (val.trim().endsWith('.')) newVal += '.';
      if (!isNaN(newVal)) {
        switch (idx) {
          case 0:
            setTopValue(newVal);
            break;
          case 1:
            setRightValue(newVal);
            break;
          case 2:
            setBottomValue(newVal);
            break;
          case 3:
            setLeftValue(newVal);
            break;
        }
        debouncedOnCustomPropChange(options[idx], newVal, true, _.isEmpty(_.trim(newVal)));
        cleanUp('main');
      }
    },
    [cleanUp, debouncedOnCustomPropChange, options],
  );
  // const debouncedEdgeValueChange = _.debounce((newVal, idx) => onEdgeValueChange(newVal, idx), 450);

  const onFocus = useCallback(
    idx => {
      icon_rotate.value = idx;
    },
    [icon_rotate],
  );

  return (
    <View style={styles.container}>
      <View style={styles.splitRowContainer}>
        <View style={[styles.rowContainer]}>
          {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
          <View style={[label ? commonStyles.inputContainer : {width: '100%'}]}>
            <View style={[styles.rowContainer, {width: '100%'}]}>
              <TextInput
                style={[
                  styles.valueInput,
                  {
                    paddingLeft: 8,
                    paddingRight: 8,
                    width: '95%',
                    color:
                      isExpanded || !!topValue || !!rightValue || !!bottomValue || !!leftValue
                        ? theme.CONTROL_PLACEHOLDER_COLOR
                        : theme.CONTROL_INPUT_COLOR,
                  },
                  commonStyles.input,
                ]}
                blurOnSubmit={true}
                placeholder="0"
                value={!!topValue || !!rightValue || !!bottomValue || !!leftValue ? 'Mixed' : mainValue}
                editable={isExpanded || !!topValue || !!rightValue || !!bottomValue || !!leftValue ? false : true}
                onChangeText={onMainValueChange}
              />
              <Pressable
                style={[styles.button, commonStyles.input, isExpanded && styles.buttonActive]}
                onPress={() => {
                  setIsExpanded(!isExpanded);
                }}>
                <View
                  style={[
                    styles.switchButton,
                    {borderColor: isExpanded ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR},
                  ]}
                />
                {/* <ApptileWebIcon name="TRBL-button" size={24} /> */}
              </Pressable>
            </View>
            {isExpanded && (
              <View style={[styles.rowContainer, commonStyles.input, styles.bottomBar]}>
                <View style={[styles.bottomBarBox, {borderLeftWidth: 0}]}>
                  <Animated.View style={[animatedRotateStyle]}>
                    <View style={styles.switchButtonRight} />
                  </Animated.View>
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={topValue}
                    configPathSelector={options[0] ? [...configPathSelector.slice(0, -1), options[0]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onEdgeValueChange(text, 0)}
                    value={topValue}
                    placeholder="0"
                    onFocus={() => onFocus(0)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={rightValue}
                    configPathSelector={options[1] ? [...configPathSelector.slice(0, -1), options[1]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onEdgeValueChange(text, 1)}
                    value={rightValue}
                    placeholder="0"
                    onFocus={() => onFocus(1)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={bottomValue}
                    configPathSelector={options[2] ? [...configPathSelector.slice(0, -1), options[2]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onEdgeValueChange(text, 2)}
                    value={bottomValue}
                    placeholder="0"
                    onFocus={() => onFocus(2)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={leftValue}
                    configPathSelector={options[3] ? [...configPathSelector.slice(0, -1), options[3]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onEdgeValueChange(text, 3)}
                    value={leftValue}
                    placeholder="0"
                    onFocus={() => onFocus(3)}
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

type BubbleProps = {configPathSelector: string[]; value: any} & ThemeEditorBaseProps;
export const Bubble: React.FC<BubbleProps> = props => {
  const {themeIndication, getThemeObj, configPathSelector, value} = props;

  const isInheritedFromTheme =
    themeIndication && !value && getThemeObj && !!_.get(getThemeObj(configPathSelector), 'value');
  const {path: themePath, value: themeValue} = getThemeObj ? getThemeObj(configPathSelector) : {};

  if (isInheritedFromTheme) {
    return (
      <Tooltip
        visible={true}
        tooltipPosition={'Top'}
        tooltip={
          <View style={[styles.tooltip]}>
            <Text style={[commonStyles.baseText]}>
              Path: <Text style={styles.toolTipText}>{themePath}</Text>
            </Text>
            <Text style={[commonStyles.baseText]}>
              Value: <Text style={styles.toolTipText}>{themeValue}</Text>
            </Text>
          </View>
        }>
        <View style={styles.bubble} />
      </Tooltip>
    );
  }

  return <></>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'stretch',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    justifyContent: 'center',
    width: '100%',
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  splitRowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  bubble: {
    width: 20,
    height: 6,
    backgroundColor: '#FF674D',
    borderRadius: 3,
    cursor: 'pointer',
  },
  tooltip: {
    maxWidth: 250,
    flexDirection: 'column',
    gap: 6,
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
  toolTipText: {
    color: '#FF674D',
    borderWidth: 1,
    backgroundColor: '#FFFBDB',
    borderColor: '#FFEA47',
    padding: 3,
    borderRadius: 4,
    lineHeight: 24,
  },
  valueInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 4,
    color: theme.CONTROL_INPUT_COLOR,
    fontFamily: theme.FONT_FAMILY,
    outlineStyle: 'none',
  },
  bottomBar: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    marginTop: 8,
    borderRadius: 6,
    // overflow: 'hidden',
  },
  bottomBarBox: {
    position: 'relative',
    width: '20%',
    height: 30,
    // overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
    borderLeftWidth: 1,
    borderColor: theme.INPUT_BORDER,
  },
  inputGroup: {
    width: '90%',
    fontFamily: theme.FONT_FAMILY,
  },
  button: {
    flexDirection: 'row',
    width: 31,
    height: 31,
    marginLeft: 8,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonActive: {
    borderWidth: 2,
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  switchButton: {
    width: 12,
    height: 12,
    borderWidth: 2,
    borderRadius: 3,
  },
  switchButtonRight: {
    width: 12,
    height: 12,
    borderWidth: 2,
    borderRadius: 2,
    borderColor: theme.CONTROL_PLACEHOLDER_COLOR,
    borderRightColor: theme.PRIMARY_BORDER,
  },
});

export default TRBLControl;
