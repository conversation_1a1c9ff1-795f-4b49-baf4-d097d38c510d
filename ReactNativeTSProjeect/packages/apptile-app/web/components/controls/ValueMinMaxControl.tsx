import Immutable from 'immutable';
import _ from 'lodash';
import React, {useCallback, useState} from 'react';
import {StyleSheet, Text, TextInput, View} from 'react-native';

import theme from '@/root/web/styles-v2/theme';

type ValueMinMaxProps = {
  value: string;
  defaultValue: string;
  name: string;
  label: string;
  config: Immutable.Map<string, string>;
  options: [string, string, string, string];
  onChange: (value: boolean) => void;
  onCustomPropChange: (key: string, value: string, debounced: boolean, remove?: boolean) => void;
};

const ValueMinMaxControl: React.FC<ValueMinMaxProps> = props => {
  const {value, defaultValue, label, name, config, options, onChange, onCustomPropChange} = props;
  const [mainValue, setMainValue] = useState(value);
  const [minValue, setMinValue] = useState(config?.get(options[0]));
  const [maxValue, setMaxValue] = useState(config?.get(options[1]));
  const onMainValueChange = useCallback(
    newVal => {
      setMainValue(newVal);
      onCustomPropChange(name, newVal, true, _.isEmpty(_.trim(newVal)));
    },
    [name, onCustomPropChange],
  );

  const onEdgeValueChange = useCallback(
    (newVal, idx) => {
      switch (idx) {
        case 0:
          setMinValue(newVal);
          break;
        case 1:
          setMaxValue(newVal);
          break;
      }
      onCustomPropChange(options[idx], newVal, true, _.isEmpty(_.trim(newVal)));
    },
    [onCustomPropChange, options],
  );
  return (
    <View style={styles.container}>
      <View style={styles.splitRowContainer}>
        <View style={styles.rowContainer}>
          <Text style={[styles.labelText]}>{label}</Text>
          <TextInput
            style={[styles.valueInput, styles.inputGroup]}
            placeholder={defaultValue ?? 0}
            blurOnSubmit={true}
            value={mainValue}
            onChangeText={onMainValueChange}
          />
        </View>
        <View style={styles.rowContainer}>
          <Text style={styles.labelText}>min</Text>
          <TextInput
            style={[styles.valueInput, styles.inputGroup]}
            placeholder={defaultValue ?? 0}
            blurOnSubmit={true}
            onChangeText={text => onEdgeValueChange(text, 0)}
            value={minValue}
          />
        </View>
        <View style={styles.rowContainer}>
          <Text style={styles.labelText}>max</Text>
          <TextInput
            style={[styles.valueInput, styles.inputGroup]}
            placeholder={defaultValue ?? 0}
            blurOnSubmit={true}
            onChangeText={text => onEdgeValueChange(text, 1)}
            value={maxValue}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    margin: 4,
  },
  splitRowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  labelText: {
    fontFamily: theme.FONT_FAMILY,
    margin: 4,
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
  valueInput: {
    width: '33%',
    flex: 1,
    height: 32,
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    textAlign: 'center',
  },
  inputGroup: {
    borderWidth: 1,
    borderColor: '#ccc',
  },
  button: {
    flexDirection: 'row',
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  buttonActive: {
    backgroundColor: '#ccc',
  },
  switchButton: {
    width: 16,
    height: 16,
    margin: 4,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
  },
});

export default ValueMinMaxControl;
