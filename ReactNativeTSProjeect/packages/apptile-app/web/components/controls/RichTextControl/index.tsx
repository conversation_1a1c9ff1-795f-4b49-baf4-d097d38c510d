import React from 'react';

import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';
import PluginPropertyEditor from '@/root/web/views/propertyInspector/components/PluginPropertyEditor';
import ThemePropertyEditor from '@/root/web/views/propertyInspector/components/themePropertyEditor';

import {getEditorConfig} from './editorConfig';
import {RichTextControlProps} from '../../pluginEditorComponents';

const styleEditorConfig = getEditorConfig();

interface IRichTextControlProps extends Omit<RichTextControlProps, 'configProps'> {
  inTheme?: boolean;
}

const RichTextControl: React.FC<IRichTextControlProps> = props => {
  let {configPathSelector, inTheme, pluginId, entityConfig, pageId, config} = props;

  return (
    <>
      {Object.entries(styleEditorConfig).map((entry, index) => {
        const [key, value] = entry;

        return (
          <CollapsiblePanel title={key} isOpen={false} key={`${key}-${index}`}>
            {value.map((propEditor, idx) => {
              const selector = configPathSelector.slice(0, -1).concat([propEditor.name]);

              if (inTheme) {
                return (
                  <ThemePropertyEditor editor={propEditor} key={idx + propEditor.name} configPathSelector={selector} />
                );
              }

              return (
                <PluginPropertyEditor
                  key={pluginId + propEditor.name}
                  editor={propEditor}
                  entityConfig={entityConfig}
                  config={config}
                  pageId={pageId}
                  pluginId={pluginId}
                  configPathSelector={selector}
                />
              );
            })}
          </CollapsiblePanel>
        );
      })}
    </>
  );
};

export default RichTextControl;
