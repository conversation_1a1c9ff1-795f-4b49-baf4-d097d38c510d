import React, {useCallback, useState} from 'react';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import {View, Text, StyleSheet} from 'react-native';
import CheckboxControl from '../../CheckboxControl';
import CodeInputControl from '../../CodeInputControl';
import _ from 'lodash';
import NumericInputControl from '../../NumericInputControl';
import RangeSliderControl from '../../RangeSliderControl';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface CodeInputConfigEditorProps {
  configProps: PropertyEditorConfig<'codeInput', 'codeInput'>['props'];
  onConfigUpdate: (configProps: PropertyEditorConfig<'codeInput', 'codeInput'>['props']) => void;
}

const CodeInputConfigEditor: React.FC<CodeInputConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;
  const {singleLine, noOfLines} = configProps;
  const [singleLineVal, setSingleLineVal] = useState(singleLine || true);
  const [validationPattern, setValidationPattern] = useState(configProps?.validationPattern || '');
  const [validationError, setValidationError] = useState(
    configProps?.validationError || 'Validation Failed In Text Input',
  );
  const [numLineVal, setNumLineVal] = useState(noOfLines ?? 0);

  const validateRegex = (pattern: string) => {
    let regex = pattern,
      options = '';
    if (pattern.trim().startsWith('/')) {
      regex = pattern.slice(1, pattern.lastIndexOf('/'));
      options = pattern.slice(pattern.lastIndexOf('/') + 1);
    }
    try {
      new RegExp(regex, options);
      return true;
    } catch (e) {
      return false;
    }
  };
  const onSingleLineChange = useCallback(
    (val: boolean) => {
      setSingleLineVal(val);
      onConfigUpdate({...configProps, singleLine: val});
    },
    [configProps, onConfigUpdate],
  );
  const debouncedOnCustomPropChange = _.debounce((value: any) => onConfigUpdate(value), 250);
  const onValidationPatternChange = useCallback(
    (value: string) => {
      setValidationPattern(value);
      if (validateRegex(value)) {
        debouncedOnCustomPropChange({...configProps, validationPattern: value});
      }
    },
    [configProps, debouncedOnCustomPropChange],
  );
  const onValidationErrorChange = useCallback(
    (value: string) => {
      setValidationError(value);
      debouncedOnCustomPropChange({...configProps, validationError: value});
    },
    [configProps, debouncedOnCustomPropChange],
  );
  const onNumberOfLineChange = useCallback(
    (val: string) => {
      const newVal = parseInt(val);
      console.log(newVal, val);
      if (!isNaN(newVal)) {
        setNumLineVal(newVal);
        onConfigUpdate({...configProps, noOfLines: newVal});
      }
    },
    [configProps, onConfigUpdate],
  );

  return (
    <View style={styles.container}>
      <CheckboxControl label="Single Line Input" onChange={onSingleLineChange} value={singleLineVal} />
      <CodeInputControl
        placeholder={'^[\\S\\s]{1,10}$'}
        label={'Validation Pattern'}
        onChange={onValidationPatternChange}
        value={validationPattern}
      />
      {!validateRegex(validationPattern) && (
        <Text style={[commonStyles.baseText, commonStyles.errorText]}>Please Enter A Valid Regex</Text>
      )}
      <CodeInputControl label={'Validation Error'} onChange={onValidationErrorChange} value={validationError} />
      <RangeSliderControl
        minRange="0"
        maxRange="16"
        value={`${numLineVal}`}
        label={'Number Of Lines'}
        onChange={onNumberOfLineChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
  },
});
export default CodeInputConfigEditor;
