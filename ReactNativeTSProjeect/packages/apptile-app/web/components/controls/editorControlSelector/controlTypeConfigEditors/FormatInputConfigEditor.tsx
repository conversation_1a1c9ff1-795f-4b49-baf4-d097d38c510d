import React, {useCallback, useState} from 'react';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import {View, Text, StyleSheet} from 'react-native';
import CodeInputControl from '../../CodeInputControl';
import _ from 'lodash';
import CodeInputConfigEditor from './CodeInputConfigEditor';

export interface FormatInputConfigEditorProps {
  configProps: PropertyEditorConfig<'formatInput', 'formatInput'>['props'];
  onConfigUpdate: (configProps: PropertyEditorConfig<'formatInput', 'formatInput'>['props']) => void;
}

const FormatInputConfigEditor: React.FC<FormatInputConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;
  const [prefix, setPrefix] = useState(configProps?.prefix || '');
  const [suffix, setSuffix] = useState(configProps?.suffix || '');

  const debouncedOnCustomPropChange = _.debounce((value: any) => onConfigUpdate(value), 250);
  const onPrefixChange = useCallback(
    (value: string) => {
      setPrefix(value);
      debouncedOnCustomPropChange({...configProps, prefix: value});
    },
    [configProps, debouncedOnCustomPropChange],
  );
  const onSuffixChange = useCallback(
    (value: string) => {
      setSuffix(value);
      debouncedOnCustomPropChange({...configProps, suffix: value});
    },
    [configProps, debouncedOnCustomPropChange],
  );
  return (
    <View style={styles.container}>
      <CodeInputConfigEditor configProps={configProps} onConfigUpdate={onConfigUpdate} />
      <CodeInputControl label={'Prefix'} onChange={onPrefixChange} value={prefix} />
      <CodeInputControl label={'Suffix'} onChange={onSuffixChange} value={suffix} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
  },
});
export default FormatInputConfigEditor;
