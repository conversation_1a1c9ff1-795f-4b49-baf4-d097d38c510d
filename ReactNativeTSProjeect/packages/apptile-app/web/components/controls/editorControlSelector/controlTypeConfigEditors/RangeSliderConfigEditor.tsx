import React, {useCallback, useState} from 'react';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import {TextInput, View, Text, StyleSheet} from 'react-native';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface RangeSliderConfigEditorProps {
  configProps: PropertyEditorConfig<'rangeSliderInput', 'rangeSliderInput'>['props'];
  onConfigUpdate: (configProps: PropertyEditorConfig<'rangeSliderInput', 'rangeSliderInput'>['props']) => void;
}

const RangeSliderConfigEditor: React.FC<RangeSliderConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;
  const {minRange, maxRange, steps: decimalValues} = configProps;
  const [minVal, setMinVal] = useState(minRange);
  const [maxVal, setMaxVal] = useState(maxRange);
  const [steps, setSteps] = useState(decimalValues);

  const onMinChanged = useCallback(
    (val: string) => {
      setMinVal(val);
      onConfigUpdate({...configProps, minRange: val});
    },
    [configProps, onConfigUpdate],
  );
  const onMaxChanged = useCallback(
    (val: string) => {
      setMaxVal(val);
      onConfigUpdate({...configProps, maxRange: val});
    },
    [configProps, onConfigUpdate],
  );
  const onDecimalPlacesChanged = useCallback(
    (val: string) => {
      setSteps(val);
      onConfigUpdate({...configProps, steps: val});
    },
    [configProps, onConfigUpdate],
  );
  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText]}>Min</Text>
        <TextInput
          style={[styles.valueInput]}
          placeholder="0"
          blurOnSubmit={true}
          onChangeText={onMinChanged}
          value={minVal}
        />
      </View>
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText]}>Max</Text>
        <TextInput
          style={[styles.valueInput]}
          placeholder="0"
          blurOnSubmit={true}
          onChangeText={onMaxChanged}
          value={maxVal}
        />
      </View>
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText]}>Steps</Text>
        <TextInput
          style={[styles.valueInput]}
          placeholder="0"
          blurOnSubmit={true}
          onChangeText={onDecimalPlacesChanged}
          value={steps}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  valueInput: {
    width: 32,
    height: 32,
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    textAlign: 'center',
  },
});
export default RangeSliderConfigEditor;
