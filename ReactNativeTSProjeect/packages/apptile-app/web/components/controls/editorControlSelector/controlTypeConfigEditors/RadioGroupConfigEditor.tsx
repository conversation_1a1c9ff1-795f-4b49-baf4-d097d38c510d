import React, {useCallback} from 'react';
import {View} from 'react-native';

import {startCase} from 'lodash';

import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import JsonInputControl from '../../jsonEditor/JsonInputControl';
import CheckboxControl from '../../CheckboxControl';

export interface RadioGroupConfigEditorProps {
  configProps: PropertyEditorConfig<'radioGroup', 'radioGroup'>['props'] & {schema: {key: string; type: string}[]};
  onConfigUpdate: (configProps: PropertyEditorConfig<'radioGroup', 'radioGroup'>['props']) => void;
}

const RadioGroupConfigEditor: React.FC<RadioGroupConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;

  const onOptionsChanged = useCallback(
    (updates: string) => {
      onConfigUpdate({...configProps, options: JSON.parse(updates)});
    },
    [configProps, onConfigUpdate],
  );
  const onSelectChanged = useCallback(
    (value: boolean) => {
      onConfigUpdate({...configProps, allowDeselect: value});
    },
    [configProps, onConfigUpdate],
  );

  const {options, allowDeselect = false} = configProps;
  const data = (options || []).map(val => {
    if (typeof val === 'string') return {icon: null, text: startCase(val), value: val};
    return val;
  });

  return (
    <View>
      <CheckboxControl label="Allow Deselection?" value={allowDeselect} onChange={onSelectChanged} />
      <JsonInputControl onChange={onOptionsChanged} value={JSON.stringify(data)} schema={configProps.schema} />
    </View>
  );
};

export default RadioGroupConfigEditor;
