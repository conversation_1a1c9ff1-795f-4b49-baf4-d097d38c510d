import _ from 'lodash';
import React, {useCallback, useState} from 'react';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import JsonInputControl from '../../jsonEditor/JsonInputControl';
import { allAvailablePlans, allAvailablePlansOrdered } from 'apptile-core';

export interface DropdownConfigEditorProps {
  configProps: PropertyEditorConfig<'dropDown', 'dropDown'>['props'];
  onConfigUpdate: (configProps: PropertyEditorConfig<'dropDown', 'dropDown'>['props']) => void;
}

const DropdownConfigEditor: React.FC<DropdownConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;
  const {options} = configProps;

  const [dropdownOptions, setDropdownOptions] = useState(
    _.size(options) !== 0
      ? options
      : [
          {
            name: 'sampleKey',
            value: 'sampleValue',
            gating: allAvailablePlans.CORE,
          },
        ],
  );

  const onOptionsChanged = useCallback(
    (value: string) => {
      const jsValue = value ? JSON.parse(value) : {};

      setDropdownOptions(jsValue);

      onConfigUpdate({...configProps, options: jsValue});
    },
    [configProps, onConfigUpdate],
  );

  return (
    <JsonInputControl
      label={'dropDown'}
      value={JSON.stringify(dropdownOptions)}
      schema={[
        {key: 'name', type: 'codeInput'},
        {key: 'value', type: 'codeInput'},
        {key: 'gating', type: 'dropDown', props: {options: allAvailablePlansOrdered}},
      ]}
      onChange={onOptionsChanged}
    />
  );
};

export default DropdownConfigEditor;
