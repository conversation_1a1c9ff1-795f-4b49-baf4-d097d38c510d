import _ from 'lodash';
import React, {useCallback, useState} from 'react';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import {TextInput, View, Text, StyleSheet} from 'react-native';
import {SHOPIFY_PAGES_MAP} from '@/root/web/integrations/shopify/constants/ShopifyConstants';
import DropDownControl from '../../DropDownControl';
import CheckboxControl from '../../CheckboxControl';
import NumericInputControl from '../../NumericInputControl';

export interface ListEditorConfigEditorProps {
  configProps: PropertyEditorConfig<'listEditor', 'listEditor'>['props'];
  onConfigUpdate: (configProps: PropertyEditorConfig<'listEditor', 'listEditor'>['props']) => void;
}
const entityTypeOptions = [{key: 'default', value: 'default'}].concat(
  _.toPairs(SHOPIFY_PAGES_MAP).map(val => {
    return {key: val[1], value: val[0]};
  }),
);

const ListEditorConfigEditor: React.FC<ListEditorConfigEditorProps> = props => {
  const {configProps, onConfigUpdate} = props;
  const {shopifyType} = configProps;
  const [entityType, setEntityType] = useState(shopifyType ?? 'default');
  const [disableDelete, setDisableDelete] = useState<boolean>(configProps.disableAdd || false);

  const onValueChange = useCallback(
    val => {
      setEntityType(val);
      onConfigUpdate({...configProps, disableAdd: disableDelete, shopifyType: val === 'default' ? undefined : val});
    },
    [configProps, disableDelete, onConfigUpdate],
  );

  const onDisableChange = useCallback(
    (val: boolean) => {
      setDisableDelete(val);
      onConfigUpdate({...configProps, disableAdd: val, shopifyType: entityType === 'default' ? undefined : entityType});
    },
    [configProps, entityType, onConfigUpdate],
  );

  const onChange = useCallback(
    (name: string, val: any) => {
      setDisableDelete(val);
      onConfigUpdate({...configProps, shopifyType: entityType === 'default' ? undefined : entityType, [name]: val});
    },
    [configProps, entityType, onConfigUpdate],
  );

  return (
    <View style={styles.container}>
      <DropDownControl
        label="List Type"
        defaultValue={entityType}
        value={entityType}
        onChange={onValueChange}
        options={entityTypeOptions}
        valueKey="key"
        nameKey="value"
        disableBinding={true}
      />
      <CheckboxControl
        label="Disable Add/Delete"
        value={configProps?.disableAdd ?? false}
        onChange={(value: boolean) => onChange('disableAdd', value)}
      />
      <NumericInputControl
        label="Minimum Length"
        noUnit={true}
        value={configProps?.minLength ?? '1'}
        onChange={(value: string) =>
          onChange('minLength', isNaN(Number(value)) || Number(value) < 1 ? '1' : Math.floor(Number(value)).toString())
        }
      />
      <NumericInputControl
        label="Maximum Length"
        noUnit={true}
        value={configProps?.maxLength ?? '10000'}
        onChange={(value: string) =>
          onChange('maxLength', isNaN(Number(value)) || Number(value) < 1 ? '1' : Math.floor(Number(value)).toString())
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    gap: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  valueInput: {
    width: 32,
    height: 32,
    padding: 4,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    textAlign: 'center',
  },
});
export default ListEditorConfigEditor;
