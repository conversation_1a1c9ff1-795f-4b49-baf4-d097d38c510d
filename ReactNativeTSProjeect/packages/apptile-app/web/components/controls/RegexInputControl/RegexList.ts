import {v4 as uid} from 'uuid';

export const RegexList = [
  {
    id: uid(),
    label: 'isComplexPassword',
    expression: /(?=(.*[0-9]))(?=.*[\!@#$%^&*()\\[\]{}\-_+=~`|:;"'<>,./?])(?=.*[a-z])(?=(.*[A-Z]))(?=(.*)).{8,}/,
    description:
      'Should have 1 lowercase letter, 1 uppercase letter, 1 number, 1 special character and be at least 8 characters long',
  },
  {
    id: uid(),
    label: 'isModeratePassword',
    expression: /(?=(.*[0-9]))((?=.*[A-Za-z0-9])(?=.*[A-Z])(?=.*[a-z]))^.{8,}$/,
    description: 'Should have 1 lowercase letter, 1 uppercase letter, 1 number, and be at least 8 characters long',
  },
];
