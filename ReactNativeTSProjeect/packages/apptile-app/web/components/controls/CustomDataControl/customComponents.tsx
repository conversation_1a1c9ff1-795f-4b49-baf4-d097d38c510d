import {ChangeEventHandler, CSSProperties, FC, forwardRef, useCallback, useEffect, useRef, useState, memo} from 'react';
import AssetChooseDialog from '../assetEditor/assetChooseDialog';
import {MaterialCommunityIcons} from 'apptile-core';
import {GetDeviceImage} from '../../../../../apptile-core/plugins/widgets/widget';
import {ImageSchema, ImageValue} from './types';
import _ from 'lodash';
import DropDownControl from '../DropDownControl';
import {useSelector} from 'react-redux';
import {selectScreensInNav} from '@/root/web/selectors/EditorSelectors';
import {Image} from 'react-native';
import {IMAGE_URL_REGEX} from './utils';

export const Input = forwardRef<
  HTMLInputElement,
  {
    type: 'string' | 'number';
    defaultValue: any;
    onChange: ChangeEventHandler<HTMLInputElement>;
    style?: CSSProperties;
    placeholder?: string;
  }
>(({type, defaultValue, onChange, style = {}, placeholder}, ref) => {
  const defaultValueRef = useRef(defaultValue);
  const onChangeRef = useRef(onChange);

  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Debounced version of the change handler
  const debouncedHandleChange = useCallback(
    _.debounce(e => {
      const persistedEvent = {
        ...e,
        target: {
          ...e.target,
          value: e.target.value,
        },
      };
      onChangeRef.current(persistedEvent);
    }, 1000),
    [],
  );

  // Cleanup the debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedHandleChange.cancel();
    };
  }, [debouncedHandleChange]);

  return (
    <input
      ref={ref}
      type={type}
      placeholder={placeholder}
      defaultValue={defaultValueRef.current}
      onChange={debouncedHandleChange}
      className="customDataEditor_inputField"
      style={style}
    />
  );
});

const Placeholder: FC<{value: string}> = memo(
  ({value}) => (
    <div style={{position: 'relative'}}>
      <Image
        resizeMode="cover"
        source={value ? {uri: value} : require('../../../assets/images/placeholder-image.png')}
        style={{width: 24, height: 24, overflow: 'hidden', borderRadius: 2}}
      />
      <div style={{position: 'absolute', top: 0, right: 0, transform: 'translate(80%, -70%)'}}>
        <MaterialCommunityIcons name="pencil" size={14} color="#a3a3a3" />
      </div>
    </div>
  ),
  (prevProps, nextProps) => {
    return _.isEqual(prevProps.value, nextProps.value);
  },
);

export const ImageHandler: FC<{
  value: ImageValue;
  schema: ImageSchema;
  onChange: (value: ImageValue) => void;
}> = memo(
  ({value, schema, onChange}) => {
    const currentValueRef = useRef<ImageValue | null>(value);
    const [showDialog, setShowDialog] = useState(false);
    const [selectedAsset, setSelectedAsset] = useState('');
    const {imageRecord} = GetDeviceImage(selectedAsset);
    const [isValidUrl, setIsValidUrl] = useState(
      value.images?.length && !_.isEmpty(value.images.at(-1)) ? IMAGE_URL_REGEX.test(value.images.at(-1) || '') : true,
    );

    const onChangeUrl = useCallback(
      (url: string) => {
        const isValid = IMAGE_URL_REGEX.test(url);
        if (isValid) {
          currentValueRef.current = {value: url, images: [url]};
          setIsValidUrl(true);
        } else {
          setIsValidUrl(false);
        }
        onChange(currentValueRef.current!);
      },
      [onChange],
    );

    useEffect(() => {
      currentValueRef.current = value;
    }, [value]);

    useEffect(() => {
      if (imageRecord && onChange) {
        const imageData = imageRecord.toJS();
        const images = Object.values(imageData.variants)
          .map((variant: any) => variant.fileUrl)
          .sort((a, b) => {
            const resolutionPattern = /original-(\d+)x\d+\.png$/;
            return parseInt(a.match(resolutionPattern)[1], 10) - parseInt(b.match(resolutionPattern)[1], 10);
          });
        images.push(imageData.fileUrl);
        if (!_.isEqual(images, currentValueRef.current)) {
          currentValueRef.current = {value: images.at(-1), images};
          onChange(currentValueRef.current);
        }
      }
    }, [imageRecord, onChange]);

    const containerStyles = {
      border: 0,
      outline: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      height: '100%',
      cursor: 'pointer',
    };

    return (
      <>
        {schema.allowUrl ? (
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: '100%'}}>
            <Input
              type="string"
              defaultValue={value?.value.length > 0 ? value.images.at(-1) : ''}
              onChange={e => onChangeUrl(e.target.value)}
              style={{
                padding: 10,
                border: isValidUrl ? '0' : '1px solid red',
              }}
            />
            <div
              style={{
                ...containerStyles,
                borderRadius: 5,
                height: 35,
                width: 35,
                marginLeft: 5,
              }}
              onClick={() => setShowDialog(true)}>
              <Placeholder value={value.value} />
            </div>
          </div>
        ) : (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              justifyContent: 'center',
              height: '100%',
              width: '100%',
            }}>
            <div
              style={{
                ...containerStyles,
                borderRadius: 5,
                padding: 5,
              }}
              onClick={() => setShowDialog(true)}>
              <Placeholder value={value.value} />
            </div>
          </div>
        )}
        <AssetChooseDialog
          askURL={false}
          currentAssetId={'selectedAsset'}
          onSelectAsset={setSelectedAsset}
          onCloseDialog={setShowDialog}
          showDialog={showDialog}
        />
      </>
    );
  },
  (prevProps, nextProps) => {
    return _.isEqual(prevProps.value, nextProps.value);
  },
);

export const ScreenInputHandler: FC<{value: any; onChange: (value: any) => void}> = memo(
  ({value, onChange}) => {
    const screens = useSelector(selectScreensInNav);

    return (
      <DropDownControl
        label=""
        defaultValue={value}
        value={value}
        options={screens}
        valueKey="name"
        nameKey="name"
        onChange={onChange}
      />
    );
  },
  (prevProps, nextProps) => {
    return _.isEqual(prevProps.value, nextProps.value);
  },
);

export const EnumInputHandler: FC<{value: any; options: string[]; onChange: (value: any) => void}> = ({
  value,
  options,
  onChange,
}) => {
  return <DropDownControl label="" defaultValue={value} value={value} options={options} onChange={onChange} />;
};
