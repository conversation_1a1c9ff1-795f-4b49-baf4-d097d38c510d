export enum PrimitiveType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
}

export enum AdvancedType {
  OBJECT = 'object',
  ARRAY = 'array',
}

export enum ApptileType {
  COLLECTION = 'collection',
  PRODUCT = 'product',
  IMAGE = 'image',
  SCREEN = 'screen',
}

export type BaseSchemaOperation = 'update';

export type ArraySchemaOperation = 'add' | 'remove';

export type SchemaOperation = BaseSchemaOperation | ArraySchemaOperation;

export type SchemaType = PrimitiveType | AdvancedType | ApptileType;

export type SchemaTypeWithNull<T> = T | [T, 'null'];

export type BaseSchema<T extends SchemaType> = {
  title?: string;
  description?: string;
  type: SchemaTypeWithNull<T>;
  default?: any;
  enum?: any[] | Record<string, any>;
};

export type StringSchema = BaseSchema<PrimitiveType.STRING> & {
  format?: string;
  minLength?: number;
  maxLength?: number;
  enum?: string[] | Record<string, string>;
};

export type NumberSchema = BaseSchema<PrimitiveType.NUMBER> & {
  minimum?: number;
  maximum?: number;
  enum?: number[] | Record<string, number>;
};

export type BooleanSchema = BaseSchema<PrimitiveType.BOOLEAN> & {
  default?: boolean;
  enum?: Record<string, boolean>;
};

export type RegexSchema = BaseSchema<PrimitiveType.STRING> & {
  pattern: string;
};

export type EnumSchema = BaseSchema<PrimitiveType.STRING> & {
  enum: any[];
};

export type ObjectSchema = BaseSchema<AdvancedType.OBJECT> & {
  properties: Record<string, Schema>;
  required?: string[];
};

export type ArraySchema = BaseSchema<AdvancedType.ARRAY> & {
  items: Schema;
  minItems?: number;
  maxItems?: number;
  enum: undefined;
};

export type ShopifyItemSchema<T extends ApptileType> = BaseSchema<T> & {
  dataFormat: 'handle' | 'all';
};

export type ShopifyProductSchema = ShopifyItemSchema<ApptileType.PRODUCT>;

export type ShopifyCollectionSchema = ShopifyItemSchema<ApptileType.COLLECTION>;

export type ScreenSchema = BaseSchema<ApptileType.SCREEN>;

export type ImageSchema = BaseSchema<ApptileType.IMAGE> & {
  allowUrl?: boolean;
};

export type Schema =
  | StringSchema
  | NumberSchema
  | BooleanSchema
  | ObjectSchema
  | ArraySchema
  | ImageSchema
  | EnumSchema
  | RegexSchema
  | ShopifyProductSchema
  | ShopifyCollectionSchema
  | ScreenSchema;

export type CustomDataControlProps = {
  configProps: {
    label: string;
    schema: Schema;
  };
  value: any;
  name: string;
  defaultValue: unknown;
  onChange: Function;
  onUpdateRawValue: Function;
};

export type CustomDataControlModalProps = {
  visible: boolean;
  closeModal: () => void;
  data: any;
  schema: Schema;
  currentSchema: Schema;
  currentPath: string[];
  onNavigate: (path: string) => void;
  onUpdateValue: (path: string, schema: Schema, operation: SchemaOperation, newValue?: any) => void;
};

export type BreadcrumbsProps = {
  schema: Schema;
  onNavigate: (path: string) => void;
  currentPath: string[];
};

export type BaseViewProps = {
  onNavigate?: (path: string) => void;
  onUpdateValue?: (path: string, schema: Schema, operation: SchemaOperation, newValue?: any) => void;
  path?: string[];
};

export type DataViewProps = BaseViewProps & {
  data: any;
  schema: Schema;
};

export type ObjectViewProps = DataViewProps & {
  schema: ObjectSchema;
};

export type ArrayViewProps = DataViewProps & {
  schema: ArraySchema;
};

export type CellProps = BaseViewProps & {
  value: any;
  schema: Schema;
};

export type ImageValue = {
  value: string;
  images: string[];
};
