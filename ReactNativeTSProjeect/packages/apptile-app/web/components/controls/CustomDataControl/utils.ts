import {
  Schema,
  SchemaType,
  ObjectSchema,
  ArraySchema,
  PrimitiveType,
  ImageSchema,
  ShopifyProductSchema,
  ShopifyCollectionSchema,
  ApptileType,
  AdvancedType,
  SchemaTypeWithNull,
} from './types';
import _ from 'lodash';

export const isOfType = (schemaType: SchemaTypeWithNull<SchemaType>, expectedType: SchemaType): boolean => {
  return (
    _.isEqual(schemaType, expectedType) || (Array.isArray(schemaType) && _.isEqual(schemaType, [expectedType, 'null']))
  );
};

export const getType = (schemaType: SchemaTypeWithNull<SchemaType>): SchemaTypeWithNull<SchemaType> => {
  return Array.isArray(schemaType) ? schemaType.filter(t => t !== 'null')[0] : schemaType;
};

export const createDataStructure = (schema: Schema, path: string[] = []): any => {
  const getPath = (): string => path.join('.') || 'root';

  if ('defaultValue' in schema) {
    if (!validateValue(schema, schema.defaultValue, path)) {
      throw new Error(
        `Invalid defaultValue at ${getPath()}: ${JSON.stringify(schema.defaultValue)} for schema ${JSON.stringify(
          schema,
        )}`,
      );
    }

    const clonedValue = _.cloneDeep(schema.defaultValue);

    if (isOfType(schema.type, AdvancedType.ARRAY) && Array.isArray(clonedValue)) {
      const arraySchema = schema as ArraySchema;
      if (arraySchema.minItems !== undefined && clonedValue.length < arraySchema.minItems) {
        const neededItems = arraySchema.minItems - clonedValue.length;
        for (let i = 0; i < neededItems; i++) {
          clonedValue.push(createDataStructure(arraySchema.items));
        }
      }
    }

    return clonedValue;
  }

  if (Array.isArray(schema.type) && schema.type.includes('null')) {
    return null;
  }

  switch (true) {
    case isOfType(schema.type, PrimitiveType.STRING):
      return schema.enum ? (Array.isArray(schema.enum) ? schema.enum[0] : Object.values(schema.enum)[0]) : '';
    case isOfType(schema.type, PrimitiveType.NUMBER):
      return 0;
    case isOfType(schema.type, PrimitiveType.BOOLEAN):
      return false;
    case isOfType(schema.type, ApptileType.COLLECTION):
    case isOfType(schema.type, ApptileType.PRODUCT):
      return (schema as ShopifyProductSchema | ShopifyCollectionSchema).dataFormat === 'all' ? {} : '';
    case isOfType(schema.type, ApptileType.IMAGE):
      return {
        value: '',
        images: [],
      };
    case isOfType(schema.type, ApptileType.SCREEN):
      return '';
    case isOfType(schema.type, AdvancedType.OBJECT):
      return createObjectStructure(schema as ObjectSchema, path);
    case isOfType(schema.type, AdvancedType.ARRAY): {
      const arraySchema = schema as ArraySchema;
      const arr: any[] = [];

      if (arraySchema.minItems !== undefined) {
        for (let i = 0; i < arraySchema.minItems; i++) {
          arr.push(createDataStructure(arraySchema.items));
        }
      }

      return arr;
    }
    default:
      if (schema.type) throw new Error(`Unknown type at ${getPath()}: ${schema.type}`);
      else throw new Error(`Unknown type`);
  }
};

export const validateValue = (schema: Schema, value: any, path: string[] = []): boolean => {
  if (value === null) {
    return Array.isArray(schema.type) && schema.type.includes('null');
  }

  if (schema.enum) {
    let isValid = false;
    if (Array.isArray(schema.enum)) {
      isValid = schema.enum.some(item => item === value);
      if (!isValid) {
        console.error(`Invalid enum value at ${path.join('.')}: ${value}. Allowed: ${schema.enum.join(', ')}`);
      }
    } else {
      const enumValues = Object.values(schema.enum);
      isValid = enumValues.some(item => item === value);
      if (!isValid) {
        console.error(`Invalid enum value at ${path.join('.')}: ${value}. Allowed: ${enumValues.join(', ')}`);
      }
    }
    return isValid;
  }

  switch (true) {
    case isOfType(schema.type, PrimitiveType.STRING):
      return validateType(PrimitiveType.STRING, value, path);
    case isOfType(schema.type, PrimitiveType.NUMBER):
      return validateType(PrimitiveType.NUMBER, value, path);
    case isOfType(schema.type, PrimitiveType.BOOLEAN):
      return validateType(PrimitiveType.BOOLEAN, value, path);
    case isOfType(schema.type, ApptileType.COLLECTION):
    case isOfType(schema.type, ApptileType.PRODUCT):
      return validateShopifyItem(schema as ShopifyProductSchema | ShopifyCollectionSchema, value, path);
    case isOfType(schema.type, ApptileType.IMAGE):
      return validateImage(schema as ImageSchema, value, path);
    case isOfType(schema.type, ApptileType.SCREEN):
      return validateType(PrimitiveType.STRING, value, path);
    case isOfType(schema.type, AdvancedType.OBJECT):
      return validateObject(schema as ObjectSchema, value, path);
    case isOfType(schema.type, AdvancedType.ARRAY):
      return validateArray(schema as ArraySchema, value, path);
    default:
      return false;
  }
};

export const validateShopifyItem = (
  schema: ShopifyProductSchema | ShopifyCollectionSchema,
  value: any,
  path: string[],
): boolean => {
  if (value === null) return Array.isArray(schema.type) && schema.type.includes('null');

  if (schema.dataFormat === 'all') {
    return _.isPlainObject(value);
  }

  return validateType(PrimitiveType.STRING, value, path);
};

export const validateType = (type: PrimitiveType, value: any, path: string[]): boolean => {
  const isValid = typeof value === type;
  if (!isValid) {
    console.error(`Type mismatch at ${path.join('.')}: Expected ${type}, got ${typeof value}`);
  }
  return isValid;
};

export const validateObject = (schema: ObjectSchema, value: any, path: string[]): boolean => {
  if (typeof value !== 'object' || value === null) {
    console.error(`Type mismatch at ${path.join('.')}: Expected object, got ${typeof value}`);
    return false;
  }

  return Object.entries((schema as ObjectSchema).properties).every(([key, fieldSchema]) => {
    const newPath = [...path, key];
    if (!(key in value)) {
      console.error(`Missing required field at ${newPath.join('.')}`);
      return false;
    }
    return validateValue(fieldSchema, value[key], newPath);
  });
};

export const validateArray = (schema: ArraySchema, value: any, path: string[]): boolean => {
  if (!Array.isArray(value)) {
    console.error(`Type mismatch at ${path.join('.')}: Expected array, got ${typeof value}`);
    return false;
  }

  return value.every((item, index) => {
    const newPath = [...path, `[${index}]`];
    return validateValue(schema, item, newPath);
  });
};

export const validateImage = (schema: ImageSchema, value: any, path: string[]): boolean => {
  if (typeof value !== 'object' && value !== null) {
    console.error(`Type mismatch at ${path.join('.')}: Expected object, got ${typeof value}`);
    return false;
  }

  return _.isPlainObject(value) && typeof value.value === 'string' && Array.isArray(value.images);
};

export const createObjectStructure = (schema: ObjectSchema, path: string[]): Record<string, any> => {
  const obj: Record<string, any> = {};
  for (const [key, fieldSchema] of Object.entries(schema.properties)) {
    obj[key] = createDataStructure(fieldSchema, [...path, key]);
  }
  return obj;
};

export const getSchemaAtPath = (schema: Schema, pathParts: string[]): Schema | null => {
  let parts = pathParts.filter(Boolean);
  let currentSchema: Schema = schema;

  for (const part of parts) {
    if (isOfType(currentSchema.type, AdvancedType.OBJECT)) {
      const objectSchema = currentSchema as ObjectSchema;
      if (objectSchema.properties[part]) {
        currentSchema = objectSchema.properties[part];
      } else {
        return null;
      }
    } else if (isOfType(currentSchema.type, AdvancedType.ARRAY)) {
      currentSchema = (currentSchema as ArraySchema).items;
    } else {
      return null;
    }
  }

  return currentSchema;
};

export const validateValueAtPath = (data: any, path: string, schema: Schema): boolean => {
  const pathParts = _.toPath(path);
  const targetSchema = getSchemaAtPath(schema, pathParts);

  if (!targetSchema) return false;

  const value = _.get(data, path);
  return validateValue(targetSchema, value, pathParts);
};

export const setValue = <T extends object>(data: T, path: string, newValue: any, schema: Schema): T => {
  if (!schema) {
    throw new Error(`Invalid path: ${path}`);
  }

  if (!validateValue(schema, newValue, _.toPath(path))) {
    throw new Error(`Invalid value for schema at path ${path}`);
  }

  return _.setWith(_.clone(data), path, newValue, _.clone);
};

export const addArrayItem = <T extends object>(data: T, path: string, schema: Schema): T => {
  if (!schema || !isOfType(schema.type, AdvancedType.ARRAY)) {
    throw new Error(`Path ${path} is not an array`);
  }
  const arraySchema = schema as ArraySchema;
  const currentArray = (path ? _.get(data, path, []) : data) as any[];

  if (arraySchema.maxItems !== undefined && currentArray?.length >= arraySchema.maxItems) {
    throw new Error(`Array at ${path} exceeds maxItems (${arraySchema.maxItems})`);
  }

  const newItem = createDataStructure(arraySchema.items);

  if (!path) {
    return [...currentArray, newItem] as unknown as T;
  }

  return _.setWith(_.clone(data), path, [...currentArray, newItem], _.clone);
};

export const removeArrayItem = <T extends object>(data: T, path: string, index: number, schema: Schema): T => {
  if (!schema || !isOfType(schema.type, AdvancedType.ARRAY)) {
    throw new Error(`Path ${path} is not an array`);
  }

  const arraySchema = schema as ArraySchema;
  const currentArray = (path ? _.get(data, path, []) : data) as any[];

  if (index < 0 || index >= currentArray.length) {
    throw new Error(`Invalid index ${index} for array at ${path}`);
  }

  if (arraySchema.minItems !== undefined && currentArray.length - 1 < arraySchema.minItems) {
    throw new Error(`Array at ${path} would fall below minItems (${arraySchema.minItems})`);
  }

  const newArray = currentArray.filter((_, i) => i !== index);

  if (!path) {
    return newArray as unknown as T;
  }

  return _.setWith(_.clone(data), path, newArray, _.clone);
};

export const getArrayConstraints = (schema: Schema): {min?: number; max?: number} => {
  if (isOfType(schema.type, AdvancedType.ARRAY)) {
    const arraySchema = schema as ArraySchema;
    return {
      min: arraySchema.minItems,
      max: arraySchema.maxItems,
    };
  }
  return {};
};

export const resolveTitle = (schema: Schema, path: string[]): string | undefined => {
  // First check for explicit titleRef
  // if ('titleRef' in schema && typeof schema.titleRef === 'string') {
  //   const targetField = schema.titleRef;
  //   // Validate target field exists
  //   if (!getSchemaAtPath(schema, [targetField])) {
  //     console.warn(`titleRef '${targetField}' not found in schema at path ${path.join('.') || 'root'}`);
  //     return undefined;
  //   }
  //   // Validate target field is a string
  //   const targetSchema = getSchemaAtPath(schema, [targetField]);
  //   if (!targetSchema || targetSchema.type !== 'string') {
  //     console.warn(`titleRef '${targetField}' is not a string at path ${path.join('.') || 'root'}`);
  //     return undefined;
  //   }
  //   const targetSchemaTitle = targetSchema.title;
  //   if (targetSchemaTitle) return targetSchemaTitle;
  //   return _.startCase(targetField);
  // }

  // Then check for direct title
  if (schema.title) return schema.title;

  // For array items, check parent schema
  if (path.length > 0) {
    const parentPath = path.slice(0, -1);
    const parentSchema = getSchemaAtPath(schema, parentPath);
    if (parentSchema) return resolveTitle(parentSchema, parentPath);
  }

  // return _.startCase(schema.type);
  return undefined;
};

/**
 * Valid:
- http://example.com/image.jpg 
- https://sub.example.com/path/to/image.png 
- http://example.com:8080/image.jpeg?param=1 
- https://example.com/image.JPG  (case-insensitive)
- http://example.com/%61%62%63/image.jpg  (percent-encoded path)
Invalid:
- http://example.com/image.jpgx 
- http://example.com 
- http://example.com?image.jpg 
- http://example.com#image.jpg 
- http://example.com/path/image.jpg/ 
- http://example.com//image.jpg  (double slash, but valid path)
- http://example.com/image.jpg?query=param#section  (query and fragment)
This seems to handle most cases, including percent-encoded characters, case insensitivity for the extension, query parameters, and fragments.
 */
export const IMAGE_URL_REGEX =
  /^https?:\/\/(?:[a-zA-Z0-9-]+\.)+[a-zA-Z0-9-]+(?::\d+)?(?:\/(?:[\w\-._~!$&'()*+,;=:@]|%[0-9a-fA-F]{2})*)*\/?(?:[\w\-._~!$&'()*+,;=:@%]|%[0-9a-fA-F]{2})+\.(png|jpe?g)(?:\?(?:[\w\-._~!$&'()*+,;=:@%/?]|%[0-9a-fA-F]{2})*)?(?:#(?:[\w\-._~!$&'()*+,;=:@%/?]|%[0-9a-fA-F]{2})*)?$/i;

export const extractValueFromBinding = <T>(str: string): T | null => {
  const match = str.match(/^\{\{(.*)\}\}$/s); // Match content inside {{ }}
  if (match) {
    try {
      return JSON.parse(match[1]) as T; // Parse the JSON content
    } catch (e) {
      console.error('Invalid JSON:', e);
    }
  }
  return null; // Return null if the format is incorrect
};
