import {type FC, useCallback, useEffect, useState, useMemo} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import CustomDataControlModal from './CustomDataControlModal';
import {
  AdvancedType,
  ApptileType,
  PrimitiveType,
  type ArraySchema,
  type CustomDataControlProps,
  type ObjectSchema,
  type Schema,
  type SchemaOperation,
} from './types';
import theme from '@/root/web/styles-v2/theme';
import {createDataStructure, setValue, addArrayItem, removeArrayItem, isOfType} from './utils';
import Button from '@/root/web/components-v2/base/Button';
import _ from 'lodash';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


const CustomDataControl: FC<CustomDataControlProps> = ({
  configProps,
  value,
  name,
  defaultValue,
  onChange,
  onUpdateRawValue,
}) => {
  const {label, schema} = configProps;
  const [currentState, setCurrentState] = useState(value);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentPath, setCurrentPath] = useState<string[]>([]);

  const persistToAppConfig = useCallback(() => {
    onUpdateRawValue?.(currentState);
  }, [currentState, onChange]);

  const getCurrentData = useCallback(() => {
    return currentPath.reduce((acc, segment) => {
      if (typeof acc === 'object' && acc !== null) {
        return acc[segment];
      }
      return acc;
    }, currentState);
  }, [currentState, currentPath]);

  const getCurrentSchema = useCallback(() => {
    return currentPath.reduce((acc: Schema, segment) => {
      if (isOfType(acc.type, AdvancedType.OBJECT) && (acc as ObjectSchema).properties[segment]) {
        return (acc as ObjectSchema).properties[segment];
      } else if (isOfType(acc.type, AdvancedType.ARRAY)) {
        return (acc as ArraySchema).items;
      }
      return acc;
    }, schema);
  }, [schema, currentPath]);

  const handleNavigate = useCallback((path: string) => {
    setCurrentPath(path ? _.toPath(path).filter(Boolean) : []);
  }, []);

  const handleUpdateValue = useCallback(
    (path: string, schemaToUpdate: Schema, operation: SchemaOperation, newValue?: any) => {
      let updatedData = null;
      switch (true) {
        case isOfType(schemaToUpdate.type, PrimitiveType.STRING):
        case isOfType(schemaToUpdate.type, PrimitiveType.NUMBER):
        case isOfType(schemaToUpdate.type, PrimitiveType.BOOLEAN):
        case isOfType(schemaToUpdate.type, ApptileType.COLLECTION):
        case isOfType(schemaToUpdate.type, ApptileType.PRODUCT):
        case isOfType(schemaToUpdate.type, ApptileType.IMAGE):
        case isOfType(schemaToUpdate.type, ApptileType.SCREEN): {
          updatedData = setValue(_.cloneDeep(currentState), path, newValue, schemaToUpdate);
          setCurrentState(updatedData);
          break;
        }
        case isOfType(schemaToUpdate.type, AdvancedType.ARRAY): {
          switch (operation) {
            case 'add': {
              updatedData = addArrayItem(_.cloneDeep(currentState), path, schemaToUpdate);
              setCurrentState(updatedData);
              break;
            }
            case 'remove': {
              updatedData = removeArrayItem(_.cloneDeep(currentState), path, newValue, schemaToUpdate);
              setCurrentState(updatedData);
              break;
            }
            default:
              break;
          }
          break;
        }
        default:
          break;
      }
    },
    [currentState],
  );

  useEffect(() => {
    if (value) {
      setCurrentState(value);
      return;
    }

    if (!value) {
      setCurrentState(createDataStructure(schema));
      return;
    }

    if (defaultValue) {
      setCurrentState(defaultValue);
      return;
    }
  }, [value]);

  // Memoize the button click handler
  const openModal = useCallback(() => setIsModalVisible(true), []);
  const closeModal = useCallback(() => setIsModalVisible(false), []);

  // Memoize the current data and schema to avoid recalculation in render
  const currentData = useMemo(() => getCurrentData(), [getCurrentData]);
  const currentSchemaValue = useMemo(() => getCurrentSchema(), [getCurrentSchema]);

  return (
    <>
      <View style={styles.container}>
        <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label || 'Custom Data'}</Text>
        <Button variant="FILLED-PILL" containerStyles={{width: '50%'}} onPress={openModal}>
          Edit
        </Button>
      </View>
      <CustomDataControlModal
        visible={isModalVisible}
        closeModal={() => {
          persistToAppConfig();
          closeModal();
        }}
        data={currentData}
        schema={schema}
        currentSchema={currentSchemaValue}
        currentPath={currentPath}
        onNavigate={handleNavigate}
        onUpdateValue={handleUpdateValue}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.PRIMARY_MARGIN,
    flexDirection: 'row',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});

export default CustomDataControl;
