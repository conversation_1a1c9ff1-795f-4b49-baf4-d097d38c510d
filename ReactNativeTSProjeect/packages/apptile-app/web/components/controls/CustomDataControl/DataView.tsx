import {useCallback, FC, useState, useMemo} from 'react';
import {
  AdvancedType,
  ArraySchema,
  ArrayViewProps,
  CellProps,
  DataViewProps,
  ImageSchema,
  ObjectSchema,
  ObjectViewProps,
  ShopifyProductSchema,
  ShopifyCollectionSchema,
  PrimitiveType,
  ApptileType,
} from './types';
import _ from 'lodash';
import Button from '@/root/web/components-v2/base/Button';
import CheckboxControl from '../CheckboxControl';
import ShopifyEntityKeyControl from '../ShopifyEntityKeyControl';
import {EnumInputHandler, ImageHandler, Input, ScreenInputHandler} from './customComponents';
import {MaterialCommunityIcons} from 'apptile-core';
import {extractValueFromBinding, getType, isOfType} from './utils';

const Cell: FC<CellProps> = ({value, schema, onNavigate, onUpdateValue, path = []}) => {
  if (value === undefined) {
    return <span>-</span>;
  }

  const onChange = useCallback(
    (newValue: any) => {
      onUpdateValue?.(path.join('.'), schema, 'update', newValue);
    },
    [path, schema, onUpdateValue],
  );

  if (schema.enum && !_.isEmpty(schema.enum)) {
    return (
      <EnumInputHandler
        value={value}
        onChange={newValue =>
          onChange(
            Array.isArray(schema.enum) ? newValue : Object.entries(schema.enum!).find(([key]) => key === newValue)?.[0],
          )
        }
        options={Array.isArray(schema.enum) ? schema.enum : Object.values(schema.enum)}
      />
    );
  }

  switch (true) {
    case isOfType(schema.type, PrimitiveType.STRING): {
      return (
        <Input
          type={getType(schema.type) as PrimitiveType.STRING}
          defaultValue={value || ''}
          onChange={e => onChange(e.target.value)}
        />
      );
    }
    case isOfType(schema.type, PrimitiveType.NUMBER): {
      return (
        <Input
          type={getType(schema.type) as PrimitiveType.NUMBER}
          defaultValue={value || ''}
          onChange={e => {
            if (!_.isNaN(e.target.value)) onChange(Number(e.target.value));
          }}
        />
      );
    }
    case isOfType(schema.type, PrimitiveType.BOOLEAN): {
      return (
        <div style={{display: 'flex', width: '100%', justifyContent: 'center'}}>
          <div style={{display: 'inline'}}>
            <CheckboxControl value={value} onChange={newValue => onChange(Boolean(newValue) || false)} />
          </div>
        </div>
      );
    }
    case isOfType(schema.type, ApptileType.PRODUCT):
    case isOfType(schema.type, ApptileType.COLLECTION): {
      return (
        <ShopifyEntityKeyControl
          value={value}
          onChange={newValue => {
            onChange(
              (schema as ShopifyProductSchema | ShopifyCollectionSchema).dataFormat === 'all'
                ? extractValueFromBinding(newValue)
                : newValue,
            );
          }}
          type={(schema as ShopifyProductSchema | ShopifyCollectionSchema).dataFormat}
          itemType={getType(schema.type) as ApptileType.PRODUCT | ApptileType.COLLECTION}
          label=""
          name=""
        />
      );
    }
    case isOfType(schema.type, ApptileType.IMAGE): {
      return <ImageHandler schema={schema as ImageSchema} value={value} onChange={onChange} />;
    }
    case isOfType(schema.type, ApptileType.SCREEN): {
      return <ScreenInputHandler value={value} onChange={onChange} />;
    }
    case isOfType(schema.type, AdvancedType.OBJECT): {
      return (
        <span onClick={() => onNavigate?.('')} className="customDataEditor_link">
          {'{...}'}
        </span>
      );
    }
    case isOfType(schema.type, AdvancedType.ARRAY): {
      return (
        <span onClick={() => onNavigate?.('')} className="customDataEditor_link">
          {`[${Array.isArray(value) ? value.length : 0} items]`}
        </span>
      );
    }
    default: {
      return <span>{JSON.stringify(value)}</span>;
    }
  }
};

const ObjectView: FC<ObjectViewProps> = ({data, schema, onNavigate, onUpdateValue, path = []}) => {
  if (!isOfType(schema.type, AdvancedType.OBJECT) || typeof data !== 'object' || data === null) {
    return <div>Invalid object data or schema</div>;
  }

  const fields = Object.keys(schema.properties || {});

  if (fields.length === 0) {
    return <div>No fields defined in schema</div>;
  }

  // Memoize fields to prevent unnecessary recalculation
  const memoizedFields = useMemo(() => fields, [fields]);

  // Memoize the header row rendering
  const headerRow = useMemo(
    () => (
      <div className="customDataEditor_header-row">
        {memoizedFields.map(field => (
          <div key={field} className="customDataEditor_cell">
            <span className="customDataEditor_header-text">{schema?.title || _.startCase(field)}</span>
          </div>
        ))}
      </div>
    ),
    [memoizedFields, schema?.title],
  );

  // Create stable callback references
  const getNavigateCallback = useCallback(
    (field: string) => (newPath: string) => {
      onNavigate?.([field, ...(newPath ? _.toPath(newPath) : [])].join('.'));
    },
    [onNavigate],
  );

  return (
    <>
      {schema.title ? <h3 style={{marginTop: 0}}>{schema.title}</h3> : <></>}
      <div className="customDataEditor_scroll-view">
        <div className="customDataEditor_table">
          {headerRow}
          <div className="customDataEditor_row">
            {memoizedFields.map(field => {
              const cellValue = data[field];
              const cellSchema = schema.properties[field];
              const cellPath = [...path, field];

              return (
                <div key={field} className="customDataEditor_cell">
                  <Cell
                    value={cellValue}
                    schema={cellSchema}
                    onNavigate={getNavigateCallback(field)}
                    onUpdateValue={onUpdateValue}
                    path={cellPath}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};

const APPTILE_DEFAULT_VALUE_FIELD = 'apptile_default_value';

const ArrayView: FC<ArrayViewProps> = ({data, schema, onNavigate, onUpdateValue, path = []}) => {
  const [showSlNo, setShowSlNo] = useState(false);
  if (!isOfType(schema.type, AdvancedType.ARRAY) || !Array.isArray(data)) {
    return <div>Invalid array data or schema</div>;
  }

  const itemSchema = schema.items;
  const fields = isOfType(itemSchema.type, AdvancedType.OBJECT)
    ? Object.keys((itemSchema as ObjectSchema).properties || {})
    : [APPTILE_DEFAULT_VALUE_FIELD];

  if (fields.length === 0) {
    return <div>No fields defined in array item schema</div>;
  }

  // Memoize these callbacks to prevent unnecessary recreation
  const handleAddEntry = useCallback(() => {
    if (!onUpdateValue || !data) return;
    const fullPath = path?.join('.') || '';
    onUpdateValue(fullPath, schema, 'add');
  }, [onUpdateValue, data, path, schema]);

  const handleRemoveEntry = useCallback(
    (index: number) => {
      if (!onUpdateValue || !data) return;
      const fullPath = path?.join('.') || '';
      onUpdateValue(fullPath, schema, 'remove', index);
    },
    [onUpdateValue, data, path, schema],
  );

  // Memoize fields to prevent unnecessary recalculation
  const memoizedFields = useMemo(() => fields, [fields]);

  // Memoize the header row rendering
  const headerRow = useMemo(
    () => (
      <div className="customDataEditor_header-row">
        {showSlNo && (
          <div className="customDataEditor_cell" style={{width: '50px'}}>
            <span className="customDataEditor_header-text">Sl. No.</span>
          </div>
        )}
        {memoizedFields.map(field => (
          <div key={field} className="customDataEditor_cell">
            <span className="customDataEditor_header-text">
              {(schema.items as ObjectSchema).properties?.[field]?.title || _.startCase(field)}
            </span>
          </div>
        ))}
      </div>
    ),
    [memoizedFields, schema.items, showSlNo],
  );

  // Create a stable callback for Cell navigation
  const getCellNavigateCallback = useCallback(
    (index: number) => (path: string) => onNavigate?.(path ? `${path}` : index.toString()),
    [onNavigate],
  );

  // Memoize the array rows to prevent unnecessary re-rendering
  const arrayRows = useMemo(() => {
    return data.map((item, index) => {
      const canRemove = schema.minItems === undefined || data.length > schema.minItems;

      return (
        <div key={`${JSON.stringify(item)}-${index}`} className="customDataEditor_row">
          {showSlNo && (
            <div className="customDataEditor_cell" style={{width: '50px'}}>
              <span>{index + 1}</span>
            </div>
          )}
          {memoizedFields.map(field => {
            const cellValue = isOfType(itemSchema.type, AdvancedType.OBJECT) ? item[field] : item;
            const cellSchema = isOfType(itemSchema.type, AdvancedType.OBJECT)
              ? (itemSchema as ObjectSchema).properties?.[field]
              : itemSchema;
            const cellPath =
              field === APPTILE_DEFAULT_VALUE_FIELD ? [...path, index.toString()] : [...path, index.toString(), field];

            return (
              <div key={field} className="customDataEditor_cell">
                <Cell
                  value={cellValue}
                  schema={cellSchema}
                  onNavigate={getCellNavigateCallback(index)}
                  onUpdateValue={onUpdateValue}
                  path={cellPath}
                />
              </div>
            );
          })}
          {canRemove && (
            <div className="customDataEditor_deleteButtonContainer">
              <button onClick={() => handleRemoveEntry(index)} className="customDataEditor_deleteButton">
                <MaterialCommunityIcons name="delete" color="red" size={20} />
              </button>
            </div>
          )}
        </div>
      );
    });
  }, [data, memoizedFields, itemSchema, showSlNo, path, getCellNavigateCallback, onUpdateValue, handleRemoveEntry]);

  // Memoize the add entry button section
  const addEntrySection = useMemo(() => {
    const canAdd = onUpdateValue && (schema.maxItems === undefined || data.length < schema.maxItems);
    if (!canAdd) return null;

    return (
      <div style={{display: 'flex', gap: 16, alignItems: 'center', justifyContent: 'space-between'}}>
        <div style={{width: 200}}>
          <CheckboxControl label="Sl. No." value={showSlNo} onChange={setShowSlNo} />
        </div>
        <Button onPress={handleAddEntry} containerStyles={{margin: 8, width: 200}}>
          Add Entry
        </Button>
      </div>
    );
  }, [onUpdateValue, schema.maxItems, data.length, showSlNo, handleAddEntry]);

  return (
    <div>
      {schema.title ? <h3 style={{marginTop: 0}}>{schema.title}</h3> : <></>}
      <div className="customDataEditor_scroll-view">
        <div className="customDataEditor_table">
          {headerRow}
          {arrayRows}
        </div>
      </div>
      {addEntrySection}
    </div>
  );
};

export const DataView: FC<DataViewProps> = ({data, schema, onNavigate, onUpdateValue, path}) => {
  if (!schema) {
    return <div>No schema provided</div>;
  }

  switch (true) {
    case isOfType(schema.type, AdvancedType.OBJECT):
      return (
        <ObjectView
          data={data}
          schema={schema as ObjectSchema}
          onNavigate={onNavigate}
          onUpdateValue={onUpdateValue}
          path={path}
        />
      );
    case isOfType(schema.type, AdvancedType.ARRAY):
      return (
        <ArrayView
          data={data}
          schema={schema as ArraySchema}
          onNavigate={onNavigate}
          onUpdateValue={onUpdateValue}
          path={path}
        />
      );
    default:
      return (
        <div className="customDataEditor_cell">
          <Cell value={data} schema={schema} onNavigate={onNavigate} onUpdateValue={onUpdateValue} path={path} />
        </div>
      );
  }
};

const styles = `
.customDataEditor_table {
  display: table;
  width: 100%;
  border-collapse: collapse;
  box-sizing: border-box;
}

.customDataEditor_row {
  display: table-row;
  box-sizing: border-box;
}

.customDataEditor_cell {
  min-width: 150px;
  vertical-align: middle;
  display: table-cell;
  padding: 10px;
  border: 1px solid #ddd;
  text-align: center;
  box-sizing: border-box;
}

.customDataEditor_header .customDataEditor_cell {
  min-width: 150px;
  text-align: center;
  font-weight: bold;
  background-color: #f5f5f5;
}

// .customDataEditor_row:not(.customDataEditor_header):hover .customDataEditor_cell {
//   background-color: #f0f0f0;
// }

.customDataEditor_scroll-view {
  overflow-x: auto;
  width: 100%;
}

.customDataEditor_header-row {
  display: table-row;
  background-color: #f5f5f5;
}

.customDataEditor_header-text {
  font-weight: bold;
  text-align: center;
  display: block;
}

.customDataEditor_link {
  color: #0066cc;
  text-decoration: underline;
  cursor: pointer;
}

.customDataEditor_deleteButtonContainer {
  vertical-align: middle;
  display: table-cell;
  padding: 10px;
  max-width: 50px;
}

.customDataEditor_deleteButton {
  cursor: pointer;
  border: none;
  background-color: transparent;
  outline: none;
  padding: 10px;
  border-radius: 50%;
}

.customDataEditor_deleteButton:hover {
  background-color: #f5f5f5;
}

.customDataEditor_inputField {
  border: 0;
  outline: 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 5px;
  flex: 1;
  flex-grow: 1;
  width: 80%;
  height: 100%;
}

.customDataEditor_inputField::-webkit-outer-spin-button,
.customDataEditor_inputField::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
`;

document.head.insertAdjacentHTML('beforeend', `<style>${styles}</style>`);
