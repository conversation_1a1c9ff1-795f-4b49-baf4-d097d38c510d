import {type FC, Fragment, useMemo, useCallback} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import Modal from '@/root/web/components-v2/base/Modal';
import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import {DataView} from './DataView';
import {BreadcrumbsProps, CustomDataControlModalProps, SchemaOperation} from './types';
import {getSchemaAtPath} from './utils';
import _ from 'lodash';

const Breadcrumbs: FC<BreadcrumbsProps> = ({schema, onNavigate, currentPath}) => {
  // Create a stable callback reference for root navigation
  const navigateToRoot = useCallback(() => onNavigate(''), [onNavigate]);

  // Memoize breadcrumb items to prevent unnecessary rerenders
  const breadcrumbItems = useMemo(() => {
    return currentPath.map((segment, index) => {
      const currentSchema = getSchemaAtPath(schema!, currentPath.slice(0, index))!;
      const displayText = _.isNaN(Number(segment)) ? currentSchema.title || _.startCase(segment) : Number(segment) + 1;

      // Create a stable callback for each segment
      const handleNavigate = () => onNavigate(currentPath.slice(0, index + 1).join('.'));

      return (
        <Fragment key={index}>
          <Text style={styles.breadcrumbSeparator}>/</Text>
          <Text style={styles.breadcrumbItem} onPress={handleNavigate}>
            {displayText}
          </Text>
        </Fragment>
      );
    });
  }, [schema, onNavigate, currentPath]);

  return (
    <View style={styles.breadcrumbs}>
      <Text style={styles.breadcrumbItem} onPress={navigateToRoot}>
        {schema.title || 'Root'}
      </Text>
      {breadcrumbItems}
    </View>
  );
};

const DataControlModal: FC<CustomDataControlModalProps> = props => {
  const {visible, closeModal, data, schema, currentSchema, currentPath, onNavigate, onUpdateValue} = props;

  // Memoize the wrapper navigation function to prevent rerenders
  const handleNavigate = useCallback(
    (path: string) => onNavigate(currentPath.join('.') + '.' + path),
    [onNavigate, currentPath],
  );

  // Memoize the update value function
  const handleUpdateValue = useCallback(
    (path: string, schema: any, operation: SchemaOperation, value: any) =>
      onUpdateValue(path, schema, operation, value),
    [onUpdateValue],
  );

  // Memoize the content to prevent unnecessary re-renders
  const modalContent = useMemo(
    () => (
      <View style={[theme.modal, styles.dialogStyle]}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Custom Data Editor</Text>
          <Pressable onPress={closeModal}>
            <MaterialCommunityIcons name="close" size={24} />
          </Pressable>
        </View>
        <Breadcrumbs onNavigate={onNavigate} currentPath={currentPath} schema={schema} />
        <div style={{flex: 1, marginTop: 30, overflowY: 'auto'}}>
          <DataView
            path={currentPath}
            data={data}
            schema={currentSchema}
            onNavigate={handleNavigate}
            onUpdateValue={handleUpdateValue}
          />
        </div>
      </View>
    ),
    [closeModal, onNavigate, currentPath, schema, data, currentSchema, handleNavigate, handleUpdateValue],
  );

  return <Modal disableOutsideClick visible={visible} content={modalContent} />;
};

const styles = StyleSheet.create({
  dialogStyle: {
    height: '90vh',
    width: '90vw',
    maxHeight: 1200,
    maxWidth: 1800,
    backgroundColor: '#fff',
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 28,
  },
  content: {
    flex: 1,
    marginTop: 30,
  },
  breadcrumbs: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    flexWrap: 'wrap',
  },
  breadcrumbItem: {
    color: '#0066cc',
    fontSize: 14,
    marginRight: 4,
  },
  breadcrumbSeparator: {
    marginHorizontal: 4,
    color: '#666',
  },
});

export default DataControlModal;
