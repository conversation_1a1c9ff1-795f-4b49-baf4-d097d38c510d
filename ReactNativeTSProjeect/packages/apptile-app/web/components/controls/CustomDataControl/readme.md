# Schema Documentation

## Schema Definition

```javascript
/**
 * @typedef {Object} Schema
 * @property {string} type - The type of data (required). Supported types: 'string', 'number', 'boolean', 'object', 'array'
 * @property {Array} [enum] - Array of allowed values (for enum types)
 * @property {boolean} [nullable=false] - Whether the value can be null
 * @property {*} [defaultValue] - Default value (must match type/enum rules)
 * @property {Object.<string, Schema>} [fields] - Required for object types
 * @property {Schema} [items] - Required for array types (schema for array items)
 */
```

## Schema Definition Guide

A recursive schema system for defining data structures with validation. Supports complex nested structures with type safety and default values.

### Basic Structure
```javascript
const schema = {
  type: 'object',
  fields: {
    key: { 
      type: '<TYPE>',
      // ...additional properties
    }
  }
};
```

### Type Specific Rules

#### 1. Primitive Types
- **string**: `{ type: 'string' }`
- **number**: `{ type: 'number' }`
- **boolean**: `{ type: 'boolean' }`

#### 2. Enum Type
```javascript
{
  type: 'string', // or 'number'
  enum: ['val1', 'val2'],
  defaultValue: 'val1' // optional, must be in enum
}
```

#### 3. Object Type
```javascript
{
  type: 'object',
  fields: {
    nestedField: { /* nested schema */ }
  },
  nullable: true // optional
}
```

#### 4. Array Type
```javascript
{
  type: 'array',
  items: { /* schema for array items */ },
  defaultValue: [] // optional, must be array
}
```

#### 5. Nullable Fields
```javascript
{
  type: 'string',
  nullable: true // default becomes null
}
```

### Validation Rules
- **Default values** MUST match the defined type/enum
- **Nullable fields** cannot have default values
- **Enum definitions** require at least one value
- **Object schemas** MUST contain `fields` property
- **Array schemas** MUST contain `items` property
- **Circular references** are not supported

## Examples

### Simple Schema
```javascript
{
  type: 'object',
  fields: {
    name: { type: 'string' },
    age: { type: 'number', nullable: true },
    active: { type: 'boolean', defaultValue: true }
  }
}
```

### Complex Nested Schema
```javascript
{
  type: 'object',
  fields: {
    products: {
      type: 'array',
      items: {
        type: 'object',
        fields: {
          id: { type: 'string' },
          price: { type: 'number' },
          tags: {
            type: 'array',
            items: { type: 'string', enum: ['sale', 'new'] }
          }
        }
      }
    }
  }
}
```

### Full-Featured Example
```javascript
{
  type: 'object',
  fields: {
    status: {
      type: 'string',
      enum: ['draft', 'published'],
      defaultValue: 'draft'
    },
    metadata: {
      type: 'object',
      nullable: true,
      fields: {
        author: { type: 'string' },
        revision: { type: 'number' }
      }
    },
    variants: {
      type: 'array',
      items: {
        type: 'object',
        fields: {
          sku: { type: 'string' },
          stock: { 
            type: 'number',
            nullable: true,
            defaultValue: null
          }
        }
      }
    }
  }
}
```

## Extended Schema Types

### 1. Image Type
```ts
{
  type: 'image',
}
```

### 2. Collection Type
```ts
{
  type: 'collection',
}
```

### 3. Product Type
```ts
{
  type: 'product',
}
```

## Extended Type Examples

**Image Upload Example**
```ts
{
  type: 'object',
  fields: {
    heroImage: {
      type: 'image',
    },
    gallery: {
      type: 'array',
      items: {
        type: 'image',
      }
    }
  }
}
```

## Usage
```javascript
const data = createDataStructure(schema); // Returns initialized data structure
```

## Error Handling
**@throws** {Error} On invalid schema configuration including:
- Type mismatch in defaultValue
- Missing required properties (fields/items)
- Invalid enum values
- Circular references

**@note** The system supports infinite nesting through recursive schemas

**@warning** Avoid circular references in object/array definitions

# Custom Data Control Component

A dynamic nested data structure editor with schema validation and modal-based editing interface.

## Features
- Schema-driven data validation
- Nested object/array editing
- Type-safe operations
- Modal-based editing interface
- Breadcrumb navigation
- Default value support