import Immutable from 'immutable';
import React from 'react';
import {QuadPicker, QuadPickerProps} from './themeEditor/components';

interface QuadInputControlProps {
  value: string;
  name: string;
  label: string;
  defaultValue?: string;
  placeholder?: string;
  options: [string, string, string, string];
  onChange: (value: any) => void;
  onCustomPropChange: (key: string, value: any) => void;
  config: Immutable.Map<string, any>;
  layout?: 'plus' | 'square';
}

const QuadInputControl: React.FC<QuadInputControlProps> = ({
  value,
  label,
  placeholder,
  name,
  options,
  defaultValue,
  config,
  onChange,
  onCustomPropChange,
  layout,
}) => {
  const onValueChange = React.useCallback(
    newVal => {
      onChange(newVal);
    },
    [onChange],
  );
  const onCustomValueChange = React.useCallback(
    (key, newVal) => {
      onCustomPropChange(key, newVal === '' ? null : newVal);
    },
    [onCustomPropChange],
  );

  const constructQuadValues = () => {
    const QuadValues = [];

    // Master field
    QuadValues.push({
      name,
      value,
    });

    // subFields
    options.forEach(option => {
      QuadValues.push({
        name: option,
        value: config.get(option),
      });
    });

    return QuadValues as QuadPickerProps['value'];
  };

  const updateHandler = (quadName: string, quadValue: number | string) => {
    if (quadName === name) {
      onValueChange(quadValue);
    } else {
      onCustomValueChange(quadName, quadValue);
    }
  };

  return (
    <QuadPicker
      label={label}
      value={constructQuadValues()}
      placeholder={placeholder}
      layout={layout}
      defaultValue={defaultValue}
      onChange={updateHandler}
    />
  );
};

export default QuadInputControl;
