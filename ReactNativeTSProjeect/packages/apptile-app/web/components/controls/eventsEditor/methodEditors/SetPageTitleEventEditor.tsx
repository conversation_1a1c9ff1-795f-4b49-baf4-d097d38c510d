import {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {EventHandlerConfig} from 'apptile-core';
import {View, Text, StyleSheet} from 'react-native';
import CodeInput from '../../../codeEditor/codeInput';

export interface SetPageTitleEventEditorProps {
  event: EventHandlerConfig;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const SetPageTitleEventEditor: React.FC<SetPageTitleEventEditorProps> = props => {
  const {event, onConfigUpdate} = props;

  const onPropValueChangeHandler = useCallback(
    (value, key) => {
      const newState = event.set(key, value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  const debouncedOnPropValueChangeHandler = debounce((value, key) => onPropValueChangeHandler(value, key), 450);
  return (
    <View style={styles.rowLayout}>
      <Text style={styles.labelText}>Property Value</Text>
      <View style={styles.CodeInputStyle}>
        <CodeInput
          placeholder="{{textInput1.value}}"
          value={event.value}
          onChange={(value: string) =>
            debouncedOnPropValueChangeHandler(value, 'value')
          }
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
});

export default SetPageTitleEventEditor;
