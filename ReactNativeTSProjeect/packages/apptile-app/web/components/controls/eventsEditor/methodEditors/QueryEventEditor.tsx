import _ from 'lodash';
import React, {useCallback} from 'react';
import {EventHandlerConfig} from 'apptile-core';
import {PluginWithGlobalAvailability} from '../../../../selectors/PluginSelectors';
import DropDownControl from '../../DropDownControl';
import Immutable from 'immutable';
import ParamsInspector from '../ParamsInspector';
import CheckboxControl from '../../CheckboxControl';
import {View} from 'react-native';


export interface QueryEventEditorProps {
  event: EventHandlerConfig;
  queries: PluginWithGlobalAvailability;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const QueryEventEditor: React.FC<QueryEventEditorProps> = props => {
  const {event, queries, onConfigUpdate} = props;

  const onPluginChangeHandler = useCallback(
    pluginId => {
      const pluginconf = _.find(queries, plugin => plugin.pluginId === pluginId);
      const newState = event.set('pluginId', pluginId).set('isGlobalPlugin', pluginconf ? pluginconf.isGlobal : true);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, queries],
  );
  const onParamsChange = useCallback(
    (params: Immutable.Map<string, string>) => {
      const newState = event.set('params', params);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  const onisGlobalChangeHandler = useCallback(
    value => {
      const newState = event.set('isGlobalPlugin', value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  return (
    <>
      <DropDownControl
        label="Query"
        defaultValue=""
        value={event.pluginId}
        onChange={onPluginChangeHandler}
        options={queries}
        valueKey="pluginId"
      />
      <View style={{flexDirection: 'row'}}>
        <CheckboxControl
          label="Is Global Plugin"
          value={event?.get('isGlobalPlugin') ?? false}
          onChange={(value: boolean) => onisGlobalChangeHandler(value)}
        />
      </View>
      <ParamsInspector
        {...{
          params: event?.params ?? Immutable.Map(),
          onParamsChange: onParamsChange,
        }}
      />
    </>
  );
};

export default QueryEventEditor;
