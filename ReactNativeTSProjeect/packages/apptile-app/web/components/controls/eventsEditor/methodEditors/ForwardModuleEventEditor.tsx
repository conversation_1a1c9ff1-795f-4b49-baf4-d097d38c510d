import _, {} from 'lodash';
import React, {useCallback} from 'react';
import {StyleSheet} from 'react-native';
import {EventHandlerConfig} from 'apptile-core';
import DropDownControl from '../../DropDownControl';
import Immutable from 'immutable';
import ParamsInspector from '../ParamsInspector';

export interface ForwardModuleEventEditorProps {
  event: EventHandlerConfig;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
  moduleEvents: string[];
}

const ForwardModuleEventEditor: React.FC<ForwardModuleEventEditorProps> = function (props) {
  const {event, onConfigUpdate, moduleEvents} = props;
  const onEventChangeHandler = useCallback(
    eventName => {
      const newState = event.setIn(['value'], eventName);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  const onParamsChange = useCallback(
    (params: Immutable.Map<string, string>) => {
      const newState = event.set('params', params);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  return (
    <>
      <DropDownControl
        label="Event"
        defaultValue=""
        value={event.get('value')}
        onChange={onEventChangeHandler}
        options={moduleEvents}
      />
      <ParamsInspector
        {...{
          params: event?.params ?? Immutable.Map(),
          onParamsChange: onParamsChange,
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  inputStyle: {
    flex: 1,
    flexGrow: 4,
    padding: 8,
    fontSize: 12,
    fontFamily: 'monospace',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
});

export default ForwardModuleEventEditor;
