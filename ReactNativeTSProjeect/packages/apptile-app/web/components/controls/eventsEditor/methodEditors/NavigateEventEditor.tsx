import _ from 'lodash';
import React, {useCallback} from 'react';
import {EventHandlerConfig, PageConfig, ScreenConfigParams} from 'apptile-core';
import DropDownControl from '../../DropDownControl';
import ParamsInspector from '../ParamsInspector';
import Immutable from 'immutable';
import {useSelector} from 'react-redux';
import {pageConfigsSelector} from 'apptile-core';

export interface QueryEventEditorProps {
  event: EventHandlerConfig;
  screens: ScreenConfigParams[];
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const QueryEventEditor: React.FC<QueryEventEditorProps> = props => {
  const {event, screens, onConfigUpdate} = props;
  const pageConfigs = useSelector(pageConfigsSelector);

  const onScreenChangeHandler = useCallback(
    screenName => {
      const screenConfig = _.find(screens, screen => screen.name === screenName);
      const pageId = screenConfig?.screen;
      const pageConfig: PageConfig = pageConfigs.get(pageId);
      const pageParams = pageConfig?.pageParams;
      const routeParams = pageParams?.map((pageParam, name) => {
        return pageParam.defaultValue ?? '';
      });

      const newState = event
        .set('screenName', screenName)
        .set('params', event.params.merge(Immutable.Map(routeParams)));
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate, pageConfigs, screens],
  );

  const onParamsChange = useCallback(
    (params: Immutable.Map<string, string>) => {
      const newState = event.set('params', params);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  return (
    <>
      <DropDownControl
        label="Screen"
        value={event.screenName}
        onChange={onScreenChangeHandler}
        options={screens}
        valueKey="name"
        nameKey="name"
      />
      <ParamsInspector
        {...{
          params: event?.params ?? Immutable.Map(),
          onParamsChange: onParamsChange,
        }}
      />
    </>
  );
};

export default QueryEventEditor;
