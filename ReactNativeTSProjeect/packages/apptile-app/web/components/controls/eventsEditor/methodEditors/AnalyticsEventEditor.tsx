import _, {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {EventHandlerConfig} from 'apptile-core';
import ParamsInspector from '../ParamsInspector';
import Immutable from 'immutable';
import CodeInput from '../../../codeEditor/codeInput';

export interface AnalyticsEventEditorProps {
  event: EventHandlerConfig;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const AnalyticsEventEditor: React.FC<AnalyticsEventEditorProps> = props => {
  const {event, onConfigUpdate} = props;

  const onPropValueChangeHandler = useCallback(
    (value, key) => {
      const newState = event.set(key, value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );
  const debouncedOnPropValueChangeHandler = debounce((value, key) => onPropValueChangeHandler(value, key), 450);

  const onParamsChange = useCallback(
    (params: Immutable.Map<string, string>) => {
      const newState = event.set('params', params);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  return (
    <>
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Event name</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder=""
            value={event.value}
            onChange={(value: string) =>
              debouncedOnPropValueChangeHandler(value, 'value')
            }
          />
        </View>
      </View>
      <ParamsInspector
        {...{
          params: event?.params ?? Immutable.Map(),
          onParamsChange: onParamsChange,
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
  inputStyle: {
    flex: 1,
    flexGrow: 4,
    padding: 8,
    fontSize: 12,
    fontFamily: 'monospace',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
});

export default AnalyticsEventEditor;
