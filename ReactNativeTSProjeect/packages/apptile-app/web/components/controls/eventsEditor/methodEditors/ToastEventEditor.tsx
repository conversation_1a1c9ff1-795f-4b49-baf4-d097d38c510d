import {debounce} from 'lodash';
import React, {useCallback} from 'react';
import {EventHandlerConfig} from 'apptile-core';
import {View, Text, StyleSheet} from 'react-native';
import CodeInput from '../../../codeEditor/codeInput';
import DropDownControl from '../../DropDownControl';

export interface ToastEventEditor {
  event: EventHandlerConfig;
  onConfigUpdate: (eventConfig: EventHandlerConfig) => void;
}

const ToastEventEditor: React.FC<ToastEventEditor> = props => {
  const {event, onConfigUpdate} = props;
  const toastTypes = ['success', 'error', 'warning', 'info'];
  const placement = ['top', 'bottom'];

  const onPropValueChangeHandler = useCallback(
    (value, key) => {
      const newState = event.set(key, value);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  const onParamsChange = useCallback(
    (params: Immutable.Map<string, string>) => {
      const newState = event.set('params', params);
      onConfigUpdate(newState);
    },
    [event, onConfigUpdate],
  );

  const debouncedOnPropValueChangeHandler = debounce((value, key) => onPropValueChangeHandler(value, key), 450);

  return (
    <>
      <DropDownControl
        label="Type"
        onChange={value => {
          onParamsChange(event.params.set('type', value));
        }}
        value={event.params.get('type', '')}
        options={toastTypes}
      />
      <DropDownControl
        label="Placement"
        onChange={value => {
          onParamsChange(event.params.set('placement', value));
        }}
        value={event.params.get('placement', '')}
        options={placement}
      />
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Message</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder="{{textInput1.value}}"
            value={event.value}
            onChange={(value: string) =>
              debouncedOnPropValueChangeHandler(value, 'value')
            }
          />
        </View>
      </View>
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Duration</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder="1000"
            value={event.params.get('duration', '')}
            onChange={(value: string) =>
              onParamsChange(event.params.set('duration', value))
            }
          />
        </View>
      </View>
      <View style={styles.rowLayout}>
        <Text style={styles.labelText}>Offset</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            placeholder="0"
            value={event.params.get('offset', '')}
            onChange={(value: string) =>
              onParamsChange(event.params.set('offset', value))
            }
          />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
});

export default ToastEventEditor;
