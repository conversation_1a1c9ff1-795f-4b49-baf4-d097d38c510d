import {EventType} from 'apptile-core';

type ActionOption = {
  label: string;
  pluginType?: string;
  method: string | undefined;
  type: EventType;
  newSection?: boolean;
};

export const actionOptions: ActionOption[] = [
  {
    label: 'Trigger query',
    method: 'executeQuery',
    type: 'query',
  },
  {
    label: 'Get next page for query',
    method: 'getNextPage',
    type: 'query',
  },
  {
    label: 'Set Component Value',
    method: 'setValue',
    type: 'widget',
  },
  {
    label: 'Navigate to Screen',
    method: 'navigate',
    type: 'page',
  },
  {
    label: 'Navigate Back',
    method: 'navigateBack',
    type: 'page',
  },
  {
    label: 'Navigate to Screen as Root',
    method: 'navigateReset',
    type: 'page',
  },
  {
    label: 'Set Current Page Title',
    method: 'setPageTitle',
    type: 'page',
  },
  {
    label: 'Make Toast',
    method: 'triggerToast',
    type: 'query',
  },
  {
    label: 'Trigger Action',
    method: 'triggerAction',
    type: 'action',
  },
  {
    label: 'Publish Page Analytics',
    method: 'sendPageAnalytics',
    type: 'page',
  },
  {
    label: 'Publish Tracking Analytics',
    method: 'sendTrackAnalytics',
    type: 'action',
  },
];

export const moduleEventOption = {
  label: 'Forward to Module',
  method: 'forwardModuleEvent',
  type: 'action',
};
