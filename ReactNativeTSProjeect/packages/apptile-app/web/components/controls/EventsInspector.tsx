import Immutable from "immutable";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Pressable, StyleSheet, Text, Touchable, View } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { EventHandlerConfig, ScreenConfigParams } from "apptile-core";
import {
	EditorProps,
	PluginEditor,
	PropertyEditorProps,
} from "../../../app/common/EditorControlTypes";
import { MaterialCommunityIcons } from "apptile-core";
import {
	selectModuleDefaultEvents,
	selectParentModuleEvents,
	selectScreensInNav,
} from "../../selectors/EditorSelectors";
import {
	PluginWithGlobalAvailability,
	selectPageAndGlobalPluginByType,
} from "../../selectors/PluginSelectors";
import CollapsiblePanel from "../CollapsiblePanel";
import { actionOptions } from "./eventsEditor/actionOptions";
import EventEditor from "./eventsEditor/EventInspector";
import theme from "@/root/web/styles-v2/theme";

import { selectExposedEvent } from "@/root/web/selectors/EditorSelectors";
import { updateModuleProperty } from "../../actions/editorActions";
import DropDownControl from "./DropDownControl";
import CodeInputControl from "./CodeInputControl";
import useLocalStorage from "@/root/web/common/hooks/useLocalStorage";

export interface EditorEventItemProps {
	onEventUpdate: (propName: number, event: EventHandlerConfig) => void;
	onEventDelete: (propName: number) => void;
	reorderEventUpward: (eventId: number) => void;
	reorderEventDownward: (eventId: number) => void;
	eventId: number;
	event: EventHandlerConfig;
	children?: React.ReactNode;
	triggers: string[];
	plugins: PluginWithGlobalAvailability;
	queries: PluginWithGlobalAvailability;
	screens: ScreenConfigParams[];
	pageId: string;
	configPathSelector: string[];
	moduleUUID: string;
	selected?: boolean;
	onEventSelect: (eventId: number) => void;
}

const EditorEventItem: React.FC<EditorEventItemProps> = (props) => {
	const {
		selected,
		eventId,
		event,
		pageId,
		configPathSelector,
		isModule,
		moduleUUID,
		onEventUpdate,
		onEventDelete,
		onEventSelect,
		reorderEventUpward,
		reorderEventDownward,
	} = props;
	const dispatch = useDispatch();
	const exposedModule =
		useSelector<any>((state) =>
			selectExposedEvent(state, moduleUUID, eventId),
		) ?? null;

	const [active, setActive] = useState(false);

	const { label, pluginId, type, method, screenName, params } = event;

	const closePopup = () => {
		setActive(false);
	};

	const onDeleteHandler = () => {
		onEventDelete(eventId);
	};

	let defaultEventHandlers = isModule
		? useSelector((state) => selectModuleDefaultEvents(state, moduleUUID))
		: Immutable.List();

	const onModulePropChange = () => {
		defaultEventHandlers = defaultEventHandlers.set(
			eventId,
			defaultEventHandlers
				.get(eventId)
				?.set(
					"isExposed",
					!defaultEventHandlers.get(eventId)?.get("isExposed"),
				),
		);
		dispatch(
			updateModuleProperty(
				moduleUUID,
				"defaultEventHandlers",
				defaultEventHandlers,
			),
		);
	};

	const onModulePropValueChange = (key, value) => {
		defaultEventHandlers = defaultEventHandlers.set(
			eventId,
			defaultEventHandlers.get(eventId)?.set(key, value),
		);
		dispatch(
			updateModuleProperty(
				moduleUUID,
				"defaultEventHandlers",
				defaultEventHandlers,
			),
		);
	};

	const nameChange = _.debounce((value) => {
		onModulePropValueChange("name", value);
	}, 400);

	if (!event) return null;
	const displayMethod = () => {
		switch (type) {
			case "page":
				return (
					<Text style={styles.eventListingMethod}>
						{method}("{screenName}")
					</Text>
				);
			case "action":
				return (
					<Text style={styles.eventListingMethod}>
						{method} {"=>\n"}
						{event.pluginId + "." + event.get("value")}
					</Text>
				);
			case "native-action":
				return <Text style={styles.eventListingMethod}>{method}</Text>;
			case "query":
			case "widget":
				return (
					<Text style={styles.eventListingMethod}>
						{pluginId + `.${method}`}
					</Text>
				);
		}
	};

	return (
		<View style={styles.eventListItem}>
			<View style={[styles.eventListingLayout]}>
				<View style={styles.eventItemBox}>
					{isModule && (
						<View>
							<Pressable
								style={{ padding: 4 }}
								onPress={() => onModulePropChange()}
							>
								<MaterialCommunityIcons
									color={exposedModule?.get("isExposed") ? "green" : "amber"}
									name={
										exposedModule?.get("isExposed")
											? "alpha-e-circle"
											: "alpha-e-circle-outline"
									}
									size={16}
								/>
							</Pressable>
						</View>
					)}
					<View style={styles.iconContainer}>
						<Pressable
							pointerEvents="auto"
							onPress={() => onEventSelect(eventId)}
							style={{ marginRight: 4 }}
						>
							<MaterialCommunityIcons
								name={selected ? "checkbox-marked" : "checkbox-blank-outline"}
								size={20}
								color={"black"}
							/>
						</Pressable>

						<MaterialCommunityIcons
							name="arrow-up"
							color={"#333"}
							size={14}
							onPress={() => reorderEventUpward(eventId)}
							style={styles.upIcon}
						/>

						<MaterialCommunityIcons
							name="arrow-down"
							color={"#333"}
							size={14}
							onPress={() => reorderEventDownward(eventId)}
							style={styles.downIcom}
						/>
					</View>

					<Pressable onPress={() => setActive(!active)}>
						<View style={[styles.eventListingBox]}>
							<Text style={[styles.eventListingLabel]}>{label}</Text>
							<View style={styles.eventListingDisplayMethod}>
								{displayMethod()}
							</View>
						</View>
					</Pressable>
				</View>
				<Pressable style={styles.deleteButton} onPress={onDeleteHandler}>
					<MaterialCommunityIcons name="delete" color="#808080" size={20} />
				</Pressable>
			</View>
			<CollapsiblePanel title="Event" isHeaderHidden={true} isOpen={active}>
				<View>
					<EventEditor {...props} closePopup={closePopup} />
					{isModule && exposedModule && (
						<View style={{ paddingHorizontal: 4 }}>
							<DropDownControl
								options={[
									{ value: "both", name: "Navigate & Open Link" },
									{ value: "navigate", name: "Navigate Pages" },
									{ value: "link", name: "Open Link" },
								]}
								valueKey="value"
								nameKey="name"
								defaultValue={exposedModule?.get("type")}
								label={"Type of Event"}
								onChange={(value: string) => {
									onModulePropValueChange("type", value);
								}}
								value={exposedModule?.get("type")}
							/>
							<CodeInputControl
								value={exposedModule?.get("name")}
								label={"Event Name"}
								onChange={(value: string) => {
									nameChange(value);
								}}
							/>
						</View>
					)}
				</View>
			</CollapsiblePanel>
		</View>
	);
};

export interface PIEventsInspectorProps<ConfigType = "eventsEditor">
	extends PropertyEditorProps {}

const EventsInspector: React.FC<PIEventsInspectorProps> = ({
	pluginId,
	configProps,
	pageId,
	entityConfig,
	config,
	configPathSelector,
	pluginConfigEventUpdate,
	pluginConfigDeleteEvent,
	reorderEventUpward,
	reorderEventDownward,
	onChange,
	onCustomPropChange,
	onUpdateRawValue,
}) => {
	const { plugins, queries } = useSelector(selectPageAndGlobalPluginByType);
	const screens = useSelector(selectScreensInNav);

	// const editorProps = editor.props as any;
	const { triggers } = configProps;

	// const selectedPluginConf = pluginId ? entityConfig.config : entityConfig;
	const inModule = pluginId
		? !_.isEmpty(entityConfig.namespace?.getNamespace())
		: false;
	const isModule = pluginId ? !!entityConfig.config.get("moduleUUID") : false;
	const parentModuleEvents = useSelector((state) =>
		selectParentModuleEvents(state, pageId, pluginId),
	);

	const [events, setEvents] = useState<
		Immutable.List<{ selected: boolean; event: EventHandlerConfig }>
	>(
		config
			.get("events")
			?.map((event: EventHandlerConfig) => ({ selected: false, event })) ??
			Immutable.List<{ selected: boolean; event: EventHandlerConfig }>(),
	);
	const [eventsClipboard, setEventsClipboard] = useLocalStorage<{
		pluginId: string | null;
		clipboard: EventHandlerConfig[];
	}>("eventsClipboard", { pluginId: null, clipboard: [] });

	const rawEvents = useMemo(
		() => events.toArray().map((i) => i.event),
		[events],
	);

	const isAllSelected = useMemo(() => {
		const selectedEvents = events.filter((i) => i.selected);
		if (!events.size) return false;
		return selectedEvents.size === events.size;
	}, [rawEvents, eventsClipboard, pluginId]);

	const { isCopyDisabled, isPasteDisabled } = useMemo(() => {
		const selectedEvents = events.filter((i) => i.selected);
		return {
			isCopyDisabled: !selectedEvents.size,
			isPasteDisabled:
				!eventsClipboard.clipboard.length || !eventsClipboard.pluginId,
		};
	}, [events, eventsClipboard, pluginId]);

	useEffect(() => {
		config
			.get("events")
			?.map((event: EventHandlerConfig) => ({ selected: false, event })) ??
			Immutable.List<{ selected: boolean; event: EventHandlerConfig }>();
	}, [config]);
	if (!config) return null;

	const onEventUpdate = useCallback(
		(eventIndex: number, event: EventHandlerConfig) => {
			let newEvents = events.set(eventIndex, {
				selected: events.get(eventIndex)?.selected || false,
				event,
			});
			setEvents(newEvents);
			onUpdateRawValue(newEvents.map((i) => i.event));
		},
		[events, onUpdateRawValue],
	);
	const onEventDelete = useCallback(
		(eventIndex: number) => {
			if (eventIndex > events.size - 1) return;
			let newEvents = events
				.take(eventIndex)
				.concat(events.skip(eventIndex + 1));
			setEvents(newEvents);
			onUpdateRawValue(newEvents.map((i) => i.event));
		},
		[events, onUpdateRawValue],
	);
	const reorderPluginEventUpward = useCallback(
		(eventIndex: number) => {
			if (eventIndex == 0) return;
			let newEvents = events
				.take(eventIndex - 1)
				.push(events.get(eventIndex))
				.push(events.get(eventIndex - 1))
				.concat(events.skip(eventIndex + 1));
			setEvents(newEvents);
			onUpdateRawValue(newEvents.map((i) => i.event));
		},
		[events, onUpdateRawValue],
	);
	const reorderPluginEventDownward = useCallback(
		(eventIndex: number) => {
			if (eventIndex >= events.size - 1) return;
			let newEvents = events
				.take(eventIndex)
				.push(events.get(eventIndex + 1))
				.push(events.get(eventIndex))
				.concat(events.skip(eventIndex + 2));
			setEvents(newEvents);
			onUpdateRawValue(newEvents.map((i) => i.event));
		},
		[events, onUpdateRawValue],
	);

	const getActionOption = (type: string) => {
		return _.find(actionOptions, (actionOption) => actionOption.type === type);
	};

	const onAddEventHandler = useCallback(() => {
		const eventConfig = new EventHandlerConfig({
			label: undefined,
			type: undefined,
			method: "",
			pluginId: null,
			isGlobalPlugin: false,
			params: Immutable.Map(),
		});
		let newEvents = events.push({ selected: false, event: eventConfig });
		setEvents(newEvents);
		onUpdateRawValue(newEvents.map((i) => i.event));
	}, [events, onUpdateRawValue]);

	const onEventSelect = useCallback((eventId: number) => {
		setEvents((previous) =>
			previous.map((i, index) => ({
				...i,
				selected: index === eventId ? !i.selected : i.selected,
			})),
		);
	}, []);

	const onPaste = useCallback(() => {
		const newEvents = events.concat(
			eventsClipboard.clipboard.map((event) => ({
				selected: false,
				event: new EventHandlerConfig({
					label: event.label,
					type: event.type,
					method: event.method,
					pluginId: event.pluginId,
					isGlobalPlugin: event.isGlobalPlugin,
					screenName: event.screenName,
					prop: event.prop,
					value: event.value,
					params: Immutable.Map(event.params),
					hasCondition: event.hasCondition,
					condition: event.condition,
				}),
			})),
		);
		setEvents(newEvents);
		onUpdateRawValue(newEvents.map((i) => i.event));
		console.groupEnd();
	}, [pluginId, eventsClipboard, events, onUpdateRawValue]);

	return (
		<View style={{ flex: 1 }}>
			{/* <Pressable pointerEvents="auto" style={[styles.addEventRow]} onPress={onAddEventHandler}>
        <Text style={styles.actionLinkText}>+ Add Event</Text>
      </Pressable> */}
			<View
				style={{
					display: "flex",
					alignItems: "center",
					flexDirection: "row",
					justifyContent: "space-between",
					marginBottom: 10,
				}}
			>
				<View
					style={{
						display: "flex",
						alignItems: "center",
						flexDirection: "row",
					}}
				>
					<Pressable
						pointerEvents="auto"
						style={[styles.eventOption, { marginRight: 10 }]}
						onPress={() => {
							// setEventsClipboard(previous =>
							//   _.isEqual(rawEvents, previous.clipboard)
							//     ? {
							//         pluginId: null,
							//         clipboard: [],
							//       }
							//     : {
							//         pluginId,
							//         clipboard: rawEvents,
							//       },
							// );
							setEvents((previous) =>
								previous.map((i) => ({ ...i, selected: !isAllSelected })),
							);
						}}
					>
						<MaterialCommunityIcons
							name="select-all"
							size={14}
							color="#3c92dc"
						/>
						<Text style={styles.actionLinkText}>
							{isAllSelected ? "Unselect" : "Select"} All
						</Text>
					</Pressable>
					{!isCopyDisabled && (
						<Pressable
							pointerEvents={isCopyDisabled ? "none" : "auto"}
							style={[styles.eventOption, { marginRight: 10 }]}
							onPress={() => {
								setEventsClipboard({
									pluginId,
									clipboard: events
										.filter((i) => i.selected)
										.map((i) => i.event)
										.toArray(),
								});
							}}
							disabled={isCopyDisabled}
						>
							<MaterialCommunityIcons
								name="content-copy"
								size={14}
								color="#3c92dc"
							/>
							<Text style={styles.actionLinkText}>Copy</Text>
						</Pressable>
					)}
					{!isPasteDisabled && (
						<Pressable
							pointerEvents={isPasteDisabled ? "none" : "auto"}
							style={[styles.eventOption, { marginRight: 10 }]}
							onPress={onPaste}
							disabled={isPasteDisabled}
						>
							<MaterialCommunityIcons
								name="content-paste"
								size={14}
								color="#3c92dc"
							/>
							<Text style={styles.actionLinkText}>Paste</Text>
						</Pressable>
					)}
				</View>
				<Pressable
					pointerEvents="auto"
					style={styles.eventOption}
					onPress={onAddEventHandler}
				>
					<MaterialCommunityIcons name="plus" size={14} color="#3c92dc" />
					<Text style={styles.actionLinkText}>Add Event</Text>
				</Pressable>
			</View>

			{events &&
				events.map(({ event, selected }, eventId) => {
					return (
						<EditorEventItem
							key={pluginId + "" + eventId}
							selected={selected}
							moduleUUID={
								entityConfig.config ? entityConfig.config.get("moduleUUID") : ""
							}
							{...{
								eventId,
								event,
								triggers,
								plugins,
								queries,
								screens,
								reorderEventUpward: reorderPluginEventUpward,
								reorderEventDownward: reorderPluginEventDownward,
								inModule,
								parentModuleEvents,
								pageId,
								configPathSelector,
								isModule,
							}}
							onEventSelect={onEventSelect}
							onEventDelete={onEventDelete}
							onEventUpdate={onEventUpdate}
						/>
					);
				})}
		</View>
	);
};

export default EventsInspector;

const styles = StyleSheet.create({
	eventListItem: {
		flex: 1,
		flexDirection: "column",
		flexBasis: "auto",
		flexGrow: 1,
		elevation: 2,
	},
	eventListingLabel: {
		alignContent: "center",
		padding: 4,
		flex: 1,
		flexBasis: "auto",
		backgroundColor: "#0091bc",
		borderRadius: 4,
		marginHorizontal: 4,
		flexWrap: "wrap",
		flexGrow: 0,
		fontSize: theme.FONT_SIZE,
		lineHeight: theme.LINE_HEIGHT,
		color: theme.FONT_COLOR,
	},
	eventListingMethod: {
		flex: 1,
		flexGrow: 0,
		fontSize: 10,
		lineHeight: 12,
		flexDirection: "row",
		flexWrap: "wrap",
		flexShrink: 1,
	},
	eventListingDisplayMethod: {
		justifyContent: "center",
		flex: 1,
		flexWrap: "wrap",
		flexShrink: 1,
	},
	addEventRow: {
		flexDirection: "row",
		margin: 2,
		borderRadius: 4,
		justifyContent: "flex-end",
	},
	eventListingBox: {
		flexDirection: "row",
		margin: 2,
		borderRadius: 4,
	},
	eventListingLayout: {
		flex: 1,
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		backgroundColor: "#f8f8f8",
		overflow: "scroll",
	},
	actionLinkText: {
		textAlign: "right",
		flex: 1,
		flexGrow: 0,
		padding: 3,
		color: "#3c92dc",
		flexBasis: "auto",
	},
	deleteButton: { margin: 2, padding: 2 },
	upIcon: {
		borderRadius: 40,
	},
	downIcom: {
		borderRadius: 40,
		marginLeft: 4,
	},
	iconContainer: {
		flexDirection: "row",
		alignItems: "center",
	},
	eventItemBox: { flexDirection: "row" },
	eventOption: {
		display: "flex",
		alignItems: "center",
		flexDirection: "row",
		borderColor: "#3c92dc",
		borderWidth: 1,
		borderRadius: 4,
		padding: 2,
	},
});
