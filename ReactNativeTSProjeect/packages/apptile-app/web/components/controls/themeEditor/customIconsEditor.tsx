import React, {useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';

import {MaterialCommunityIcons} from 'apptile-core';
import Uploader<PERSON>pi from '@/root/web/api/UploaderApi';
import AssetsApi from '@/root/web/api/AssetsApi';
import {apptileStateSelector} from 'apptile-core';
import {CustomIconContext} from 'apptile-core';
// import CustomIcon from '@/root/app/components/common/CustomIcon';

const styles = StyleSheet.create({
  rootContainer: {
    padding: 8,
  },
  iconsContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  iconContainer: {
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#0092BC',
    margin: 3,
    padding: 4,
    position: 'relative',
    zIndex: 1,
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconTooltip: {
    position: 'absolute',
    top: 44,
    padding: 4,
    backgroundColor: '#0000009e',
    maxWidth: 52,
    left: '50%',
    transform: [{translateX: '-50%'}],
    textAlign: 'center',
    borderRadius: 4,
  },
  tooltipText: {
    color: 'white',
    // whiteSpace: 'nowrap',
  },
  textInputContainer: {
    marginTop: 12,
    paddingTop: 16,
  },
  textInput: {
    padding: 8,
    borderWidth: 1,
    borderColor: 'rgb(221, 221, 221)',
    borderRadius: 4,
  },
  fileInputContainer: {
    paddingTop: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  uploadButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: '#0092BC',
    borderRadius: 6,
  },
  uploadText: {
    color: 'white',
    marginLeft: 6,
  },
  instructionContainer: {
    marginTop: 12,
    paddingTop: 6,
    borderTopWidth: 1,
    borderColor: '#0092BC',
  },
  instructionText: {
    fontSize: 11,
    color: '#9e9e9e',
  },
});

export default function CustomIconConsumer() {
  return <CustomIconContext.Consumer>{Icon => <CustomIconsEditor Icon={Icon} />}</CustomIconContext.Consumer>;
}

function CustomIconsEditor({Icon}: any) {
  const activeTimer = useRef<NodeJS.Timeout>();
  useEffect(() => {
    return () => clearTimeout(activeTimer.current);
  }, []);

  const [tooltipVisibleFor, setTooltipVisibility] = useState('');
  const toggleTooltip = (icon: string) => {
    clearTimeout(activeTimer.current);
    setTooltipVisibility(icon);
    navigator.clipboard?.writeText(icon);
    activeTimer.current = setTimeout(() => {
      setTooltipVisibility('');
    }, 1750);
  };

  /** form handling */
  const app = useSelector(apptileStateSelector);
  const inputRef = useRef<HTMLInputElement>(null);
  const [status, setStatus] = useState<'input' | 'processing' | 'success' | 'failed'>('input');
  useEffect(() => {
    let id: NodeJS.Timeout;
    if (status === 'success')
      id = setTimeout(() => {
        setStatus('input');
      }, 5000);

    return () => {
      if (id) clearTimeout(id);
    };
  }, [status]);
  const [form, setForm] = useState<{name: string; icon: File | null}>({name: '', icon: null});
  const resetForm = () => {
    setForm({name: '', icon: null});
    if (inputRef.current) inputRef.current.value = '';
  };
  const handleSubmit = async () => {
    if (!form.icon || !form.name) return;
    const response = await UploaderApi.UploadAssetApi({file: form.icon}, () => {});

    setStatus('processing');
    await AssetsApi.addIconToCurrentApp(app.appId as string, {
      name: form.name,
      fileKey: response.data.fileKey,
    });
    resetForm();
    setStatus('success');
  };

  const icons = Icon ? Icon.getRawGlyphMap() : null;

  return (
    <View style={styles.rootContainer}>
      <View style={styles.iconsContainer}>
        {icons &&
          Object.keys(icons).map((icon, idx, arr) => (
            <TouchableOpacity
              key={icon}
              style={[
                styles.iconContainer,
                {backgroundColor: tooltipVisibleFor === icon ? '#ededed' : 'transparent', zIndex: arr.length - idx},
              ]}
              onPress={() => toggleTooltip(tooltipVisibleFor === icon ? '' : icon)}>
              <Icon name={icon} size={32} />

              <View style={[styles.iconTooltip, {display: tooltipVisibleFor === icon ? 'flex' : 'none'}]}>
                <Text style={styles.tooltipText}>{icon}</Text>
              </View>
            </TouchableOpacity>
          ))}
      </View>

      <View style={styles.textInputContainer}>
        <label>Icon name</label>
        <TextInput
          style={styles.textInput}
          value={form.name}
          onChangeText={text => setForm({...form, name: text.trim()})}
        />
      </View>
      <View style={styles.fileInputContainer}>
        <input
          ref={inputRef}
          type="file"
          accept="image/svg+xml"
          onChange={e => setForm({...form, icon: (e.target.files || [])[0]})}
        />
        <Pressable
          style={[
            styles.uploadButton,
            !(form.name && form.icon) || status === 'processing' ? {backgroundColor: '#bbbbbb'} : {},
          ]}
          disabled={!(form.name && form.icon) || status === 'processing'}
          onPress={handleSubmit}>
          <MaterialCommunityIcons name="cloud-upload" color="white" size={18} />
          {status === 'processing' ? (
            <Text style={styles.uploadText}>Uploading...</Text>
          ) : (
            <Text style={styles.uploadText}>Upload</Text>
          )}
        </Pressable>
      </View>

      {status === 'success' && (
        <Text style={[styles.instructionText, {color: 'green', marginVertical: 4}]}>
          Upload success, Icon set generation might take some time.
        </Text>
      )}

      <View style={styles.instructionContainer}>
        <Text style={styles.instructionText}>
          Usage: Select `Custom Icon` in icon options and enter correct name in input.
        </Text>
      </View>
    </View>
  );
}
