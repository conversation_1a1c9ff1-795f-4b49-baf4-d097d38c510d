import React from 'react';
import {Text, StyleSheet} from 'react-native';

interface TitleProps {
  content: string;
}
export const Title: React.FC<TitleProps> = props => {
  return (
    <Text style={styles.titleContainer}>
      <Text style={styles.titleText}>{props.content}</Text>
    </Text>
  );
};

const styles = StyleSheet.create({
  titleContainer: {marginVertical: 15, width: '100%'},
  titleText: {fontSize: 16, color: '#334155', borderLeftWidth: 6, borderColor: '#0092bc', paddingLeft: 10},
});
