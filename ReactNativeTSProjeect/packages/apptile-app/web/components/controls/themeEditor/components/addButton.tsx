import React from 'react';
import {View, StyleSheet, Pressable, TextInput} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';

interface AddButton {
  placeholder: string;
  textInputValue: string;
  onTextChange: (value: string) => void;
  onAddItemHandler: () => void;
}

export const AddButton: React.FC<AddButton> = props => {
  return (
    <View style={[styles.container]}>
      <TextInput
        onChangeText={props.onTextChange}
        value={props.textInputValue}
        placeholder={props.placeholder}
        style={styles.inputBox}
      />
      <View style={[styles.buttonContainer, styles.addButtonContainer]}>
        <Pressable onPress={props.onAddItemHandler}>
          <MaterialCommunityIcons name="plus" size={24} color="#fff" />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: -1,
    justifyContent: 'flex-end',
    width: '100%',
    flexDirection: 'row',
    position: 'relative',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#CFFAFE',
    padding: 8,
    borderRadius: 9999,
    width: 40,
    height: 40,
    position: 'relative',
  },
  inputBox: {
    width: 150,
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#F8FAFC',
    paddingVertical: 5,
    paddingHorizontal: 10,
    color: '#475569',
  },
  addButtonContainer: {borderRadius: 0, backgroundColor: '#0092bc'},
});
