import React from 'react';
import {View, StyleSheet, Text, Pressable, TextInput} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {Title, AddButton} from './components';
import {selectAppConfig} from 'apptile-core';
import {baseGlobalThemeConfig} from 'apptile-core';
import {ApptileThemeConfigParams} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {addCustomPropTheme, updateCustomPropTheme, removeCustomPropTheme} from '@/root/web/actions/themeActions';
import {makeToast} from '@/root/web/actions/toastActions';
import {debounce} from 'lodash';

export const CustomEditor: React.FC = () => {
  const appConfig = useSelector(selectAppConfig);
  let theme = (appConfig?.get('theme')?.toJS() ?? baseGlobalThemeConfig) as ApptileThemeConfigParams;

  const {config} = theme;
  const custom = config.custom ?? {};

  const dispatch = useDispatch();

  const [customName, setCustomName] = React.useState('');

  const addCustomHandler = () => {
    if (!customName) {
      return;
    }

    // convert space to dash
    const parsedCustomName = customName.trim().split(' ').join('_');

    // can't add already existing key
    if (custom[parsedCustomName]) {
      dispatch(
        makeToast({
          content: "Can't add already existing colorName",
          appearances: 'info',
        }),
      );
      setCustomName('');
      return;
    }

    dispatch(
      addCustomPropTheme({
        name: parsedCustomName,
        value: '',
      }),
    );

    setCustomName('');
  };

  const onChangeHandler = (customKey: string, customValue: string) => {
    dispatch(
      updateCustomPropTheme({
        name: customKey,
        value: customValue,
      }),
    );
  };

  const debouncedOnChangeHandler = debounce(onChangeHandler, 250);

  return (
    <View style={styles.container}>
      {/* Typography Scroll */}
      <View>
        <Title content="Keys" />
        {Object.entries(custom).map(entry => {
          const customKey = entry[0];
          const customValue = entry[1];
          return (
            <CustomEl
              customKey={customKey}
              customValue={customValue as string}
              onChange={debouncedOnChangeHandler}
              key={`custom-${customKey}`}
            />
          );
        })}
      </View>
      {/* Add Custom */}
      <AddButton
        placeholder="customName"
        textInputValue={customName}
        onTextChange={setCustomName}
        onAddItemHandler={addCustomHandler}
      />
    </View>
  );
};

interface CustomInputProp {
  customKey: string;
  customValue: string;
  onChange: (customKey: string, customValue: string) => void;
}

const CustomEl: React.FC<CustomInputProp> = props => {
  const dispatch = useDispatch();

  const onChangeText = (value: string) => {
    props.onChange(props.customKey, value);
  };

  const deleteCustomHandler = () => {
    dispatch(removeCustomPropTheme(props.customKey));
  };

  return (
    <View style={styles.customElContainer}>
      <Text style={styles.customKeyText}>{props.customKey}</Text>
      <View style={styles.customInputContainer}>
        <Pressable onPress={deleteCustomHandler} style={styles.deleteButtonUnReserved}>
          <MaterialCommunityIcons name="close" size={20} color="#fff" />
        </Pressable>
        <TextInput
          onChangeText={onChangeText}
          defaultValue={props.customValue}
          placeholder="value"
          style={styles.customInput}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    padding: 15,
  },
  customKeyText: {fontSize: 15, marginHorizontal: 10},
  customInputContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  customElContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 10,
    alignItems: 'center',
    marginVertical: 10,
  },
  deleteButtonUnReserved: {backgroundColor: '#EF4444', paddingVertical: 2.5},
  customInput: {
    width: 100,
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#F8FAFC',
    paddingVertical: 5,
    color: '#475569',
  },
});
