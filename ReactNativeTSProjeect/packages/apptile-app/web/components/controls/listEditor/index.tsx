import React, {use<PERSON><PERSON>back, useMemo, useState} from 'react';
import {Pressable, StyleSheet, Switch, Text, View} from 'react-native';
import {connect, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {IImageListItem} from '@/root/app/plugins/state/DisplayImageList/ImageListTypes';
import ImageListItem from './ImageListItem';
import _ from 'lodash';
import {pluginConfigValueSetRaw} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import SortableList from '../../SortableList';
import {imageListEditorProps} from '../../pluginEditorComponents';
import theme from '@/root/web/styles-v2/theme';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface ImageListEditorControlProps extends imageListEditorProps {
  // pluginId: string;
  // pageId: string;
  // name: string;
  // value: Array<any>;
  // label: string;
  // placeholder?: string;
  // disableAdd?: boolean;
  // onChange: (value: any) => void;

  // FIXME: Calback for Shopify editor to know we updated.
  // Calling onChange causes sideeffects in normal editor due to Property Inspector causing a duplicate update.
  // This is an additional callback only used by Shopify Integration.
  valueUpdated?: (value: any) => void;

  // onCustomPropChange: (key: string, value: any) => void;
  // config: Immutable.Map<string, any>;

  // pluginConfigValueSetRaw: typeof pluginConfigValueSetRaw;
}

const _ImageListEditorControl: React.FC<ImageListEditorControlProps> = props => {
  const {pageId, pluginId, name, value, configProps, valueUpdated, pluginConfigValueSetRaw} = props;
  const {label, disableAdd, shopifyType, minLength = '1', maxLength = '1000'} = configProps;
  const [imageList, setImageList] = useState<Array<IImageListItem>>(value);
  const onValueChange = useCallback(
    newVal => {
      setImageList(newVal);
      pluginConfigValueSetRaw(pluginId, pageId, name, newVal);
      valueUpdated && valueUpdated(newVal);
    },
    [name, pageId, pluginConfigValueSetRaw, pluginId],
  );
  /// FIXME: SHOPIFY related customizations
  const platform = useSelector(state => state.platform);
  const [isEmbeddedInShopify] = useState(platform?.isEmbeddedInShopify);
  //// FIXME: End SHOPIFY customizations

  const onAddItem = useCallback(() => {
    if (imageList?.length < (isNaN(maxLength) ? 10000 : parseInt(maxLength))) {
      const newList = imageList.concat({
        url: '',
        sourceType: 'url',
        assetId: '',
        resizeMode: 'cover',
        navEntityType: shopifyType ?? '',
        navEntityId: '',
      });
      // setImageList(newList);
      onValueChange(newList);
    }
  }, [imageList, maxLength, onValueChange, shopifyType]);

  const onUpdateImageItem = useCallback(
    (index, item) => {
      const newList = imageList.slice();
      newList[index] = {...item};
      // setImageList(newList);
      onValueChange(newList);
    },
    [imageList, onValueChange],
  );
  const onDeleteImageItem = useCallback(
    index => {
      if (imageList?.length > (isNaN(minLength) ? 1 : parseInt(minLength))) {
        var newList = imageList;
        _.pullAt(newList, index);
        newList = newList.slice();
        // setImageList(newList);
        onValueChange(newList);
      }
    },
    [imageList, minLength, onValueChange],
  );
  const updateImagesOrder = useCallback(
    (reorderedList: [any, IImageListItem][]) => {
      let vals: IImageListItem[] = [];
      reorderedList?.map(([key, val]) => {
        vals.push(val);
      });
      setImageList(vals);
      onValueChange(vals);
    },
    [onValueChange],
  );

  return (
    <View style={styles.container}>
      <View style={styles.columnLayout}>
        {!isEmbeddedInShopify && (
          <View style={[styles.rowLayout, {zIndex: 1}]}>
            <View style={[styles.rowLayout]}>
              <Text style={commonStyles.labelText}>{label}</Text>
            </View>
            <View style={[{flex: 1}]}>
              {!disableAdd && (
                <Tooltip
                  visible={imageList?.length >= parseInt(maxLength, 10)}
                  tooltipPosition={'Top'}
                  tooltip={
                    <View style={[styles.tooltip]}>
                      <Text style={[commonStyles.baseText, commonStyles.errorText]}>
                        This list has maximum limit of {maxLength}
                      </Text>
                    </View>
                  }>
                  <Pressable
                    disabled={imageList?.length >= parseInt(maxLength, 10)}
                    onPress={onAddItem}
                    style={[styles.rowLayout, {justifyContent: 'flex-end'}]}>
                    <MaterialCommunityIcons
                      name="plus"
                      size={15}
                      color={
                        imageList?.length >= parseInt(maxLength, 10)
                          ? theme.CONTROL_PLACEHOLDER_COLOR
                          : theme.CONTROL_ACTIVE_COLOR
                      }
                    />
                    <Text
                      style={[
                        commonStyles.baseText,
                        {
                          color:
                            imageList?.length >= parseInt(maxLength, 10)
                              ? theme.CONTROL_PLACEHOLDER_COLOR
                              : theme.CONTROL_ACTIVE_COLOR,
                        },
                      ]}>
                      {' '}
                      ADD
                    </Text>
                  </Pressable>
                </Tooltip>
              )}
            </View>
          </View>
        )}
        {isEmbeddedInShopify && (
          <View style={styles.rowLayout}>
            <View style={[styles.rowLayout]}>
              <Text style={commonStyles.labelText}>{label}</Text>
            </View>
            <View style={[]}>
              {!disableAdd && (
                <Pressable onPress={onAddItem} style={[styles.rowLayout, {justifyContent: 'flex-end'}]}>
                  <MaterialCommunityIcons name="plus" size={15} color={theme.CONTROL_ACTIVE_COLOR} />
                  <Text style={[commonStyles.baseText, {color: theme.CONTROL_ACTIVE_COLOR}]}> ADD</Text>
                </Pressable>
              )}
            </View>
          </View>
        )}
        <View style={[styles.columnLayout, {paddingBottom: 10, zIndex: 1}]}>
          {Array.isArray(imageList) && (
            <SortableList
              dragKey={`${pluginId}-${name}-list-editor`}
              data={_.toPairs(imageList)}
              onChange={updateImagesOrder}
              itemComponent={ImageListItemComponent}
              componentProps={{
                onUpdateImageItem,
                onDeleteImageItem,
                disableAdd,
                shopifyType,
                totalItems: imageList?.length,
                minLength: parseInt(minLength, 10),
              }}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const ImageListItemComponent = ({
  itemVal,
  itemKey,
  disableAdd,
  totalItems,
  minLength,
  shopifyType,
  onUpdateImageItem,
  onDeleteImageItem,
}) => {
  return (
    <ImageListItem
      key={itemKey}
      imageItem={itemVal}
      disableRemove={disableAdd}
      shopifyType={shopifyType}
      minLength={minLength}
      totalItems={totalItems}
      onUpdateImageItem={val => onUpdateImageItem(itemKey, val)}
      onDeleteImageItem={() => onDeleteImageItem(itemKey)}
    />
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    justifyContent: 'center',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: 5,
  },
  labelText: {
    fontFamily: theme.FONT_FAMILY,
    minWidth: '30%',
    maxWidth: '50%',
    marginRight: 4,
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
  checkboxStyle: {},
});

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        pluginConfigValueSetRaw,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = () => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(_ImageListEditorControl);
