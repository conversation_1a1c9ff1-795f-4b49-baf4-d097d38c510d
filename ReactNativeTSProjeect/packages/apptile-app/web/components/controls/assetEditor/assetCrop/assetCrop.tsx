import React, {useState, useEffect, useCallback} from 'react';
import 'react-image-crop/dist/ReactCrop.css';
import ReactCrop, {centerCrop, makeAspectCrop, Crop, PixelCrop} from 'react-image-crop';
import {getCroppedImage} from './getCroppedImage';
import {StyleSheet, Text, View} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import CheckboxControl from '../../CheckboxControl';
// This is to demonstate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: 'px',
        height: 200,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

const AssetCrop: React.FC<any> = props => {
  const [imgSrc, setImgSrc] = useState('');
  const {aspectRatio} = props;
  //const imgRef = useRef<HTMLImageElement>(null);
  const [imgRef, setImgRef] = useState<HTMLImageElement>(null);
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [aspect, setAspect] = useState<number | undefined>(aspectRatio);
  const [processing, setProcessing] = useState(false);
  const [croppedImage, setCroppedImage] = useState(null);
  const [croppedMeta, setCroppedMeta] = useState(null);
  const [imageLayoutDetails, setImageLayoutDetails] = useState({});
  // const [isFreeFlow, setIsFreeFlow] = useState(!aspectRatio);

  const {cropPreview, startCropUpload} = props;
  //For setting the cropped image blob in the parent component

  // useEffect(() => {
  //   if (isFreeFlow) {
  //     setAspect(undefined);
  //   } else {
  //     setAspect(aspectRatio);
  //     onImageLoad({currentTarget: imageLayoutDetails}, aspectRatio);
  //   }
  // }, [setAspect, isFreeFlow, aspectRatio]);

  useEffect(() => {
    if (cropPreview && completedCrop && imgRef) {
      setProcessing(true);
      const {croppedFile, metaData} = getCroppedImage(imgRef, completedCrop, props.files[0], imageLayoutDetails);
      croppedFile.then(value => {
        setCroppedImage(value);
        setCroppedMeta(metaData);
        setProcessing(false);
      });
    }
  }, [completedCrop, props, cropPreview, imgRef, imageLayoutDetails]);

  const cropUpload = useCallback(() => {
    if (croppedImage) {
      const metaData = croppedImage?.metaData;
      delete croppedImage?.metaData;
      props.uploadAppAsset({file: croppedImage, metaData});
      setCroppedImage(null);
    }
  }, [croppedImage]);

  useEffect(() => {
    if (startCropUpload) {
      props.files[0].metaData = croppedMeta;
      props.uploaderRef.current = cropUpload;
      props.setCroppedImage(props.files[0]);
      props.disableButtons(true);
    }
  }, [croppedImage, croppedMeta, props, startCropUpload]);

  useEffect(() => {
    setCrop(undefined); // Makes crop preview update between images.
    const reader = new FileReader();
    reader.addEventListener('load', () => setImgSrc(reader.result?.toString() || ''));
    reader.readAsDataURL(props.files[0]);
  }, [props.files]);

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>, ratio: number) {
    const requiredAspect = ratio ?? 1;
    const {naturalWidth, naturalHeight, width, height} = e.currentTarget;
    setCrop(centerAspectCrop(width, height, requiredAspect));
    setCompletedCrop(centerAspectCrop(width, height, requiredAspect));
    setImageLayoutDetails({naturalHeight, naturalWidth, width, height});
  }

  const desiredWidth = imgRef ? (imgRef?.naturalWidth / imgRef?.naturalHeight) * imgRef?.height : '100%';

  return (
    <View style={styles.cropperWrapper}>
      {imgSrc ? (
        <>
          {/* <Text style={styles.cropHeaderHeight}>
            Crop Dimensions(px): {Math.round(completedCrop?.width)} x {Math.round(completedCrop?.height)}
          </Text> */}

          {/* Showing selected image from upload button here */}
          {props.showOriginal && (
            <img
              alt="Orinial"
              src={imgSrc}
              style={{
                maxHeight: 312,
                objectFit: 'contain',
                transform: `scale(${scale}) rotate(${rotate}deg)`,
              }}
            />
          )}

          {!props.showOriginal && !cropPreview ? (
            <>
              {/* <View style={{width: '100%', alignItems: 'flex-end'}}>
                <View style={[{width: 150}, !aspectRatio && {cursor: 'not-allowed'}]}>
                  <CheckboxControl
                    label="Recomended dimensions"
                    fullSizeLabel={'true'}
                    value={!isFreeFlow}
                    disabled={!aspectRatio}
                    onChange={value => setIsFreeFlow(!value)}
                  />
                </View>
              </View> */}

              {/* Crop container shows after clicking on Crop button */}
              <ReactCrop
                crop={crop}
                onChange={pixelCrop => setCrop(pixelCrop)}
                onComplete={c => setCompletedCrop(c)}
                style={{maxHeight: 312, width: desiredWidth}}
                keepSelection={true}
                aspect={aspect}>
                <img
                  ref={ref => {
                    ref && setImgRef(ref);
                  }}
                  alt="Crop me"
                  src={imgSrc}
                  style={{
                    maxHeight: 312,
                    objectFit: 'contain',
                    transform: `scale(${scale}) rotate(${rotate}deg)`,
                  }}
                  onLoad={e => onImageLoad(e, aspectRatio)}
                />
              </ReactCrop>
            </>
          ) : (
            // Container will display after clicking on Apply button after setting cropped area
            !props.showOriginal && (
              <img
                src={croppedImage?.path}
                style={{
                  border: '1px solid black',
                  objectFit: 'contain',
                  width: completedCrop?.width,
                  height: completedCrop?.height,
                  maxHeight: '100%',
                }}
              />
            )
          )}
          {processing && (
            <View style={styles.processingOverlay}>
              <Text style={styles.processingOverlayText}>Processing Image ...</Text>
            </View>
          )}
        </>
      ) : (
        <Text>Loading...</Text>
      )}
    </View>
  );
};

export default AssetCrop;

const styles = StyleSheet.create({
  cropHeaderHeight: {fontFamily: theme.FONT_FAMILY, fontSize: 12},
  cropperWrapper: {width: '100%', height: 314, justifyContent: 'center', alignItems: 'center'},
  processingOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  processingOverlayText: {
    color: '#FFFFFF',
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '500',
  },
});
