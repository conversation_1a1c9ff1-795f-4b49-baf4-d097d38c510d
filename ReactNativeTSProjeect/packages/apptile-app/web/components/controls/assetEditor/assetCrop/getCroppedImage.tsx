export const getCroppedImage = (image, crop, file, imageLayoutDetails) => {
  const {naturalWidth, naturalHeight, width, height} = imageLayoutDetails;

  const {name, lastModified, lastModifiedDate, fileType} = file;

  const canvas = document.createElement('canvas');
  const scaleX = naturalWidth / width;
  const scaleY = naturalHeight / height;
  const ctx = canvas.getContext('2d');

  canvas.width = crop.width * scaleX;
  canvas.height = crop.height * scaleY;

  const cropWidth = Math.round(crop.width * scaleX);
  const cropHeight = Math.round(crop.height * scaleY);
  const convertedX = Math.round(crop.x * scaleX);
  const convertedY = Math.round(crop.y * scaleY);

  const metaData = {width: cropWidth, height: cropHeight, x: convertedX, y: convertedY};

  ctx.drawImage(image, convertedX, convertedY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

  return {
    metaData,
    croppedFile: new Promise((resolve, reject) => {
      canvas.toBlob(blob => {
        // returning an error
        if (!blob) {
          reject(new Error('Can<PERSON> is empty'));
          return;
        }

        blob.name = name;
        blob.lastModified = lastModified;
        blob.lastModifiedDate = lastModifiedDate;
        // creating a Object URL representing the Blob object given
        blob.path = window.URL.createObjectURL(blob);

        resolve(blob);
      }, fileType);
    }),
  };
};
