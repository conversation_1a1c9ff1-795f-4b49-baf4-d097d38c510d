import React, {useState, useEffect} from 'react';
import {StyleSheet, Text, TouchableOpacity, Image, View, Pressable} from 'react-native';
import {EditorRootState} from '@/root/web/store/EditorRootState';
// import {TouchableOpacity} from 'react-native-gesture-handler';
import {useSelector, useDispatch} from 'react-redux';
import {fetchAppAssets, fetchAppAssetsLoadMore} from '../../../actions/editorActions';
import {debounce} from 'lodash';
import {assetEditorProps} from '../../pluginEditorComponents';
import AssetChooseDialog from './assetChooseDialog';
import theme from '@/root/web/styles-v2/theme';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import {MaterialCommunityIcons} from 'apptile-core';
import CodeInputControl from '../CodeInputControl';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import {selectPluginConfig} from 'apptile-core';
import CodeInputControlV2 from '../../controls-v2/CodeInputControl';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


const noValue = Symbol('noValue');
interface assetEditorCustomProps extends assetEditorProps {
  onBulkValueChange?: (update: any) => void;
  setOpened?: any;
}

const AssetEditor: React.FC<assetEditorCustomProps> = props => {
  const {
    pluginId,
    pageId,
    config,
    configProps,
    configPathSelector,
    entityConfig,
    onChange,
    onCustomPropChange,
    onBulkValueChange,
    setOpened,
  } = props;

  //Getting the aspect ratio of the image from plugin config
  const aspectRatio = useSelector(state =>
    selectPluginConfig(state, pageId, pluginId)?.get('layout')?.get('aspectRatio'),
  );

  const {sourceTypeProperty, urlProperty, assetProperty, label, disableBinding} = configProps;
  const sourceType = config.get(sourceTypeProperty, 'URL');
  const [showPopover, setShowPopover] = useState(false);
  const dispatch = useDispatch();

  /// FIXME: SHOPIFY related customizations
  const platform = useSelector(state => state.platform);
  const [isEmbeddedInShopify] = useState(platform?.isEmbeddedInShopify);
  //// FIXME: End SHOPIFY customizations

  useEffect(() => {
    if(setOpened) setOpened(showPopover);
  },[showPopover])
  const onChangeURL = (url: string) => {
    if (onBulkValueChange) onBulkValueChange({[urlProperty]: url, [sourceTypeProperty]: 'url'});
    else {
      onCustomPropChange(urlProperty, url);
      onCustomPropChange(sourceTypeProperty, 'url');
    }
    if(setOpened) setOpened(false);
    setShowPopover(false);
  };

  const onChooseAsset = (assetId: string) => {
    if (onBulkValueChange) onBulkValueChange({[assetProperty]: assetId, [sourceTypeProperty]: 'upload'});
    else {
      onCustomPropChange(assetProperty, assetId);
      onCustomPropChange(sourceTypeProperty, 'upload');
    }
    if(setOpened) setOpened(false);
    setShowPopover(false);
  };

  const assetUploader = useSelector((state: EditorRootState) => state.asset.assetUploader);
  const assetIds = useSelector((state: EditorRootState) => state.asset.assetIds);

  const [selectedAsset, setSelectedAsset] = useState<string>('');

  const {isFetching, isEndOfCatalogue, currentPage} = assetUploader;
  const appId = useSelector((state: EditorRootState) => state.apptile.appId as string);
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const previouslySelectedAssetId = config.get(assetProperty);
  const [currentAsset, setCurrentAsset] = useState(assetState.assetsById[previouslySelectedAssetId]);
  const [isBinding, setIsBinding] = useState(false);

  useMountEffect(() => {
    if (!assetState.assetsById[previouslySelectedAssetId] && sourceType?.toLowerCase() != 'url')
      dispatch(fetchAppAssets(appId, 1));
  });

  useEffect(() => {
    setCurrentAsset(assetState.assetsById[previouslySelectedAssetId]);
    if (
      !assetState.assetsById[previouslySelectedAssetId] &&
      sourceType?.toLowerCase() != 'url' &&
      !isFetching &&
      !isEndOfCatalogue
    ) {
      dispatch(fetchAppAssetsLoadMore(appId, currentPage + 1));
    }
  }, [isFetching]);

  useEffect(() => {
    setCurrentAsset(assetState.assetsById[previouslySelectedAssetId]);
    if (
      !assetState.assetsById[previouslySelectedAssetId] &&
      sourceType?.toLowerCase() != 'url' &&
      !isFetching &&
      !isEndOfCatalogue
    ) {
      dispatch(fetchAppAssetsLoadMore(appId, currentPage + 1));
    }
  }, [previouslySelectedAssetId]);

  const [thumburl, setThumbUrl] = useState(
    sourceType?.toLowerCase() == 'url' ? config.get(urlProperty) : currentAsset?.thumbUrl || '',
  );
  useEffect(() => {
    setThumbUrl(sourceType?.toLowerCase() == 'url' ? config.get(urlProperty) : currentAsset?.thumbUrl || '');
  }, [config, currentAsset?.thumbUrl, sourceType, urlProperty]);

  const debouncedOnValueChange = debounce((newVal, type) => {
    onCustomPropChange(configProps[type], newVal);
  }, 450);

  return (
    <>
      <View style={styles.container}>
        {label && (
          <View style={[commonStyles.labelContainer, {alignSelf: 'flex-start', paddingTop: 2}]}>
            {!disableBinding && !isBinding && (
              <Pressable onPress={() => setIsBinding(true)}>
                <MaterialCommunityIcons name="flash" size={18} />
              </Pressable>
            )}
            {!disableBinding && isBinding && (
              <Pressable onPress={() => setIsBinding(false)}>
                <MaterialCommunityIcons name="flash-off" size={18} />
              </Pressable>
            )}
            {!isBinding && (
              <View style={styles.labelTextWrapper}>
                <Text style={[commonStyles.labelText]}>{label}</Text>

                {aspectRatio && Number(aspectRatio) !== 0 && (
                  <Text style={styles.aspectText}>800 x {Math.round(800 / Number(aspectRatio))}</Text>
                )}
              </View>
            )}
          </View>
        )}
        {!isBinding && (
          <>
            <View style={[styles.inputStyle, label ? commonStyles.inputContainer : {width: '100%'}]}>
              <Image
                style={[styles.inputImage]}
                resizeMode="cover"
                source={thumburl || require('../../../assets/images/placeholder-image.png')}
              />
              <Pressable style={[commonStyles.input, styles.editButton]} onPress={() => {
                  if(setOpened) setOpened(true);
                  setShowPopover(true);
                }}>
                <ApptileWebIcon name={'edit-icon'} size={16} color={theme.CONTROL_PLACEHOLDER_COLOR} />
                <Text style={[commonStyles.baseText, {justifyContent: 'center'}]}> Edit</Text>
              </Pressable>
            </View>

            <AssetChooseDialog
              aspectRatio={aspectRatio}
              currentAssetId={previouslySelectedAssetId}
              currentURL={config.get(urlProperty)}
              onSelectAsset={onChooseAsset}
              onCloseDialog={(value)=> {
                if(setOpened) setOpened(value);
                setShowPopover(value);
              }}
              onChangeURL={onChangeURL}
              showDialog={showPopover}
              {...props}
            />
          </>
        )}
        {isBinding && (
          <View style={{flexDirection: 'column'}}>
            {isBinding && (
              <>
                <View style={[{flexDirection: 'row', alignItems: 'center'}, commonStyles.controlContainer]}>
                  <CodeInputControlV2
                    label={'Source Type'}
                    defaultValue={sourceType}
                    value={sourceType}
                    placeholder="{{item.sourceType}}"
                    onChange={(val: string) => debouncedOnValueChange(val, 'sourceTypeProperty')}
                  />
                </View>
                <View style={[{flexDirection: 'row', alignItems: 'center'}, commonStyles.controlContainer]}>
                  <CodeInputControlV2
                    label={'Asset Id'}
                    defaultValue={previouslySelectedAssetId}
                    value={previouslySelectedAssetId}
                    placeholder="{{item.assetId}}"
                    onChange={(val: string) => debouncedOnValueChange(val, 'assetProperty')}
                  />
                </View>
                <View style={[{flexDirection: 'row', alignItems: 'center'}, commonStyles.controlContainer]}>
                  <CodeInputControlV2
                    label={'Value'}
                    defaultValue={config.get(urlProperty)}
                    value={config.get(urlProperty)}
                    placeholder="{{item.value}}"
                    onChange={(val: string) => debouncedOnValueChange(val, 'urlProperty')}
                  />
                </View>
              </>
            )}
          </View>
        )}
      </View>
    </>
  );
};

export default AssetEditor;

const styles = StyleSheet.create({
  codeInputStyle: {
    flex: 4,
    borderRadius: 2,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  textInputStyle: {
    fontSize: 11,
    padding: 4,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  container: {
    flex: 1,
    flexShrink: 0,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: 4,
  },
  labelTextWrapper: {
    flexDirection: 'column',
    width: '100%',
  },
  aspectText: {
    fontSize: 10,
    color: 'grey',
    marginTop: 5,
    flexWrap: 'wrap',
    textAlign: 'center',
  },
  inputStyle: {
    flexDirection: 'row',
    borderRadius: 6,
    backgroundColor: theme.INPUT_BACKGROUND,
    height: 172,
    overflow: 'hidden',
  },
  inputImage: {
    height: 172,
    width: '100%',
    justifyContent: 'space-around',
  },
  urlText: {
    width: 'calc( 100% - 78px)',
    paddingVertical: '22px',
    paddingHorizontal: '2px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  rowContainer: {
    padding: 2,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btn: {
    padding: 4,
    fontSize: 10,
    backgroundColor: '#0091bc',
    color: '#fff',
    flex: 1,
  },
  editButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 8,
    flex: 1,
    padding: 4,
    borderRadius: 20,
    position: 'absolute',
    right: 10,
    bottom: 10,
  },
});
