import {
  AppConfig,
  ImmutableMapType,
  ModuleEditorRecord,
  PluginConfigType,
  PluginNamespaceImpl,
} from 'apptile-core';
import {addNamespace} from 'apptile-core';
import {PropertyEditorConfig} from '@/root/app/common/EditorControlTypes';
import {IModuleInstanceConfig} from '@/root/app/plugins/module/moduleInstance';
import {GetRegisteredPlugin, resolvePluginPropertyEditor} from 'apptile-core';
import {selectAppConfig, selectPluginConfig} from 'apptile-core';
import {selectModuleByUUID} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {TextInput, TouchableOpacity, View, Text} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {pluginConfigUpdatePath, pluginConfigSetPathValue} from 'apptile-core';
import PluginPropertyEditor from '../../../views/propertyInspector/components/PluginPropertyEditor';
import {MaterialCommunityIcons} from 'apptile-core';
import {styles} from './moduleEditorStyles';
import PropertyEditorControlSelector from '../editorControlSelector/propEditorContolSelector';
import CheckboxControl from '../CheckboxControl';
import CollapsiblePanel from '../../CollapsiblePanel';
import {NAMESPACE_SPERATOR} from 'apptile-core';
import RadioGroupControl from '../RadioGroupControl';
import {Plan, allAvailablePlans} from 'apptile-core';

export interface ModulePropertyEditorProps {
  pageId: string;
  parentModuleConfig: PluginConfigType<any>;
  editorRecord: ModuleEditorRecord;
  onRename: (name: string) => void;
  onDelete: () => void;
  onUpdateType: (editorConfig: PropertyEditorConfig<any>) => void;
  onUpdateMandatory: (mandatory: boolean) => void;
  onUpdateAdvance: (advance: boolean) => void;
  onUpdateBasePlan: (basePlan: string) => void;
}
const noValue = Symbol('noValue');

const ModulePropertyEditor: React.FC<ModulePropertyEditorProps> = props => {
  const {
    pageId,
    parentModuleConfig,
    editorRecord,
    pluginConfigUpdate,
    debouncedPluginConfigUpdate,
    onRename,
    onDelete,
    onUpdateType,
    onUpdateMandatory,
    onUpdateAdvance,
    onUpdateBasePlan,
  } = props;
  const {label, selector, mandatory, advanceProperty, basePlan} = editorRecord;
  let {editorType: propEditorRecord} = editorRecord;
  const appConfig: AppConfig = useSelector(selectAppConfig);
  const dispatch = useDispatch();
  const {pluginConfigUpdatePath: updateConfig, pluginConfigSetPathValue: setConfig} = useMemo(
    () => bindActionCreators({pluginConfigUpdatePath, pluginConfigSetPathValue}, dispatch),
    [dispatch],
  );
  const moduleInstanceConfig: ImmutableMapType<IModuleInstanceConfig> = parentModuleConfig.get('config');
  // console.log('moduleInstanceConfig', moduleInstanceConfig);
  const moduleUUID = moduleInstanceConfig.get('moduleUUID');
  const moduleName = moduleInstanceConfig.get('moduleName');
  const childNamespace = moduleInstanceConfig.get('childNamespace');
  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));
  const moduleNamespace = new PluginNamespaceImpl(
    (parentModuleConfig.namespace?.getNamespace() ?? []).concat([childNamespace]),
    parentModuleConfig.id,
  );
  const pluginId = addNamespace(moduleNamespace, selector[0]);
  const pluginConfig = useSelector(state => selectPluginConfig(state, pageId, pluginId));
  const propertyName = _.last(selector);
  const propertyUpdatePath = selector.slice(1, -1);
  const isQueryParam = selector.includes('inputVariables');
  let propEditor: PropertyEditorConfig<any> = propEditorRecord;
  // if (!isQueryParam) {
  //   propEditor = resolvePluginPropertyEditor(pluginConfig.subtype, propertyName);
  //   if (!propEditor) {
  //     propEditor = {
  //       name: propertyName,
  //       type: 'codeInput',
  //       props: {
  //         label: label,
  //         placeholder: '',
  //       },
  //     };
  //   } else {
  //     if (propEditor?.props?.label) propEditor = {...propEditor, props: {...propEditor?.props, label}};
  //   }
  // } else {
  //   const queryName = pluginConfig.getIn(['config', 'queryName']) as string;
  //   const datasourceId = pluginConfig.getIn(['config', 'datasource']) as string;
  //   const dsConfig = appConfig.getPlugin(datasourceId);
  //   const dsModel = GetRegisteredPlugin(dsConfig.subtype);
  //   const propEditors = dsModel?.getQueryInputParams(queryName);
  //   propEditor = _.find(propEditors, editor => editor.name === propertyName);
  //   if (propEditor?.props?.label) propEditor = {...propEditor, props: {...propEditor?.props, label}};
  // }
  if (!propEditor) {
    propEditor = {
      name: propertyName,
      type: 'codeInput',
      props: {
        label: label,
        placeholder: '',
      },
    };
  }
  propEditor.name = propertyName;
  propEditor.props.label = label;

  if (!isQueryParam && !propEditorRecord) {
    const originalPropertyConfig = resolvePluginPropertyEditor(pluginConfig?.subtype, propertyName);
    if (originalPropertyConfig && originalPropertyConfig.props)
      propEditorRecord = {props: originalPropertyConfig.props};
  }
  const [isRenaming, setRenaming] = useState(false);
  const setEditingName = useCallback(() => {
    setRenaming(true);
  }, [setRenaming]);
  const inputRef = useRef<TextInput>(null);
  useEffect(() => {
    inputRef.current?.focus();
  }, [isRenaming]);
  const onRenameLabel = useCallback(
    e => {
      onRename(e.target.value);
    },
    [onRename],
  );
  return (
    <View style={{paddingHorizontal: 5}}>
      <View style={styles.itemContainer}>
        {propEditor?.type === 'editorSectionHeader' && (
          <View style={[styles.rowContainer, {justifyContent: 'space-between', paddingVertical: 0}]}>
            <View style={{flex: 1}}>
              {isRenaming ? (
                <TextInput
                  onBlur={() => setRenaming(false)}
                  ref={inputRef}
                  style={styles.nameInput}
                  defaultValue={label ?? propertyName}
                  blurOnSubmit={true}
                  onSubmitEditing={onRenameLabel}
                />
              ) : (
                <>
                  <PluginPropertyEditor
                    key={pluginConfig?.id + propEditor.name + label}
                    editor={propEditor}
                    entityConfig={pluginConfig}
                    config={pluginConfig?.getIn(propertyUpdatePath)}
                    pageId={pageId}
                    pluginId={pluginId}
                    configPathSelector={selector}
                    inModule={false}
                    isModule
                  />
                </>
              )}
            </View>
            <View style={styles.rowContainer}>
              {!isRenaming && (
                <TouchableOpacity onPress={setEditingName}>
                  <MaterialCommunityIcons name="pencil" size={18} />
                </TouchableOpacity>
              )}
              <TouchableOpacity onPress={onDelete}>
                <MaterialCommunityIcons name="delete-outline" size={18} />
              </TouchableOpacity>
            </View>
          </View>
        )}
        {propEditor?.type !== 'editorSectionHeader' && (
          <CollapsiblePanel
            title={`${label ?? propertyName} : \t${
              pluginId
                ? pluginId.indexOf(NAMESPACE_SPERATOR) < 0
                  ? pluginId
                  : pluginId.slice(pluginId.indexOf(NAMESPACE_SPERATOR) + NAMESPACE_SPERATOR.length)
                : ''
            }`}
            isOpen={false}
            backgroundStyle={{
              borderWidth: 0,
              backgroundColor: '#0000',
              borderRadius: 8,
              overflow: 'hidden',
              marginBottom: 0,
            }}>
            <View style={styles.rowContainer}>
              {isRenaming ? (
                <TextInput
                  onBlur={() => setRenaming(false)}
                  ref={inputRef}
                  style={styles.nameInput}
                  defaultValue={label ?? propertyName}
                  blurOnSubmit={true}
                  onSubmitEditing={onRenameLabel}
                />
              ) : (
                <>
                  <Text style={styles.labelText}>{label ?? propertyName}</Text>
                  <TouchableOpacity onPress={setEditingName}>
                    <MaterialCommunityIcons name="pencil" size={18} />
                  </TouchableOpacity>
                </>
              )}
              <TouchableOpacity onPress={onDelete}>
                <MaterialCommunityIcons name="delete-outline" size={18} />
              </TouchableOpacity>
            </View>
            <PropertyEditorControlSelector editorRecord={propEditorRecord} onChange={onUpdateType} />
            <CheckboxControl
              value={mandatory}
              label="Is This a Mandatory Field ?"
              onChange={function (value: boolean): void {
                onUpdateMandatory(value);
              }}
            />
            <CheckboxControl
              value={advanceProperty}
              label="Is This a Advance Property ?"
              onChange={function (value: boolean): void {
                onUpdateAdvance(value);
              }}
            />
            {advanceProperty && (
              <RadioGroupControl
                value={basePlan}
                label="Base Plan"
                options={Object.keys(allAvailablePlans).map((plan: string) => ({
                  text: allAvailablePlans[plan as Plan],
                  value: plan,
                }))}
                onChange={function (value: string): void {
                  onUpdateBasePlan(value);
                }}
              />
            )}
            <PluginPropertyEditor
              key={pluginConfig?.id + propEditor.name + label}
              editor={propEditor}
              entityConfig={pluginConfig}
              config={pluginConfig?.getIn(propertyUpdatePath)}
              pageId={pageId}
              pluginId={pluginId}
              configPathSelector={selector}
              inModule={false}
              isModule
            />
          </CollapsiblePanel>
        )}
      </View>
    </View>
  );
};

export default ModulePropertyEditor;
