import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import {deleteModuleEvent, renameModuleEvent} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';

import {styles} from './moduleEditorStyles';
import {PIModuleEditorProps} from './moduleEditor';
export interface ModuleEventItemProps extends PIModuleEditorProps {
  moduleUUID: string;
  eventName: string;
  onDeleteEvent: typeof deleteModuleEvent;
  onRenameEvent: typeof renameModuleEvent;
}
const ModuleEventItem: React.FC<ModuleEventItemProps> = props => {
  const [isRenaming, setRenaming] = useState(false);
  const {moduleUUID, eventName, onDeleteEvent, onRenameEvent} = props;

  const onDelete = useCallback(() => {
    onDeleteEvent(moduleUUID, eventName);
  }, [onDeleteEvent, moduleUUID, eventName]);

  const setEditingName = useCallback(() => {
    setRenaming(true);
  }, [setRenaming]);
  const inputRef = useRef<TextInput>(null);
  useEffect(() => {
    inputRef.current?.focus();
  }, [isRenaming]);

  const onRename = useCallback(
    e => {
      onRenameEvent(moduleUUID, eventName, e.target.value);
    },
    [onRenameEvent, moduleUUID, eventName],
  );

  return (
    <View style={styles.itemContainer}>
      <View style={styles.rowContainer}>
        {isRenaming ? (
          <TextInput
            onBlur={() => setRenaming(false)}
            ref={inputRef}
            style={styles.nameInput}
            defaultValue={eventName}
            blurOnSubmit={true}
            onSubmitEditing={onRename}
          />
        ) : (
          <>
            <Text style={styles.labelText}>{eventName}</Text>
            <TouchableOpacity onPress={setEditingName}>
              <MaterialCommunityIcons name="pencil" size={18} />
            </TouchableOpacity>
          </>
        )}
        <TouchableOpacity onPress={onDelete} style={[styles.deleteIcon]}>
          <MaterialCommunityIcons name="delete-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export {ModuleEventItem};
