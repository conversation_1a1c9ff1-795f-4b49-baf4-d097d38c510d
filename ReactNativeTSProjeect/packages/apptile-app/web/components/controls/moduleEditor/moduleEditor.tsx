import {debounce} from 'lodash';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Pressable, Text, Image, TouchableOpacity, View} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {
  moduleVariantUpdate,
  updateModuleProperty as updateModulePropertyValue,
} from '../../../../web/actions/editorActions';
import {
  createModuleEvent,
  createModuleOutput,
  createModuleProperty,
  deleteModuleEvent,
  deleteModuleOutput,
  deleteModuleProperty,
  renameModuleEvent,
  renameModuleOutput,
  renameModuleProperty,
  updateModuleOutput,
  updateModuleProperty,
  persistModuleInputs,
} from 'apptile-core';
import {
  ModuleEditorLocationType,
  ModuleEditorRecord,
  ModulePropertyType,
  ModuleRecord,
  ModuleRecords,
} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {selectModuleByUUID, selectModulesCache} from 'apptile-core';
import {AppDispatch} from '../../../../app/store';
import {EditorProps, PropertyEditorConfig} from '../../../../app/common/EditorControlTypes';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import EventsInspector from '../EventsInspector';
import {getEventsEditorConfig} from 'apptile-core';
import ModulePropertyEditor from './modulePropertyEditor';
import PluginPropertyEditor from '@/root/web/views/propertyInspector/components/PluginPropertyEditor';

import {styles} from './moduleEditorStyles';
import {ModulePropertyItem} from './modulePropertyItem';
import {ModuleOutputItem} from './moduleOutputItem';
import {ModuleEventItem} from './moduleEventItem';
import {
  updateModulePluginProperty,
  updateModuleDefinition,
  remapModuleInstance,
  addModulePluginProperty,
  deleteModulePluginProperty,
  reorderModulePluginProperties,
} from '@/root/web/actions/editorActions';
import ModuleSelectorControl from '../ModuleSelectorControl';
import SortableList, {SortableListItem} from '../../SortableList';
import Immutable from 'immutable';
import {Selector, strsel} from 'apptile-core';
import CollapsiblePanel from '../../CollapsiblePanel';
import CheckboxControl from '../CheckboxControl';
import RadioGroupControl from '../RadioGroupControl';
import {Plan, allAvailablePlans} from 'apptile-core';
import useCallbackRef from '@/root/app/common/utils/useCallBackRef';
import TilesApi from '@/root/web/api/TilesApi';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import VariantSelection from '@/root/web/components-v2/VariantSelection';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

export interface PIModuleEditorProps<ConfigType = 'moduleEditor'> extends EditorProps {
  pluginId: string;
  moduleRecords: ModuleRecords;
  createModuleProperty: typeof createModuleProperty;
  createModuleOutput: typeof createModuleOutput;
  createModuleEvent: typeof createModuleEvent;
  renameModuleProperty: typeof renameModuleProperty;
  renameModuleOutput: typeof renameModuleOutput;
  renameModuleEvent: typeof renameModuleEvent;
  deleteModuleProperty: typeof deleteModuleProperty;
  deleteModuleOutput: typeof deleteModuleOutput;
  deleteModuleEvent: typeof deleteModuleEvent;
  // updateModuleProperty: typeof updateModuleProperty;
  // debouncedUpdateModuleProperty: typeof updateModuleProperty;
  updateModuleOutput: typeof updateModuleOutput;
  debouncedUpdateModuleOutput: typeof updateModuleOutput;
}
const noValue = Symbol('noValue');

const ModuleEditor: React.FC<PIModuleEditorProps> = props => {
  const {
    pluginId,
    propertyEditor,
    pluginSelector,
    pageId,
    appConfig,
    moduleRecords,
    entityConfig,
    pluginLayoutUpdate,
    pluginConfigUpdate,
    debouncedPluginConfigUpdate,
    updateModuleOutput,
    debouncedUpdateModuleOutput,
    createModuleProperty,
    createModuleOutput,
    createModuleEvent,
    renameModuleProperty,
    renameModuleOutput,
    renameModuleEvent,
    deleteModuleProperty,
    deleteModuleOutput,
    deleteModuleEvent,
  } = props;

  const moduleInstanceConfig = entityConfig.get('config');
  const moduleUUID = moduleInstanceConfig.get('moduleUUID');
  const moduleName = moduleInstanceConfig.get('moduleName');
  const childNamespace = moduleInstanceConfig.get('childNamespace');
  const moduleRecord = new ModuleRecord(useSelector(state => selectModuleByUUID(state, moduleUUID)));
  const moduleTriggers = moduleRecord?.get('events', []);

  const dispatch = useDispatch();
  const {
    updateModulePluginProperty: updateModuleEditor,
    updateModuleDefinition: updateModuleDef,
    remapModuleInstance: remapModule,
    addModulePluginProperty: addEditor,
    deleteModulePluginProperty: deleteEditor,
    reorderModulePluginProperties: reorderEditors,
    persistModuleInputs: persistInputs,
  } = useMemo(
    () =>
      bindActionCreators(
        {
          updateModulePluginProperty,
          updateModuleDefinition,
          remapModuleInstance,
          addModulePluginProperty,
          deleteModulePluginProperty,
          reorderModulePluginProperties,
          persistModuleInputs,
        },
        dispatch,
      ),
    [dispatch],
  );

  const [editors, setEditors] = useState(moduleRecord?.get('editors'));
  useEffect(() => {
    if (editors !== moduleRecord?.get('editors')) setEditors(moduleRecord?.get('editors'));
  }, [editors, moduleRecord]);
  const [styleEditors, setStyleEditors] = useState(moduleRecord?.get('styleEditors'));
  useEffect(() => {
    if (styleEditors !== moduleRecord?.get('styleEditors')) setStyleEditors(moduleRecord?.get('styleEditors'));
  }, [styleEditors, moduleRecord]);

  const [basicEditors, setBasicEditors] = useState(moduleRecord?.get('basicEditors'));
  useEffect(() => {
    if (basicEditors !== moduleRecord?.get('basicEditors')) setBasicEditors(moduleRecord?.get('basicEditors'));
  }, [basicEditors, moduleRecord]);

  const saveModule = useCallback(() => {
    updateModuleDef(entityConfig.id, pageId, moduleUUID);
  }, [entityConfig.id, moduleUUID, pageId, updateModuleDef]);

  const addProperty = useCallback(() => {
    let i = 1;
    const propIds = moduleRecord?.get('moduleConfig').keySeq().toArray();
    let newPropId = `Input${i}`;
    while (true) {
      if (!propIds.includes(newPropId)) break;
      i++;
      newPropId = `Input${i}`;
    }
    createModuleProperty(moduleUUID, newPropId, 'property', '');
  }, [createModuleProperty, moduleRecord, moduleUUID]);

  const addOutput = useCallback(() => {
    let i = 1;
    const propIds = moduleRecord?.get('moduleConfig').keySeq().toArray();
    let newOutputId = `Output${i}`;
    while (true) {
      if (!propIds.includes(newOutputId)) break;
      i++;
      newOutputId = `Output${i}`;
    }
    createModuleOutput(moduleUUID, newOutputId, '');
  }, [createModuleOutput, moduleRecord, moduleUUID]);

  const addEvent = useCallback(() => {
    let i = 1;
    const eventNames = moduleRecord?.events;
    let newEventName = `onEvent${i}`;
    while (true) {
      if (!eventNames?.includes(newEventName)) break;
      i++;
      newEventName = `onEvent${i}`;
    }
    createModuleEvent(moduleUUID, newEventName);
  }, [createModuleEvent, moduleRecord, moduleUUID]);

  const addSection = useCallback(
    (location: ModuleEditorLocationType) => {
      let i = 1;
      const editorType = location === 'basic' ? 'basicEditors' :  location === 'style' ? 'styleEditors' : 'editors';
      const editorsMap = moduleRecord?.get(editorType);
      let newSectionName = `Section${i}`;
      while (true) {
        if (!editorsMap?.has(strsel([pluginId, newSectionName]))) break;
        i++;
        newSectionName = `Section${i}`;
      }
      addEditor(
        moduleUUID,
        new ModuleEditorRecord({
          label: newSectionName,
          selector: [pluginId, newSectionName],
          editorType: {
            type: 'editorSectionHeader',
            props: {
              label: newSectionName,
            },
          },
        }),
        location,
      );
    },
    [addEditor, moduleRecord, moduleUUID, pluginId],
  );

  const deleteEditorProperty = useCallback(
    (selector: Selector, location: ModuleEditorLocationType) => {
      deleteEditor(moduleUUID, selector, location);
    },
    [moduleUUID, deleteEditor],
  );

  const renameEditor = useCallback(
    (name: string, location: ModuleEditorLocationType, key: any) => {
      updateModuleEditor(moduleUUID, key, location, {label: name});
    },
    [moduleUUID, updateModuleEditor],
  );
  const updateEditorConfig = useCallback(
    (editorConfig: PropertyEditorConfig<any>, location: ModuleEditorLocationType, key: any) => {
      updateModuleEditor(moduleUUID, key, location, {editorType: editorConfig});
    },
    [moduleUUID, updateModuleEditor],
  );
  const updateFieldConfig = useCallback(
    (value: any, fieldName: string, location: ModuleEditorLocationType, key: any) => {
      updateModuleEditor(moduleUUID, key, location, {[fieldName]: value});
    },
    [moduleUUID, updateModuleEditor],
  );
  const updateEditorsOrder = useCallback(
    (editorList: [string, ModuleEditorRecord][], location: ModuleEditorLocationType) => {
      let keys: string[] = [];
      editorList?.map(([key, val]) => {
        keys.push(key);
      });
      reorderEditors(moduleUUID, location, keys);
    },
    [moduleUUID, reorderEditors],
  );

  const switchModule = useCallback(
    uuid => {
      remapModule(uuid, pluginId, pageId);
    },
    [pageId, pluginId, remapModule],
  );
  const saveModuleInputs = useCallback(
    (val: boolean) => {
      persistInputs(moduleUUID, pageId, pluginId, val);
    },
    [moduleUUID, pageId, persistInputs, pluginId],
  );

  const ModuleEditorItemComponent = useMemo(() => {
    return ({itemVal, itemKey, location}) => {
      return (
        <ModulePropertyEditor
          key={itemKey}
          pageId={pageId}
          parentModuleConfig={entityConfig}
          editorRecord={itemVal}
          pluginConfigUpdate={pluginConfigUpdate}
          debouncedPluginConfigUpdate={debouncedPluginConfigUpdate}
          onRename={val => renameEditor(val, location, itemKey)}
          onDelete={() => deleteEditorProperty(itemVal?.selector, location)}
          onUpdateType={val => updateEditorConfig(val, location, itemKey)}
          onUpdateMandatory={val => updateFieldConfig(val, 'mandatory', location, itemKey)}
          onUpdateAdvance={val => updateFieldConfig(val, 'advanceProperty', location, itemKey)}
          onUpdateBasePlan={val => updateFieldConfig(val, 'basePlan', location, itemKey)}
        />
      );
    };
  }, [debouncedPluginConfigUpdate, entityConfig, pageId, pluginConfigUpdate, renameEditor, updateEditorConfig]);

  return (
    <View style={styles.moduleEditorContainer}>
      <View style={styles.rowContainer}>
        <ModuleSelectorControl value={moduleRecord?.moduleUUID ?? moduleUUID} label="Name" onChange={switchModule} />
        <TouchableOpacity onPress={saveModule}>
          <MaterialCommunityIcons name="content-save-cog-outline" size={18} />
        </TouchableOpacity>
      </View>
      <View style={styles.rowContainer}>
        <Text style={styles.labelText}>Namespace</Text>
        <Text style={styles.valueText}>{childNamespace}</Text>
      </View>
      <VariantSelection key={`${moduleUUID}_Variants`} hidden={true} />
      <View style={styles.rowContainer}>
        <CheckboxControl
          label="Persist Module Inputs"
          value={moduleRecord?.get('persistInputBindings') ?? false}
          onChange={saveModuleInputs}
        />
      </View>
      <View style={styles.rowContainer}>
        <RadioGroupControl
          value={moduleRecord?.isMovable ?? 'CORE'}
          label="Is Movable"
          options={Object.keys(allAvailablePlans).map((plan: string) => ({
            text: allAvailablePlans[plan as Plan],
            value: plan,
          }))}
          onChange={function (value: string): void {
            dispatch(updateModulePropertyValue(moduleUUID, 'isMovable', value));
          }}
        />
      </View>
      <View style={styles.rowContainer}>
        <RadioGroupControl
          value={moduleRecord?.isDeletable ?? 'CORE'}
          label="Is Deletable"
          options={Object.keys(allAvailablePlans).map((plan: string) => ({
            text: allAvailablePlans[plan as Plan],
            value: plan,
          }))}
          onChange={function (value: string): void {
            dispatch(updateModulePropertyValue(moduleUUID, 'isDeletable', value));
          }}
        />
      </View>
      <View style={styles.rowContainer}>
        <CheckboxControl
          value={moduleRecord?.isRootLevelTile ?? false}
          label="Is Root Level Tile ?"
          fullSizeLabel={'true'}
          onChange={function (value: boolean): void {
            dispatch(updateModulePropertyValue(moduleUUID, 'isRootLevelTile', value));
          }}
        />
      </View>
      <View style={styles.rowContainer}>
        <Text style={styles.titleText}>Inputs</Text>
        <TouchableOpacity onPress={addProperty}>
          <MaterialCommunityIcons name="plus-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
      {moduleRecord?.get('inputs').map(input => (
        <ModulePropertyItem
          key={`${moduleUUID}_input_${input}`}
          moduleUUID={moduleUUID}
          propertyName={input}
          propertyValue={moduleInstanceConfig.get(input)}
          propertyType="property"
          onUpdateValue={debouncedPluginConfigUpdate}
          onDeleteProperty={deleteModuleProperty}
          onRenameProperty={renameModuleProperty}
          {...props}
        />
      ))}
      <CollapsiblePanel isOpen={true} title={'Basic'}>
        <View style={styles.rowContainer}>
          <Text style={styles.titleText}>Basic Properties</Text>
          <TouchableOpacity style={styles.rowContainer} onPress={() => addSection('basic')}>
            <Text>Section</Text>
            <MaterialCommunityIcons name="plus-circle-outline" size={18} />
          </TouchableOpacity>
        </View>
        <SortableList
          dragKey={`${pluginId}-basicEditors`}
          data={basicEditors?.entrySeq().toArray()}
          onChange={v => updateEditorsOrder(v, 'basic')}
          itemComponent={ModuleEditorItemComponent}
          componentProps={{location: 'basic'}}
        />
      </CollapsiblePanel>
      <CollapsiblePanel isOpen={true} title={'Content'}>
        <View style={styles.rowContainer}>
          <Text style={styles.titleText}>Content Properties</Text>
          <TouchableOpacity style={styles.rowContainer} onPress={() => addSection('module')}>
            <Text>Section</Text>
            <MaterialCommunityIcons name="plus-circle-outline" size={18} />
          </TouchableOpacity>
        </View>
        <SortableList
          dragKey={`${pluginId}-editors`}
          data={editors?.entrySeq().toArray()}
          onChange={v => updateEditorsOrder(v, 'module')}
          itemComponent={ModuleEditorItemComponent}
          componentProps={{location: 'module'}}
        />
      </CollapsiblePanel>
      <CollapsiblePanel isOpen={true} title={'Style'}>
        <View style={styles.rowContainer}>
          <Text style={styles.titleText}>Style Properties</Text>
          <TouchableOpacity style={styles.rowContainer} onPress={() => addSection('style')}>
            <Text>Section</Text>
            <MaterialCommunityIcons name="plus-circle-outline" size={18} />
          </TouchableOpacity>
        </View>
        <SortableList
          dragKey={`${pluginId}-styleEditors`}
          data={styleEditors?.entrySeq().toArray()}
          onChange={v => updateEditorsOrder(v, 'style')}
          itemComponent={ModuleEditorItemComponent}
          componentProps={{location: 'style'}}
        />
      </CollapsiblePanel>
      <View style={styles.rowContainer}>
        <Text style={styles.titleText}>Outputs</Text>
        <TouchableOpacity onPress={addOutput}>
          <MaterialCommunityIcons name="plus-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
      {moduleRecord?.get('outputs').map(output => {
        const outputModuleconfig = moduleRecord.moduleConfig.get(output);
        return (
          <ModuleOutputItem
            key={`${moduleUUID}_output_${output}`}
            moduleUUID={moduleUUID}
            propertyName={output}
            propertyValue={outputModuleconfig?.config.get('value')}
            onUpdateValue={debouncedUpdateModuleOutput}
            onDeleteOutput={deleteModuleOutput}
            onRenameOutput={renameModuleOutput}
            {...props}
          />
        );
      })}
      <View style={styles.rowContainer}>
        <Text style={styles.titleText}>Events</Text>
        <TouchableOpacity onPress={addEvent}>
          <MaterialCommunityIcons name="plus-circle-outline" size={18} />
        </TouchableOpacity>
      </View>
      {moduleTriggers?.map(trigger => {
        return (
          <ModuleEventItem
            key={`${moduleUUID}_event_${trigger}`}
            moduleUUID={moduleUUID}
            eventName={trigger}
            onDeleteEvent={deleteModuleEvent}
            onRenameEvent={renameModuleEvent}
            {...props}
          />
        );
      })}
      <PluginPropertyEditor
        configPathSelector={[pluginId, 'config', 'events']}
        {...props}
        pluginId={pluginId}
        entityConfig={entityConfig}
        config={entityConfig.config}
        editor={getEventsEditorConfig(moduleTriggers)}
      />
    </View>
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  const debouncedUpdateModuleProperty = debounce(
    (moduleUUID, propertyName, propertyType, value) =>
      dispatch(updateModuleProperty(moduleUUID, propertyName, propertyType, value)),
    450,
  );
  const debouncedUpdateModuleOutput = debounce(
    (moduleUUID, propertyName, value) => dispatch(updateModuleOutput(moduleUUID, propertyName, value)),
    450,
  );
  return {
    ...bindActionCreators(
      {
        createModuleProperty,
        createModuleOutput,
        createModuleEvent,
        updateModuleProperty,
        updateModuleOutput,
        renameModuleProperty,
        renameModuleOutput,
        renameModuleEvent,
        deleteModuleProperty,
        deleteModuleOutput,
        deleteModuleEvent,
      },
      dispatch,
    ),
    debouncedUpdateModuleProperty,
    debouncedUpdateModuleOutput,
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    moduleRecords: selectModulesCache(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ModuleEditor);
