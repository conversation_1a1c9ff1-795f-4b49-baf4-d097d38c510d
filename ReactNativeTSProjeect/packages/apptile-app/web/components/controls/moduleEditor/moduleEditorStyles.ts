import theme from '@/root/web/styles-v2/theme';
import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  moduleEditorContainer: {
    alignContent: 'flex-start',
    flexDirection: 'column',
    flexBasis: 'auto',
  },
  rowContainer: {
    alignContent: 'flex-start',
    alignItems: 'center',
    padding: 5,
    flexDirection: 'row',
    flexBasis: 'auto',
  },
  itemContainer: {
    alignContent: 'flex-start',
    padding: 4,
    flexDirection: 'column',
    flexBasis: 'auto',
    backgroundColor: '#FFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    marginBottom: 4,
  },
  labelText: {
    flex: 1,
    flexGrow: 0,
    flexBasis: 'auto',
    marginRight: 10,
  },
  valueText: {
    flex: 1,
    flexGrow: 1,
  },
  titleText: {
    flex: 1,
    flexBasis: 'auto',
    margin: 10,
    fontSize: 18,
    flexGrow: 0,
  },
  nameInput: {
    flex: 1,
    padding: 4,
    fontSize: 12,
  },
  deleteIcon: {
    alignSelf: 'flex-end',
    flexGrow: 0,
    flexBasis: 'auto',
    marginLeft: 'auto',
  },

  variantsStrip: {
    marginTop: 20,
    marginBottom: 15,
    gap: 10,
  },
  variantsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
    borderColor: theme.INPUT_BORDER,
    borderWidth: 1,
    padding: 5,
    borderRadius: 8,
    backgroundColor: theme.INPUT_BACKGROUND,
    alignItems: 'flex-start',
  },
  variantsPopover: {
    width: 400,
    height: 400,
    marginTop: 60,
    marginRight: 20,
    overflowY: 'scroll',
    paddingVertical: 15,
  },
  variantItem: {
    width: '30%',
    aspectRatio: 1,
    borderRadius: 8,
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    flexDirection: 'column',
  },
  image: {
    width: '100%',
    height: '100%',
    flex: 1,
  },
});

export {styles};
