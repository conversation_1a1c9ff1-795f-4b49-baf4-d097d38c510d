import React, { useEffect, useState } from 'react';
import { Card, Button, Table, Tag, Space, Typography, Alert, Modal, Select } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import moment from 'moment';
import PaddleApi from '../../api/PaddleApi';
import { Subscription, SubscriptionStatus, BillingInterval, PaddlePlan } from '../../api/ApiTypes';

const { Title, Text } = Typography;
const { Option } = Select;

interface SubscriptionManagementProps {
  organizationId: string;
}

const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({ organizationId }) => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [plans, setPlans] = useState<PaddlePlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [selectedInterval, setSelectedInterval] = useState<BillingInterval>(BillingInterval.MONTHLY);

  useEffect(() => {
    fetchSubscriptions();
    fetchPlans();
  }, [organizationId]);

  const fetchSubscriptions = async () => {
    try {
      setLoading(true);
      const response = await PaddleApi.getOrganizationSubscriptions(organizationId);
      setSubscriptions(response.data);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPlans = async () => {
    try {
      const response = await PaddleApi.fetchPlans();
      setPlans(response.data);
    } catch (error) {
      console.error('Error fetching plans:', error);
    }
  };

  const handleCreateSubscription = async () => {
    if (!selectedPlan) return;

    try {
      setLoading(true);
      await PaddleApi.createSubscription({
        organizationId,
        planId: selectedPlan,
        billingInterval: selectedInterval
      });
      setCreateModalVisible(false);
      setSelectedPlan('');
      fetchSubscriptions();
    } catch (error) {
      console.error('Error creating subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async (subscriptionId: string) => {
    try {
      setLoading(true);
      await PaddleApi.cancelSubscription(subscriptionId, true);
      fetchSubscriptions();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return 'green';
      case SubscriptionStatus.PENDING:
        return 'orange';
      case SubscriptionStatus.CANCELLED:
        return 'red';
      case SubscriptionStatus.EXPIRED:
        return 'red';
      case SubscriptionStatus.PAUSED:
        return 'yellow';
      default:
        return 'default';
    }
  };

  const formatTokens = (tokens: number) => {
    return tokens.toLocaleString();
  };

  const getPlanName = (planId: string) => {
    const plan = plans.find(p => p.id === planId);
    return plan?.name || planId;
  };

  const columns = [
    {
      title: 'Plan',
      dataIndex: 'paddlePlanId',
      key: 'plan',
      render: (planId: string) => getPlanName(planId),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: SubscriptionStatus) => (
        <Tag color={getStatusColor(status)}>{status.toUpperCase()}</Tag>
      ),
    },
    {
      title: 'Billing Interval',
      dataIndex: 'billingInterval',
      key: 'billingInterval',
      render: (interval: BillingInterval) => interval.charAt(0).toUpperCase() + interval.slice(1),
    },
    {
      title: 'Token Allocation',
      dataIndex: 'tokenAllocation',
      key: 'tokenAllocation',
      render: (tokens: number) => `${formatTokens(tokens)} tokens`,
    },
    {
      title: 'Current Period',
      key: 'currentPeriod',
      render: (record: Subscription) => (
        <div>
          <div>{moment(record.currentPeriodStart).format('MMM DD, YYYY')}</div>
          <div>to {moment(record.currentPeriodEnd).format('MMM DD, YYYY')}</div>
        </div>
      ),
    },
    {
      title: 'Next Billing',
      dataIndex: 'nextBillingDate',
      key: 'nextBillingDate',
      render: (date: string) => moment(date).format('MMM DD, YYYY'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Subscription) => (
        <Space>
          {record.status === SubscriptionStatus.ACTIVE && !record.cancelAtPeriodEnd && (
            <Button
              size="small"
              danger
              onClick={() => handleCancelSubscription(record.id)}
              loading={loading}
            >
              Cancel
            </Button>
          )}
          {record.cancelAtPeriodEnd && (
            <Text type="secondary">Cancelling at period end</Text>
          )}
        </Space>
      ),
    },
  ];

  const activeSubscriptions = subscriptions.filter(s => s.status === SubscriptionStatus.ACTIVE);
  const hasActiveSubscription = activeSubscriptions.length > 0;

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={3}>Subscription Management</Title>
          <Button
            type="primary"
            onClick={() => setCreateModalVisible(true)}
            disabled={hasActiveSubscription}
          >
            Create Subscription
          </Button>
        </div>

        {hasActiveSubscription && (
          <Alert
            message="Active Subscription"
            description="You have an active subscription. Tokens are automatically allocated based on your billing cycle."
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Table
          columns={columns}
          dataSource={subscriptions}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      <Modal
        title="Create New Subscription"
        visible={createModalVisible}
        onOk={handleCreateSubscription}
        onCancel={() => setCreateModalVisible(false)}
        confirmLoading={loading}
        okButtonProps={{ disabled: !selectedPlan }}
      >
        <div style={{ marginBottom: 16 }}>
          <Text strong>Select Plan:</Text>
          <Select
            style={{ width: '100%', marginTop: 8 }}
            placeholder="Choose a plan"
            value={selectedPlan}
            onChange={setSelectedPlan}
          >
            {plans.map(plan => (
              <Option key={plan.id} value={plan.id}>
                <div>
                  <div>{plan.name}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {plan.custom_data?.tokens && `${formatTokens(parseInt(plan.custom_data.tokens))} tokens`} - 
                    ${plan.unit_price.amount} {plan.unit_price.currency_code}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </div>

        <div>
          <Text strong>Billing Interval:</Text>
          <Select
            style={{ width: '100%', marginTop: 8 }}
            value={selectedInterval}
            onChange={setSelectedInterval}
          >
            <Option value={BillingInterval.MONTHLY}>Monthly</Option>
            <Option value={BillingInterval.YEARLY}>Yearly</Option>
          </Select>
        </div>
      </Modal>
    </div>
  );
};

export default SubscriptionManagement;
