import React, {useEffect, useState} from 'react';
import {Card, Button, Select, Radio, Typography, Space, Alert, Spin} from 'antd';
import {useSelector} from 'react-redux';
import {getPaddleService} from '../../services/PaddleService';
import Paddle<PERSON>pi from '../../api/PaddleApi';
import {PaddlePlan, BillingInterval} from '../../api/ApiTypes';

const {Title, Text, Paragraph} = Typography;
const {Option} = Select;

interface SimplePurchaseFlowProps {
  organizationId: string;
  userId: string;
  userEmail: string;
}

const SimplePurchaseFlow: React.FC<SimplePurchaseFlowProps> = ({organizationId, userId, userEmail}) => {
  const [plans, setPlans] = useState<PaddlePlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [billingInterval, setBillingInterval] = useState<BillingInterval>(BillingInterval.MONTHLY);
  const [loading, setLoading] = useState(false);
  const [paddleInitialized, setPaddleInitialized] = useState(false);

  useEffect(() => {
    initializePaddle();
    fetchPlans();

    // Listen for purchase completion events
    const handlePurchaseCompleted = (event: CustomEvent) => {
      console.log('Purchase completed:', event.detail);
      // Optionally refresh subscription data or redirect
    };

    window.addEventListener('purchaseCompleted', handlePurchaseCompleted as EventListener);

    return () => {
      window.removeEventListener('purchaseCompleted', handlePurchaseCompleted as EventListener);
    };
  }, []);

  const initializePaddle = async () => {
    try {
      // Initialize Paddle with your client token
      const paddleService = getPaddleService('your-paddle-client-token');
      setPaddleInitialized(true);
    } catch (error) {
      console.error('Failed to initialize Paddle:', error);
    }
  };

  const fetchPlans = async () => {
    try {
      setLoading(true);

      // Fetch real plans from backend API
      const response = await PaddleApi.fetchPlans();
      const plansData = response.data || response;

      setPlans(plansData);

      // Auto-select first plan
      if (plansData.length > 0) {
        setSelectedPlan(plansData[0].id);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);

      // Show user-friendly error message
      alert('Failed to load subscription plans. Please refresh the page or contact support.');
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!selectedPlan || !paddleInitialized) return;

    try {
      setLoading(true);
      const paddleService = getPaddleService();

      // Open Paddle checkout
      await paddleService.openCheckout({
        items: [{priceId: selectedPlan, quantity: 1}],
        customer: {email: userEmail, id: userId},
        customData: {
          organizationId,
          userId,
          planId: selectedPlan,
          billingInterval,
        },
      });
    } catch (error) {
      console.error('Error opening checkout:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredPlans = () => {
    return plans.filter(
      plan => plan.billing_cycle.interval === (billingInterval === BillingInterval.MONTHLY ? 'month' : 'year'),
    );
  };

  const getSelectedPlanDetails = () => {
    return plans.find(plan => plan.id === selectedPlan);
  };

  const formatTokens = (tokens: string | number) => {
    const tokenCount = typeof tokens === 'string' ? parseInt(tokens) : tokens;
    return isNaN(tokenCount) ? 'N/A' : tokenCount.toLocaleString();
  };

  const getPlanTokens = (plan: PaddlePlan): string => {
    // Try to get tokens from custom_data
    if (plan.custom_data?.tokens) {
      return plan.custom_data.tokens;
    }

    // Fallback: try to extract from plan name or description
    const nameMatch = plan.name.match(/(\d+(?:,\d+)*)\s*tokens?/i);
    if (nameMatch) {
      return nameMatch[1].replace(/,/g, '');
    }

    const descMatch = plan.description?.match(/(\d+(?:,\d+)*)\s*tokens?/i);
    if (descMatch) {
      return descMatch[1].replace(/,/g, '');
    }

    return '0';
  };

  const getPlanFeatures = (plan: PaddlePlan): string[] => {
    if (plan.custom_data?.features && Array.isArray(plan.custom_data.features)) {
      return plan.custom_data.features;
    }

    // Generate basic features based on plan data
    const features = [];
    const tokens = getPlanTokens(plan);
    if (tokens !== '0') {
      const interval = plan.billing_cycle.interval === 'month' ? 'month' : 'year';
      features.push(`${formatTokens(tokens)} tokens per ${interval}`);
    }

    features.push('API access');
    features.push('Email support');

    if (plan.billing_cycle.interval === 'year') {
      features.push('2 months free');
    }

    return features;
  };

  if (loading && plans.length === 0) {
    return (
      <Card>
        <div style={{textAlign: 'center', padding: '40px'}}>
          <Spin size="large" />
          <div style={{marginTop: 16}}>Loading plans...</div>
        </div>
      </Card>
    );
  }

  const selectedPlanDetails = getSelectedPlanDetails();
  const filteredPlans = getFilteredPlans();

  return (
    <div>
      <Card>
        <Title level={3}>Purchase Token Subscription</Title>
        <Paragraph>
          Choose a plan to get periodic token allocations. Tokens will be automatically added to your organization every
          billing cycle.
        </Paragraph>

        {!paddleInitialized && (
          <Alert
            message="Payment system not initialized"
            description="Please refresh the page or contact support if this persists."
            type="warning"
            style={{marginBottom: 16}}
          />
        )}

        <div style={{marginBottom: 24}}>
          <Text strong>Billing Interval:</Text>
          <Radio.Group
            value={billingInterval}
            onChange={e => setBillingInterval(e.target.value)}
            style={{marginLeft: 16}}>
            <Radio.Button value={BillingInterval.MONTHLY}>Monthly</Radio.Button>
            <Radio.Button value={BillingInterval.YEARLY}>Yearly</Radio.Button>
          </Radio.Group>
        </div>

        <div style={{marginBottom: 24}}>
          <Text strong>Select Plan:</Text>
          <Select
            style={{width: '100%', marginTop: 8}}
            placeholder="Choose a plan"
            value={selectedPlan}
            onChange={setSelectedPlan}>
            {filteredPlans.map(plan => (
              <Option key={plan.id} value={plan.id}>
                <div>
                  <div style={{fontWeight: 'bold'}}>{plan.name}</div>
                  <div style={{fontSize: '12px', color: '#666'}}>
                    {formatTokens(getPlanTokens(plan))} tokens - ${plan.unit_price.amount}{' '}
                    {plan.unit_price.currency_code}
                    {billingInterval === BillingInterval.YEARLY && ' (Save money!)'}
                  </div>
                  {plan.description && (
                    <div style={{fontSize: '11px', color: '#999', marginTop: 2}}>{plan.description}</div>
                  )}
                </div>
              </Option>
            ))}
          </Select>
        </div>

        {selectedPlanDetails && (
          <Card size="small" style={{marginBottom: 24, backgroundColor: '#f9f9f9'}}>
            <Title level={5}>Plan Details</Title>
            <div>
              <Text strong>Name:</Text> {selectedPlanDetails.name}
            </div>
            <div>
              <Text strong>Price:</Text> ${selectedPlanDetails.unit_price.amount}{' '}
              {selectedPlanDetails.unit_price.currency_code}
            </div>
            <div>
              <Text strong>Tokens per {billingInterval === BillingInterval.MONTHLY ? 'month' : 'year'}:</Text>{' '}
              {formatTokens(getPlanTokens(selectedPlanDetails))}
            </div>
            <div>
              <Text strong>Billing:</Text> {billingInterval === BillingInterval.MONTHLY ? 'Monthly' : 'Yearly'}
            </div>
            <div style={{marginTop: 8}}>
              <Text strong>Features:</Text>
              <ul style={{margin: '4px 0', paddingLeft: 20}}>
                {getPlanFeatures(selectedPlanDetails).map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
          </Card>
        )}

        <Alert
          message="How it works"
          description={
            <div>
              <p>1. Complete your purchase through Paddle's secure checkout</p>
              <p>2. Your subscription will be created automatically</p>
              <p>3. Tokens will be allocated to your organization immediately</p>
              <p>
                4. Additional tokens will be added every{' '}
                {billingInterval === BillingInterval.MONTHLY ? 'month' : 'year'}
              </p>
            </div>
          }
          type="info"
          style={{marginBottom: 24}}
        />

        <Space>
          <Button
            type="primary"
            size="large"
            onClick={handlePurchase}
            loading={loading}
            disabled={!selectedPlan || !paddleInitialized}>
            Purchase Subscription
          </Button>
          <Text type="secondary">Secure payment powered by Paddle</Text>
        </Space>
      </Card>
    </div>
  );
};

export default SimplePurchaseFlow;
