// AI-related action types
export const SET_CHAT_RUNNING = 'SET_CHAT_RUNNING';
export const RUN_CHAT_COMPLETION_REQUEST = 'RUN_CHAT_COMPLETION_REQUEST';
export const RUN_CHAT_COMPLETION_SUCCESS = 'RUN_CHAT_COMPLETION_SUCCESS';
export const RUN_CHAT_COMPLETION_FAILURE = 'RUN_CHAT_COMPLETION_FAILURE';
// export const FETCH_CHAT_HISTORY_REQUEST = 'FETCH_CHAT_HISTORY_REQUEST';
// export const FETCH_CHAT_HISTORY_SUCCESS = 'FETCH_CHAT_HISTORY_SUCCESS';
// export const FETCH_CHAT_HISTORY_FAILURE = 'FETCH_CHAT_HISTORY_FAILURE';
// export const SET_CHAT_HISTORY = 'SET_CHAT_HISTORY';
// export const UPDATE_LIVE_MESSAGE = 'UPDATE_LIVE_MESSAGE';
// export const CLEAR_LIVE_MESSAGE = 'C<PERSON>AR_LIVE_MESSAGE';

// Tool-related action types
export const SCREEN_CREATION_DONE = 'SCREEN_CREATION_DONE';
export const PLUGIN_CREATION_DONE = 'PLUGIN_CREATION_DONE';
export const PLUGIN_ADDED_TO_SCREEN_DONE = 'PLUGIN_ADDED_TO_SCREEN_DONE';
export const GLOBAL_STATE_CREATION_DONE = 'GLOBAL_STATE_CREATION_DONE';

export type PromptType = string | {isToolResponse: true; response: string; toolId: string};

// Action interfaces
export interface RunChatCompletionRequestPayload {
  prompt: PromptType;
  model: string;
  provider: 'claude' | 'openai';
  completionUrl: string;
  appId: string;
  usePlanner: boolean;
  liveMessageSubscriber: (contentHtml: string, toolCall: string, callUpdateChatHistory: boolean) => void;
  onCompleted: (error?: any) => void;
}

// Action creators
export const runChatCompletionRequest = (payload: RunChatCompletionRequestPayload) => ({
  type: RUN_CHAT_COMPLETION_REQUEST,
  payload,
});

export const runChatCompletionSuccess = (response: any) => ({
  type: RUN_CHAT_COMPLETION_SUCCESS,
  payload: response,
});

export const runChatCompletionFailure = (error: any) => ({
  type: RUN_CHAT_COMPLETION_FAILURE,
  payload: error,
});

// Tool completion action creators
export const screenCreationDone = (screenName: string) => ({
  type: SCREEN_CREATION_DONE,
  payload: {screenName},
});

export const pluginCreationDone = (pluginName: string) => ({
  type: PLUGIN_CREATION_DONE,
  payload: {pluginName},
});

export const pluginAddedToScreenDone = (pluginName: string, screenName: string) => ({
  type: PLUGIN_ADDED_TO_SCREEN_DONE,
  payload: {pluginName, screenName},
});

export const globalStateCreationDone = (stateName: string, stateValue: any) => ({
  type: GLOBAL_STATE_CREATION_DONE,
  payload: {stateName, stateValue},
});
