import {Toast} from 'apptile-core';

export const MAKE_TOAST = 'MAKE_TOAST';
export const REMOVE_TOAST = 'REMOVE_TOAST';

//////////////////////////////////////
// Action Creators
//////////////////////////////////////

export interface MakeToastWithReference extends Omit<Toast, 'id'> {
  id?: string;
}
export interface MakeToast {
  type: typeof MAKE_TOAST;
  payload: MakeToastWithReference;
}

export const makeToast = (toastObj: MakeToastWithReference): MakeToast => {
  return {
    type: MAKE_TOAST,
    payload: toastObj,
  };
};

export interface RemoveToast {
  type: typeof REMOVE_TOAST;
  payload: string;
}

export const removeToast = (toastIdx: string): RemoveToast => {
  return {
    type: REMOVE_TOAST,
    payload: toastIdx,
  };
};
