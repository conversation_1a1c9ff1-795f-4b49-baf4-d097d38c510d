import {ITypographyItem} from 'apptile-core';
import {IFontRecord} from 'apptile-core';

export const ADD_COLOR = 'ADD_COLOR';
export const UPDATE_COLOR = 'UPDATE_COLOR';
export const REMOVE_COLOR = 'REMOVE_COLOR';

export const ADD_TYPOGRAPHY = 'ADD_TYPOGRAPHY';
export const UPDATE_PLATFORM_TYPOGRAPHY = 'UPDATE_PLATFORM_TYPOGRAPHY';
export const UPDATE_UNIVERSAL_TYPOGRAPHY = 'UPDATE_UNIVERSAL_TYPOGRAPHY';
export const REMOVE_TYPOGRAPHY = 'REMOVE_TYPOGRAPHY';

export const TOGGLE_DARK_MODE = 'TOGGLE_DARK_MODE';
export const TOGGLE_DARK_MODE_SUPPORTED = 'TOGGLE_DARK_MODE_SUPPORTED';

export const ADD_CUSTOM_PROP_THEME = 'ADD_CUSTOM_PROP_THEME';
export const UPDATE_CUSTOM_PROP_THEME = 'UPDATE_CUSTOM_PROP_THEME';
export const REMOVE_CUSTOM_PROP_THEME = 'REMOVE_CUSTOM_PROP_THEME';

export const UPDATE_RICH_TEXT = 'UPDATE_RICH_TEXT';
export const UPDATE_TILE_THEME_CONFIG = 'UPDATE_TILE_THEME_CONFIG';
export const UPDATE_THEME_CONFIG = 'UPDATE_THEME_CONFIG';
export const SET_THEME_CONFIG = 'SET_THEME_CONFIG';
export const DELETE_THEME_CONFIG = 'DELETE_THEME_CONFIG';

export const ADD_FONT = 'ADD_FONT';
export const UPDATE_FONT = 'UPDATE_FONT';
export const DELETE_FONT = 'DELETE_FONT';

type IColor = {
  colorName: string;
  colorCode: string;
};

type ITypography = {
  typographyName: string;
  typographyItem: ITypographyItem;
};

type ITypographyUpdate = {
  typographyName: string;
  typographyItem: Partial<ITypographyItem>;
};

interface IPlatformTypographyUpdate extends ITypographyUpdate {
  platform: 'ios' | 'web' | 'android';
}

//////////////////////////////////////
// Action Creators
//////////////////////////////////////

export interface AddColor {
  type: typeof ADD_COLOR;
  payload: IColor;
}

export const addColor = (colorObj: IColor): AddColor => {
  return {
    type: ADD_COLOR,
    payload: colorObj,
  };
};

interface UpdateColorPayload extends IColor {
  mode: 'light' | 'dark';
}

export interface UpdateColor {
  type: typeof UPDATE_COLOR;
  payload: UpdateColorPayload;
}

export const updateColor = (colorObj: UpdateColorPayload): UpdateColor => {
  return {
    type: UPDATE_COLOR,
    payload: colorObj,
  };
};

export interface RemoveColor {
  type: typeof REMOVE_COLOR;
  payload: string;
}

export const removeColor = (colorName: string): RemoveColor => {
  return {
    type: REMOVE_COLOR,
    payload: colorName,
  };
};

export interface AddTypography {
  type: typeof ADD_TYPOGRAPHY;
  payload: ITypography;
}

export const addTypography = (typographyObj: ITypography): AddTypography => {
  return {
    type: ADD_TYPOGRAPHY,
    payload: typographyObj,
  };
};

export interface UpdatePlatformTypography {
  type: typeof UPDATE_PLATFORM_TYPOGRAPHY;
  payload: IPlatformTypographyUpdate;
}

export const updateTypography = (typographyObj: IPlatformTypographyUpdate): UpdatePlatformTypography => {
  return {
    type: UPDATE_PLATFORM_TYPOGRAPHY,
    payload: typographyObj,
  };
};

export interface UpdateUniversalTypography {
  type: typeof UPDATE_UNIVERSAL_TYPOGRAPHY;
  payload: ITypographyUpdate;
}

export const updateUniversalTypography = (typographyObj: ITypographyUpdate): UpdateUniversalTypography => {
  return {
    type: UPDATE_UNIVERSAL_TYPOGRAPHY,
    payload: typographyObj,
  };
};

export interface RemoveTypography {
  type: typeof REMOVE_TYPOGRAPHY;
  payload: string;
}

export const removeTypography = (typographyName: string): RemoveTypography => {
  return {
    type: REMOVE_TYPOGRAPHY,
    payload: typographyName,
  };
};

export interface ToggleDarkMode {
  type: typeof TOGGLE_DARK_MODE;
}

export const toggleDarkMode = (): ToggleDarkMode => {
  return {
    type: TOGGLE_DARK_MODE,
  };
};

export interface ToggleDarkModeSupported {
  type: typeof TOGGLE_DARK_MODE_SUPPORTED;
}

export const toggleDarkModeSupported = (): ToggleDarkModeSupported => {
  return {
    type: TOGGLE_DARK_MODE_SUPPORTED,
  };
};

type ICustomPropTheme = {
  name: string;
  value: string;
};

export interface AddCustomPropTheme {
  type: typeof ADD_CUSTOM_PROP_THEME;
  payload: ICustomPropTheme;
}

export const addCustomPropTheme = (customObj: ICustomPropTheme): AddCustomPropTheme => {
  return {
    type: ADD_CUSTOM_PROP_THEME,
    payload: customObj,
  };
};

export interface UpdateCustomPropTheme {
  type: typeof UPDATE_CUSTOM_PROP_THEME;
  payload: ICustomPropTheme;
}

export const updateCustomPropTheme = (customObj: ICustomPropTheme): UpdateCustomPropTheme => {
  return {
    type: UPDATE_CUSTOM_PROP_THEME,
    payload: customObj,
  };
};

export interface RemoveCustomPropTheme {
  type: typeof REMOVE_CUSTOM_PROP_THEME;
  payload: string;
}

export const removeCustomPropTheme = (customKey: string): RemoveCustomPropTheme => {
  return {
    type: REMOVE_CUSTOM_PROP_THEME,
    payload: customKey,
  };
};

interface UpdateRichTextPayload {
  tag: string;
  styleName: string;
  styleValue: unknown;
}

export interface UpdateRichText {
  type: typeof UPDATE_RICH_TEXT;
  payload: UpdateRichTextPayload;
}

export const updateRichText = (obj: UpdateRichTextPayload): UpdateRichText => {
  return {
    type: UPDATE_RICH_TEXT,
    payload: obj,
  };
};

interface UpdateThemeConfigPayload {
  selector: string[];
  update: any;
  remove?: string[];
}

export interface UpdateThemeConfig {
  type: typeof UPDATE_THEME_CONFIG;
  payload: UpdateThemeConfigPayload;
}

export const updateThemeConfig = (obj: UpdateThemeConfigPayload): UpdateThemeConfig => {
  return {
    type: UPDATE_THEME_CONFIG,
    payload: obj,
  };
};

interface SetThemeConfigPayload {
  selector: string[];
  value: any;
}

export interface SetThemeConfig {
  type: typeof SET_THEME_CONFIG;
  payload: SetThemeConfigPayload;
}

export const setThemeConfig = (obj: SetThemeConfigPayload): SetThemeConfig => {
  return {
    type: SET_THEME_CONFIG,
    payload: obj,
  };
};

interface DeleteThemeConfigPayload {
  selector: string[];
}

export interface DeleteThemeConfig {
  type: typeof DELETE_THEME_CONFIG;
  payload: DeleteThemeConfigPayload;
}

export const deleteThemeConfig = (obj: DeleteThemeConfigPayload): DeleteThemeConfig => {
  return {
    type: DELETE_THEME_CONFIG,
    payload: obj,
  };
};

interface UpdateTileThemeConfigPayload {
  tileName: string;
  key: string;
  value: unknown;
}

export interface UpdateTileThemeConfig {
  type: typeof UPDATE_TILE_THEME_CONFIG;
  payload: UpdateTileThemeConfigPayload;
}

export const updateTileThemeConfig = (obj: UpdateTileThemeConfigPayload): UpdateTileThemeConfig => {
  return {
    type: UPDATE_TILE_THEME_CONFIG,
    payload: obj,
  };
};

export interface AddFont {
  type: typeof ADD_FONT;
  payload: IFontRecord;
}

export const addFont = (obj: IFontRecord): AddFont => {
  return {
    type: ADD_FONT,
    payload: obj,
  };
};

export interface UpdateFont {
  type: typeof UPDATE_FONT;
  payload: IFontRecord & {id: string};
}

export const updateFont = (obj: IFontRecord & {id: string}): UpdateFont => {
  return {
    type: UPDATE_FONT,
    payload: obj,
  };
};

export interface DeleteFont {
  type: typeof DELETE_FONT;
  payload: string;
}

export const deleteFont = (id: string): DeleteFont => {
  return {
    type: DELETE_FONT,
    payload: id,
  };
};
