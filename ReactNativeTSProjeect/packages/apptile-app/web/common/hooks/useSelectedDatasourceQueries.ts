import {useSelector} from 'react-redux';
import {AppConfig} from 'apptile-core';
import {GetRegisteredPlugin} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {selectSelectedPluginConfig} from '../../selectors/EditorSelectors';

export function useSelectedDatasourceQueries() {
  const selectedQueryPluginConfig = useSelector(selectSelectedPluginConfig);
  const appConfig: AppConfig = useSelector(selectAppConfig);
  const datasourceId = selectedQueryPluginConfig.getIn(['config', 'datasource']) as string;
  if (!datasourceId) return;
  const dsConfig = appConfig.getPlugin(datasourceId);

  if (!dsConfig) return;
  const dsModel = GetRegisteredPlugin(dsConfig.subtype);
  return dsModel?.getQueries();
}

export function useSelectedQueryEditorConfig() {
  const selectedQueryPluginConfig = useSelector(selectSelectedPluginConfig);
  const appConfig: AppConfig = useSelector(selectAppConfig);
  const queryName = selectedQueryPluginConfig.getIn(['config', 'queryName']) as string;
  if (!queryName) return;

  const datasourceId = selectedQueryPluginConfig.getIn(['config', 'datasource']) as string;
  if (!datasourceId) return;

  const dsConfig = appConfig.getPlugin(datasourceId);
  if (!dsConfig) return;

  const dsModel = GetRegisteredPlugin(dsConfig.subtype);
  return dsModel.getQueryInputParams(queryName);
}
