import {useState, useEffect} from 'react';

function getStoredValue<T>(key: string, initialValue: T): T {
  // Try to get the stored value
  const storedValue = localStorage.getItem(key);

  try {
    // If a value exists, return the parsed JSON
    return storedValue ? JSON.parse(storedValue) : initialValue;
  } catch {
    // If parsing fails, return the initial value
    return initialValue;
  }
}

function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => getStoredValue(key, initialValue));

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      // Save to local storage
      localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key) {
        // Update state when the value has changed in a different tab
        if (event.newValue) {
          try {
            setStoredValue(JSON.parse(event.newValue));
          } catch (error) {
            console.error('Failed to parse updated localStorage value:', error);
          }
        } else {
          // If event.newValue is null (item was removed), you might want to
          // reset your state back to the initialValue
        }
      }
    };

    // Listen for storage changes from other tabs
    window.addEventListener('storage', handleStorageChange);

    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]); // Update listener when the key changes

  return [storedValue, setValue];
}

export default useLocalStorage;
