import {useMemo} from 'react';
import {useSelector} from 'react-redux';
import {
  COMMONLY_USED_PLUGIN_TYPES,
  CommonPluginsType,
  PluginListingSection,
  PluginListingSettings,
  PLUGIN_LISTING_SECTIONS,
  RegisteredPlugins,
  resolvePluginListing,
} from 'apptile-core';
import {ApptileStore} from '../../../app/store/RootReducer';

export type PluginListingItem = Omit<PluginListingSettings, 'section'> & {
  section: PluginListingSection | 'Frequently used';
  pluginType: string;
  moduleUUID?: string;
};
export type PluginListingFolder = PluginListingItem[];
type PluginListingFolderFilter = 'hidden' | 'duplicates';
type PluginListingFolderFilters = PluginListingFolderFilter[];

function getPluginListingFolder(...filters: PluginListingFolderFilters) {
  const filterHidden = filters.includes('hidden');
  const filterDuplicates = filters.includes('duplicates');

  const isVisiblePlugin = (plugin?: PluginListingItem): plugin is PluginListingItem =>
    !!plugin && (!filterHidden || !plugin.hidden?.());

  const commonPlugins = COMMONLY_USED_PLUGIN_TYPES.map(pluginType => {
    const pluginListing = resolvePluginListing(pluginType);
    if (!pluginListing) return;
    return {
      ...pluginListing,
      section: 'Frequently used',
      pluginType,
    } as PluginListingItem;
  }).filter(isVisiblePlugin);

  const allPlugins = [...RegisteredPlugins]
    .map(pluginType => {
      const pluginListing = resolvePluginListing(pluginType);
      const isDuplicate = filterDuplicates && COMMONLY_USED_PLUGIN_TYPES.includes(pluginType as CommonPluginsType);
      if (!pluginListing || isDuplicate) return;
      return {
        ...pluginListing,
        pluginType,
      } as PluginListingItem;
    })
    .filter(isVisiblePlugin)
    .sort(({name: nameA, section: sectionA}, {name: nameB, section: sectionB}) => {
      const sectionIndexA = PLUGIN_LISTING_SECTIONS.findIndex(section => section === sectionA);
      const sectionIndexB = PLUGIN_LISTING_SECTIONS.findIndex(section => section === sectionB);
      if (sectionIndexA !== sectionIndexB) {
        return sectionIndexA - sectionIndexB;
      }

      // Widgets are alphabetical within each section
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });
  return [...commonPlugins, ...allPlugins];
}

export default function usePluginListing(...filters: PluginListingFolderFilters): PluginListingFolder {
  const registeredPlugins = useSelector((state: ApptileStore) => state.editor.registeredPlugins);
  return useMemo(
    () => {
      return getPluginListingFolder(...filters);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [registeredPlugins, ...filters],
  );
}
