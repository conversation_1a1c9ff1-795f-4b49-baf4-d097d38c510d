export type AppSizeDimension = {
  width: number;
  height: number;
};

export interface AppSizeDefinition {
  label: string;
  size: AppSizeDimension;
}

export const appSizeDefinitions: Array<AppSizeDefinition> = [
  {label: 'iPhone 12 Pro', size: {height: 844, width: 390}},
  {label: 'iPhone XR', size: {height: 896, width: 414}},
  {label: 'iPhone SE', size: {height: 667, width: 375}},
  {label: 'Samsung Galaxy S20 Ultra', size: {height: 915, width: 412}},
  {label: 'Samsung Galaxy M33', size: {height: 784, width: 384}},
  {label: 'Samsung Galaxy S', size: {height: 533, width: 320}},
  {label: 'Sony Xperia Z', size: {height: 640, width: 360}},
];
