export function createCircularReplacer() {
  // const keys = [];
  const ancestors: any[] = [];
  return function (this: any, key: string, value: any) {
    if (typeof value !== 'object' || value === null) {
      return value;
    }

    while (ancestors.length > 0 && ancestors.at(-1) !== this) {
      ancestors.pop();
      // keys.pop();
    }

    if (ancestors.includes(this)) {
      // console.log("Circlular object: ", keys, ancestors);
      return '[Circular]';
    }

    ancestors.push(value);
    // keys.push(key);
    return value;
  };
}

function fallbackCopyTextToClipboard(text: string) {
  const activeElement = document.activeElement as HTMLElement;
  var textArea = document.createElement('textarea');
  textArea.value = text;

  // Avoid scrolling to bottom
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';

  document.body.appendChild(textArea);
  textArea.focus({preventScroll: true});
  textArea.select();

  try {
    var successful = document.execCommand('copy');
    var msg = successful ? 'successful' : 'unsuccessful';
    logger.info('Fallback: Copying text command was ' + msg);
  } catch (err) {
    logger.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
  activeElement?.focus({preventScroll: true});
}
export function copyToClipboard(text: string) {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  navigator.clipboard.writeText(text).then(
    function () {
      logger.info('Async: Copying to clipboard was successful!');
    },
    function (err) {
      logger.error('Async: Could not copy text: ', err);
    },
  );
}

export function getSubscriptionCurrencySymbol(currencyCode: string) {
  return currencyCode === 'USD' ? '$' : null;
}

export function getSubscriptionStrikeoutPrice(monthlyPrice: number, monthlyDiscountPrice: number) {
  const strikeoutPrice = monthlyDiscountPrice ? monthlyPrice : null;
  const displayPrice = monthlyDiscountPrice ? monthlyPrice - monthlyDiscountPrice : monthlyPrice;
  return {strikeoutPrice, displayPrice};
}
