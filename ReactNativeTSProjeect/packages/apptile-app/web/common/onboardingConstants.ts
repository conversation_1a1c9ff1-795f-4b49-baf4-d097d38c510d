export const themeOnboardingPageId = 'Custom_Theme_Onboarding_2249';
export const themeOnboardingPageKey = 'Custom_Theme_Onboarding_2249';

export const webflowCollectionIds = {
  Themes: '64f977469025f3e474ec18d1',
  Tags: '64f977469025f3e474ec18d2',
  Stats: '64f977469025f3e474ec18d5',
  Integrations: '64f977469025f3e474ec18da',
  Benifits: '64f977469025f3e474ec18d3',
  Preview: '64f977469025f3e474ec18d6',
};
export const WEBFLOW_SITE_ID = '632318d5dd252251b2f9e770';

//For metadata
export const GO_TO_TILES_CLICKED = 'tileButtonClicked';
export const APP_PREVIEW_CLICKED = 'previewClicked';
export const TILE_DROPPED = 'tileDropped';
