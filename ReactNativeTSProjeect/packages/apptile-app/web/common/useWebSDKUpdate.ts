import {useState, useEffect} from 'react';

const useWebSDKUpdate = () => {
  const [, setUpdate] = useState(0);

  useEffect(() => {
    const reloadComponent = () => {
      setUpdate(prev => prev + 1); // Force re-render
    };

    window.addEventListener('webSDKUpdated', reloadComponent);
    return () => {
      window.removeEventListener('webSDKUpdated', reloadComponent);
    };
  }, []);
};

export default useWebSDKUpdate;
