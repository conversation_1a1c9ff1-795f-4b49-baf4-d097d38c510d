import {APPTILE_PREVIEW_SCHEME, APPTILE_API_ENDPOINT} from '../../.env.json';

export default function generatePreviewURL(
  orgId: string | number,
  appId: string,
  forkId: number | string,
  branchName: string,
  appName?: string,
  orgName?: string,
) {
  return `${APPTILE_PREVIEW_SCHEME}://?APP_ID=${appId}&appName=${appName}&orgName=${orgName}&appApi=${APPTILE_API_ENDPOINT}&forkId=${forkId}&branchName=${branchName}`;
}
