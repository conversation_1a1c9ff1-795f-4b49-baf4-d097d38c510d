import {batchCreateModulePluginsImpl} from '../../sagas/editorModuleActionsSaga';
import {PluginNamespaceImpl, PluginConfig, AppConfig, addNamespace} from 'apptile-core';
import Immutable from 'immutable';

export function initModulePlugins(pageId: string, moduleInstanceConfig: PluginConfig, appConfig: AppConfig) {
  const modulesCache = appConfig.get('modules');
  const moduleRecord = modulesCache.get(moduleInstanceConfig.config.get('moduleUUID'));
  const moduleId = moduleInstanceConfig.id;
  const parentNamespace = moduleInstanceConfig.get('namespace') || new PluginNamespaceImpl([]);
  const moduleNamespace = new PluginNamespaceImpl(
    parentNamespace.getNamespace().concat([moduleInstanceConfig.config.get('childNamespace')]),
    moduleId,
  );

  const modulePlugins = moduleRecord?.moduleConfig;
  const modulePluginsToAdd = modulePlugins
    ?.map((modulePlugin, modulePluginId) => {
      let retPlugin = modulePlugin
        .set('id', addNamespace(moduleNamespace, modulePluginId))
        .set('namespace', new PluginNamespaceImpl(moduleNamespace.getNamespace(), modulePluginId));
      if (retPlugin.layout.container === '') {
        retPlugin = retPlugin.setIn(['layout', 'container'], moduleInstanceConfig.id);
      } else {
        retPlugin = retPlugin.setIn(['layout', 'container'], addNamespace(moduleNamespace, retPlugin.layout.container));
      }

      if (retPlugin.subtype === 'ModuleProperty') {
        const parentModuleId = addNamespace(parentNamespace, moduleId);
        retPlugin = retPlugin.setIn(['config', 'value'], `{{${parentModuleId}.${modulePluginId}}}`);
      }
      return retPlugin;
    })
    .mapKeys(modulePluginId => {
      return addNamespace(moduleNamespace, modulePluginId);
    });

  const pageConfig = appConfig.pages.get(pageId);

  let pluginsToAdd = modulePluginsToAdd; //?.filter(plugin => !pageConfig?.plugins.has(plugin.id));
  if (!pluginsToAdd?.count()) {
    return {appConfig, addedPlugins: Immutable.OrderedMap<string, PluginConfig>()};
  } else {
    appConfig = batchCreateModulePluginsImpl(appConfig, pageId, moduleId, pluginsToAdd);
    const subModules = modulePluginsToAdd?.filter(plugin => plugin.subtype === 'ModuleInstance');
    if (subModules?.count()) {
      subModules.forEach(subModuleConfig => {
        let addedPlugins: Immutable.OrderedMap<string, PluginConfig>;
        ({appConfig, addedPlugins} = initModulePlugins(pageId, subModuleConfig, appConfig));
        pluginsToAdd = pluginsToAdd?.merge(addedPlugins);
      });
    }
    return {appConfig, addedPlugins: pluginsToAdd};
  }
}
