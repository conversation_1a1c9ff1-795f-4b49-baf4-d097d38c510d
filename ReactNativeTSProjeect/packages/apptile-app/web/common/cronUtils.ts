class CronUtils {
  getCronPatternFromTimeAndRepeatDayFrequency(triggerTime: Date, repeatDays: number) {
    const triggerHourTime = triggerTime.getUTCHours();
    const triggerMintTime = triggerTime.getUTCMinutes();
    const triggerDayInterval = repeatDays;
    const cronRepeatPattern = `${triggerMintTime} ${triggerHourTime} */${triggerDayInterval} * *`;
    return cronRepeatPattern;
  }

  getRepeatTimeAndDayIntervalFromCron(cronRepeatPattern: string) {
    let triggerTime: Date, triggerDayFrequency: string;
    if (cronRepeatPattern != null) {
      const cronSections = cronRepeatPattern.split(' ');
      const mintInUtc = cronSections[0];
      const hourInUTC = cronSections[1];
      triggerDayFrequency = cronSections[2].split('/')[1];
      triggerTime = new Date();
      triggerTime.setUTCHours(parseInt(hourInUTC));
      triggerTime.setUTCMinutes(parseInt(mintInUtc));
    }
    return {triggerTime, triggerDayFrequency};
  }
}

const cronUtils = new CronUtils();
export default cronUtils;
