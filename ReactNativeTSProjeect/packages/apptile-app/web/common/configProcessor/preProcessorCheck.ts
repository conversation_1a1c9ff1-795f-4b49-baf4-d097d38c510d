const errors = {
  float: `should be numeric`,
  percent: `should be auto / numeric or numeric with percentage`,
  enum: `should be one of these`,
};

const configChecklist: any = {
  borderTopWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderRightWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderBottomWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderLeftWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderStartWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderEndWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  borderWidth: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  flex: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  flexGrow: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  flexShrink: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  aspectRatio: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  zIndex: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  flexDirection: {
    validationType: 'enum',
    enum: ['row', 'column', 'column-reverse', 'row-reverse'],
    error: errors.enum,
  },
  direction: {
    validationType: 'enum',
    enum: ['inherit', 'ltr', 'rtl'],
    error: errors.enum,
  },
  overflow: {
    validationType: 'enum',
    enum: ['visible', 'hidden', 'scroll'],
    error: errors.enum,
  },
  position: {
    validationType: 'enum',
    enum: ['static', 'relative', 'absolute'],
    error: errors.enum,
  },
  top: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  right: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  start: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  end: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  bottom: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  left: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  width: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  height: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  minWidth: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  maxWidth: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  minHeight: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  maxHeight: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginTop: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginRight: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginBottom: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginLeft: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginStart: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginEnd: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginVertical: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  marginHorizontal: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  margin: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingTop: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingRight: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingBottom: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingLeft: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingStart: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingEnd: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingVertical: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  paddingHorizontal: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  padding: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  flexBasis: {
    validationType: 'regex',
    regex: '(^auto$|^[-0-9.]{0,}[%]{0,1}$)',
    error: errors.percent,
  },
  alignItems: {
    validationType: 'enum',
    enum: ['auto', 'flex-start', 'center', 'flex-end', 'stretch', 'baseline', 'space-between', 'space-around'],
    error: errors.enum,
  },
  alignSelf: {
    validationType: 'enum',
    enum: ['auto', 'flex-start', 'center', 'flex-end', 'stretch', 'baseline', 'space-between', 'space-around'],
    error: errors.enum,
  },
  alignContent: {
    validationType: 'enum',
    enum: ['auto', 'flex-start', 'center', 'flex-end', 'stretch', 'baseline', 'space-between', 'space-around'],
    error: errors.enum,
  },
  justifyContent: {
    validationType: 'enum',
    enum: ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'],
    error: errors.enum,
  },
  flexWrap: {
    validationType: 'enum',
    enum: ['nowrap', 'wrap', 'wrap-reverse'],
    error: errors.enum,
  },
  isHighlighted: {
    validationType: 'enum',
    enum: [false, true],
    error: errors.enum,
  },
  adjustsFontSizeToFit: {
    validationType: 'enum',
    enum: [false, true],
    error: errors.enum,
  },
  allowFontScaling: {
    validationType: 'enum',
    enum: [false, true],
    error: errors.enum,
  },
  opacity: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  maxFontSizeMultiplier: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  letterSpacing: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  lineHeight: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  fontSize: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  minimumFontScale: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  textShadowRadius: {
    validationType: 'regex',
    regex: '^[-0-9.]{0,}$',
    error: errors.float,
  },
  textAlign: {
    validationType: 'enum',
    enum: ['auto', 'left', 'center', 'right', 'justify'],
    error: errors.enum,
  },
  resizeMode: {
    validationType: 'enum',
    enum: ['cover', 'contain'],
    error: errors.enum,
  },
};

export const checkConfig = (config: any) => {
  const errors: string[] = [];
  Object.keys(config).map(e => {
    if (configChecklist[e] && config[e] != '' && config[e] != undefined) {
      if (configChecklist[e].validationType == 'regex') {
        if (!new RegExp(configChecklist[e].regex).test(`${config[e]}`))
          errors.push(`${e} ${configChecklist[e].error} only.`);
      } else if (configChecklist[e].validationType == 'enum') {
        if (!configChecklist[e].enum.includes(`${config[e]}`))
          errors.push(`${e} ${configChecklist[e].error} ${configChecklist[e].enum.join(' / ')} only.`);
      }
    }
  });
  return errors;
};
