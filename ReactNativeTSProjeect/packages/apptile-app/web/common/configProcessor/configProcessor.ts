import {AppConfig} from 'apptile-core';
import {processorMap} from './processorMap';

export const configProcessor = (config: AppConfig) => {
  const construct: Record<string, unknown> = {};
  let errors: any = [];

  config
    .toSeq()
    .toMap()
    .forEach((val, key) => {
      if (processorMap[key]) {
        const processedValue = processorMap[key](val, config);
        construct[key] = processedValue.value;
        errors = [...errors, ...processedValue.errors];
      } else {
        construct[key] = val;
      }
    });

  return {value: new AppConfig(construct), errors};
};
