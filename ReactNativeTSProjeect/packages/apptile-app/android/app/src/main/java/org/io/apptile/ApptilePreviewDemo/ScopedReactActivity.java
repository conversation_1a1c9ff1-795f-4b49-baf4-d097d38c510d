package org.io.apptile.ApptilePreviewDemo;

import android.app.Application;
import android.content.Intent;
import android.os.Bundle;
import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.bridge.ReactContext;


public abstract class ScopedReactActivity extends ReactActivity {
    private ReactNativeHost mReactNativeHost;

    @Override
    protected abstract String getMainComponentName();

    protected abstract ReactNativeHost createReactNativeHost(Application app);

    protected ReactActivityDelegate createReactActivityDelegate() {

        return new ReactActivityDelegate(this, getMainComponentName()) {
            @Override
            protected ReactNativeHost getReactNativeHost() {
                if (mReactNativeHost==null) {
                    mReactNativeHost = createReactNativeHost(getApplication());
                }
                return mReactNativeHost;
            }
        };
    }
}
