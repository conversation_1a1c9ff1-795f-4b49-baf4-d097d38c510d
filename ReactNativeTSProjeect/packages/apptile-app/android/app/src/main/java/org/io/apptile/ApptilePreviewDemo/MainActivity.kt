package org.io.apptile.ApptilePreviewDemo

import android.content.Context
import android.content.Intent
import android.hardware.SensorManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
import android.widget.FrameLayout
import android.widget.ImageView

import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts

import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

import com.facebook.react.ReactInstanceManager
import com.facebook.react.bridge.ReactContext
import com.facebook.react.common.ShakeDetector

import com.google.android.gms.tasks.Task
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
/* ForNativesplash (Don't remove)
import com.bumptech.glide.Glide
ForNativesplashEnd */

/* ForCleverTap (Don't remove) import com.clevertap.react.CleverTapModule ForCleverTapEnd */
/* ForCleverTap (Don't remove) import com.clevertap.android.sdk.CleverTapAPI ForCleverTapEnd */

class MainActivity : ReactActivity() {
    private lateinit var mShakeDetector: ShakeDetector
    private var bShakeDetectorStarted = false
    private var savedInstanceState: Bundle? = null
    private lateinit var appUpdateManager: AppUpdateManager

    private var isJSLoaded = false;
    private var isMinSplashDurationPlayed = false;
    val minSplashDuration = 2.0f;
    val maxSplashduration = 20.0f;
    private var nativeSplashView: ImageView? = null;

    companion object {
        private const val TAG = "MainActivity"
    }

    private fun showNativeSplash() {
        // This makes sure the splash image is drawn in the cutout area
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val layoutParams = window.attributes
            layoutParams.layoutInDisplayCutoutMode = LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            window.attributes = layoutParams

            window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        }

        val image: ImageView = ImageView(this.applicationContext);

        this.nativeSplashView = image;
/* ForNativesplash (Don't remove)
        Glide.with(this)
            .load(R.drawable.splash)
            .centerCrop()
            .into(image);
ForNativesplashEnd */

        val frameLayout = FrameLayout(this)

        frameLayout.addView(image)
        val rootFrlayout = this.window.decorView.findViewById<FrameLayout>(android.R.id.content)
        rootFrlayout.addView(frameLayout);

        val minDelayMs = (minSplashDuration * 1000).toLong();
        Handler(Looper.getMainLooper()).postDelayed({
            isMinSplashDurationPlayed = true;
            this.deleteSplashImage();
        }, minDelayMs);

        val maxDelayMs = (maxSplashduration * 1000).toLong();
        Handler(Looper.getMainLooper()).postDelayed({
            isMinSplashDurationPlayed = true;
            isJSLoaded = true;
            this.deleteSplashImage();
        }, maxDelayMs);
    }


    // Called only from javascript side through RNApptile module.
    // This function doesn't actually remove the splash but makes
    // an attempt.
    open fun removeSplash() {
        this.isJSLoaded = true;
        this.deleteSplashImage();
    }

    // Removes the splash image if both javascript thread has asked
    // to remove it and the minimum play duration has passed
    private fun deleteSplashImage() {
        if (this.nativeSplashView != null && this.isMinSplashDurationPlayed && this.isJSLoaded) {
            val view: ImageView = this.nativeSplashView!!;
            if (view.parent != null) {
                val viewGroup: ViewGroup = view.parent as ViewGroup;
                viewGroup.removeView(view);
            }
        }
    }

    val activityResultLauncher: ActivityResultLauncher<IntentSenderRequest> = registerForActivityResult(
        ActivityResultContracts.StartIntentSenderForResult(),
        object : ActivityResultCallback<ActivityResult> {
            override fun onActivityResult(result: ActivityResult) {
                if (result.resultCode != RESULT_OK) {
                    Log.i(TAG, "Update flow failed! Result code: " + result.resultCode)
                }
            }
        }
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(null)
        this.savedInstanceState = savedInstanceState
        /* ForCleverTap (Don't remove) CleverTapModule.setInitialUri(intent.data); ForCleverTapEnd */

        val app = applicationContext as MainApplication

        mShakeDetector = ShakeDetector(object : ShakeDetector.ShakeListener {
            override fun onShake() {
                // Toast.makeText(this@MainActivity, "Don't shake me, bro!", Toast.LENGTH_SHORT).show()
            }
        })

        val intent = intent
        val action = intent.action
        val data = intent.data

        appUpdateManager = AppUpdateManagerFactory.create(app)
        val appUpdateInfoTask: Task<AppUpdateInfo> = appUpdateManager.appUpdateInfo
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)
            ) {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    activityResultLauncher,
                    AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build()
                )
            }
        }

        /* ForNativesplash (Don't remove)
        showNativeSplash();
        ForNativesplashEnd */
    }


    override fun onResume() {
        super.onResume()
        appUpdateManager
            .appUpdateInfo
            .addOnSuccessListener { appUpdateInfo ->
                if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activityResultLauncher,
                        AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build()
                    )
                }
            }
    }

    override fun onStart() {
        super.onStart()


        if (!bShakeDetectorStarted) {
            mShakeDetector.start(applicationContext.getSystemService(Context.SENSOR_SERVICE) as SensorManager)
            bShakeDetectorStarted = true
        }
    }

    // [!IMPORTANT] Keep the spaces after below comment. It is matched in build script with a trailing space.
    /* ForCleverTap (Don't remove)   
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val app = applicationContext as MainApplication

        val extras = intent.extras
        CleverTapAPI.getDefaultInstance(app)?.pushNotificationClickedEvent(extras)
    }
    ForCleverTapEnd */

    /**
     * Returns the name of the main component registered from JavaScript. This is used to schedule
     * rendering of the component.
     */
    override fun getMainComponentName(): String = "Apptile"

    /**
     * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
     * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
     */
    override fun createReactActivityDelegate(): ReactActivityDelegate =
        DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
