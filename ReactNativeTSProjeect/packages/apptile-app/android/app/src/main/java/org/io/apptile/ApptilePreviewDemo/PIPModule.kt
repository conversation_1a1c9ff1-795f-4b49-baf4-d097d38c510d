package org.io.apptile.ApptilePreviewDemo

import android.app.Activity
import android.app.PictureInPictureParams
import android.content.Intent
import android.os.Build
import android.util.Log
import android.util.Rational
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.modules.core.DeviceEventManagerModule

class PIPModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    init {
        instance = this;
    }

    companion object {
        private var instance: PIPModule? = null
        fun getInstance(): PIPModule? {
            return instance;
        }
    }

    fun sendEvent(eventName: String, params: Any?) {
        reactApplicationContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    private var listenerCount = 0

    override fun getName(): String {
        return "PIPModule"
    }

    @ReactMethod
    fun startPIPActivity(promise: Promise) {
        val intent = Intent(reactApplicationContext, PIPActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_NO_ANIMATION
        reactApplicationContext.startActivity(intent)
        promise.resolve(null);
    }

    @ReactMethod
    fun enterPictureInPictureMode(width: Int, height: Int, promise: Promise) {
        val activity: Activity? = currentActivity
        val aspectRatio = Rational(width, height)
        if (activity is PIPActivity) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val params = PictureInPictureParams.Builder()
                    .setAspectRatio(aspectRatio)
//                    Its too much trouble to figure out how to send this from react-native right now
//                    .setSourceRectHint(visibleRect)
//                    .setAutoEnterEnabled(true)
                    .build();
                activity.enterPictureInPictureMode(params)
            } else {
                Log.e("PIPModule", "PIP not supported on this Android version.")
            }
        } else {
            Log.e("PIPModule", "Current activity is not PIPActivity.")
        }
        promise.resolve(null);
    }

    @ReactMethod
    fun addListener(eventName: String) {
        listenerCount += 1
    }

    @ReactMethod
    fun removeListeners(count: Int) {
        listenerCount -= count
    }
}
