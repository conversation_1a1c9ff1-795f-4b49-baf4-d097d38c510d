<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <!-- ForNativesplash (Don't remove) <item name="android:windowBackground">@drawable/splashbitmap</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
         ForNativesplashEnd -->
    </style>
    <style name="Theme.Transparent" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>
