apply plugin: "com.android.application"
apply plugin: 'com.google.gms.google-services'

apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
 
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

// FireworkDependency (Don't remove) apply from: 'firework.gradle'

react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")
 
    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]
 
    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []
 
    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore  (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "org.io.apptile.ApptilePreviewDemo"
    defaultConfig {
        applicationId "org.io.apptile.ApptilePreviewDemo"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        missingDimensionStrategy('react-native-camera','general')
    }

    signingConfigs {
        debug {
            /**
            * # Default config
            * storeFile file('debug.keystore')
            * storePassword 'android'
            * keyAlias 'androiddebugkey'
            * keyPassword 'android'
            */
            String appUploadStoreFile =  System.getenv("SIGNING_STORE_FILE") ?: 'debug.keystore'
            if (appUploadStoreFile) {
                storeFile file(appUploadStoreFile)
                storePassword System.getenv("SIGNING_STORE_PASSWORD") ?: 'android'
                keyAlias System.getenv("SIGNING_KEY_ALIAS") ?: 'androiddebugkey'
                keyPassword System.getenv("SIGNING_KEY_PASSWORD") ?: 'android'
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            /**
            * Caution! In production, you need to generate your own keystore file.
            * see https://reactnative.dev/docs/signed-apk-android.
            */
            signingConfig signingConfigs.debug
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    aaptOptions {
        noCompress "txt", "json"
    }

    packagingOptions {
    // Make sure libjsc.so does not packed in APK
            exclude "**/libjsc.so"
    }

}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation("com.facebook.react:flipper-integration")

    implementation 'com.facebook.fresco:fresco:2.6.0'
    implementation 'com.facebook.fresco:animated-gif:2.6.0'

    implementation("androidx.core:core:1.13.1")
    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation("androidx.lifecycle:lifecycle-process:2.8.3")
    implementation "androidx.core:core-splashscreen:1.0.1"
 
    implementation "androidx.work:work-runtime-ktx:2.9.0"
    
    implementation(platform("com.google.firebase:firebase-bom:33.1.1"))
    implementation "com.google.firebase:firebase-messaging-ktx:24.0.0"
    implementation 'com.google.guava:guava:31.1-android'
    implementation "com.google.android.play:app-update:2.1.0"
    /* ForNativesplash (Don't remove) 
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    ForNativesplashEnd */

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    // PreviewAppRequirement (Don't remove) implementation project(':react-native-permissions')

    // CleverTapDependency (Don't remove) implementation 'com.clevertap.android:clevertap-android-sdk:5.0.0'
    // CleverTapDependency (Don't remove) implementation 'com.android.installreferrer:installreferrer:2.2'

    // OneSignalDependency (Don't remove) implementation 'com.onesignal:OneSignal:[5.0.0, 5.99.99]'
}


apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
