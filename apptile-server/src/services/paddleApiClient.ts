import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from '../apptile-common';
import { AppConfig } from '../config';

interface PaddlePrice {
  id: string;
  description: string;
  product_id: string;
  name: string;
  type: 'standard' | 'custom';
  billing_cycle: {
    interval: 'day' | 'week' | 'month' | 'year';
    frequency: number;
  };
  trial_period?: {
    interval: 'day' | 'week' | 'month' | 'year';
    frequency: number;
  };
  tax_mode: 'account_setting' | 'external' | 'internal';
  unit_price: {
    amount: string;
    currency_code: string;
  };
  unit_price_overrides?: Array<{
    country_codes: string[];
    unit_price: {
      amount: string;
      currency_code: string;
    };
  }>;
  quantity: {
    minimum: number;
    maximum: number;
  };
  status: 'active' | 'archived';
  custom_data?: Record<string, any>;
  import_meta?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface PaddleProduct {
  id: string;
  name: string;
  description?: string;
  type: 'standard' | 'custom';
  tax_category: string;
  image_url?: string;
  custom_data?: Record<string, any>;
  status: 'active' | 'archived';
  import_meta?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface PaddleSubscription {
  id: string;
  status: 'active' | 'canceled' | 'past_due' | 'paused' | 'trialing';
  customer_id: string;
  address_id: string;
  business_id?: string;
  currency_code: string;
  created_at: string;
  updated_at: string;
  started_at: string;
  first_billed_at: string;
  next_billed_at: string;
  paused_at?: string;
  canceled_at?: string;
  discount?: {
    id: string;
    starts_at: string;
    ends_at?: string;
  };
  collection_mode: 'automatic' | 'manual';
  billing_details?: {
    enable_checkout: boolean;
    purchase_order_number?: string;
    additional_information?: string;
    payment_terms: {
      interval: 'day' | 'week' | 'month' | 'year';
      frequency: number;
    };
  };
  current_billing_period?: {
    starts_at: string;
    ends_at: string;
  };
  billing_cycle: {
    interval: 'day' | 'week' | 'month' | 'year';
    frequency: number;
  };
  scheduled_change?: {
    action: 'cancel' | 'pause' | 'resume';
    effective_at: string;
    resume_at?: string;
  };
  items: Array<{
    price: PaddlePrice;
    quantity: number;
    recurring: boolean;
    created_at: string;
    updated_at: string;
    previously_billed_at?: string;
    next_billed_at?: string;
    trial_dates?: {
      starts_at: string;
      ends_at: string;
    };
  }>;
  custom_data?: Record<string, any>;
  import_meta?: Record<string, any>;
}

interface PaddleTransaction {
  id: string;
  status: 'draft' | 'ready' | 'billed' | 'paid' | 'past_due' | 'canceled';
  customer_id?: string;
  address_id?: string;
  business_id?: string;
  custom_data?: Record<string, any>;
  currency_code: string;
  origin: 'api' | 'subscription_charge' | 'subscription_payment_method_change' | 'subscription_update' | 'web';
  subscription_id?: string;
  invoice_id?: string;
  invoice_number?: string;
  collection_mode: 'automatic' | 'manual';
  discount_id?: string;
  billing_details?: {
    enable_checkout: boolean;
    purchase_order_number?: string;
    additional_information?: string;
    payment_terms: {
      interval: 'day' | 'week' | 'month' | 'year';
      frequency: number;
    };
  };
  billing_period?: {
    starts_at: string;
    ends_at: string;
  };
  items: Array<{
    price_id: string;
    price: PaddlePrice;
    quantity: number;
    proration?: {
      rate: string;
      billing_period: {
        starts_at: string;
        ends_at: string;
      };
    };
  }>;
  details: {
    tax_rates_used: Array<{
      tax_rate: string;
      totals: {
        subtotal: string;
        discount: string;
        tax: string;
        total: string;
      };
    }>;
    totals: {
      subtotal: string;
      discount: string;
      tax: string;
      total: string;
      credit: string;
      balance: string;
      grand_total: string;
      fee?: string;
      earnings?: string;
      currency_code: string;
    };
    adjusted_totals?: {
      subtotal: string;
      tax: string;
      total: string;
      grand_total: string;
      fee: string;
      earnings: string;
      currency_code: string;
    };
    payout_totals?: {
      subtotal: string;
      discount: string;
      tax: string;
      total: string;
      credit: string;
      balance: string;
      grand_total: string;
      fee: string;
      earnings: string;
      currency_code: string;
    };
    adjusted_payout_totals?: {
      subtotal: string;
      tax: string;
      total: string;
      grand_total: string;
      fee: string;
      earnings: string;
      currency_code: string;
    };
  };
  payments: Array<{
    amount: string;
    status: 'captured' | 'canceled' | 'error' | 'pending' | 'processing' | 'unknown';
    created_at: string;
    captured_at?: string;
    error_code?: string;
    method_details: {
      card?: {
        type: string;
        last4: string;
        expiry_month: number;
        expiry_year: number;
        cardholder_name?: string;
      };
      type: 'alipay' | 'apple_pay' | 'bancontact' | 'card' | 'google_pay' | 'ideal' | 'paypal' | 'unknown' | 'wire_transfer';
    };
  }>;
  checkout?: {
    url?: string;
  };
  created_at: string;
  updated_at: string;
  billed_at?: string;
}

interface PaddleListResponse<T> {
  data: T[];
  meta: {
    request_id: string;
    pagination: {
      per_page: number;
      next?: string;
      has_more: boolean;
      estimated_total: number;
    };
  };
}

interface PaddleErrorResponse {
  error: {
    type: string;
    code: string;
    detail: string;
    documentation_url?: string;
  };
  meta: {
    request_id: string;
  };
}

/**
 * Production-grade Paddle API client
 */
export class PaddleApiClient {
  private client: AxiosInstance;
  private apiKey: string;
  private environment: 'sandbox' | 'production';

  constructor() {
    this.apiKey = AppConfig.paddle?.apiKey || '';
    this.environment = AppConfig.paddle?.environment || 'sandbox';
    
    if (!this.apiKey) {
      throw new Error('Paddle API key is required');
    }

    const baseURL = this.environment === 'production' 
      ? 'https://api.paddle.com'
      : 'https://sandbox-api.paddle.com';

    this.client = axios.create({
      baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data?.error) {
          const paddleError = error.response.data as PaddleErrorResponse;
          logger.error('Paddle API Error:', {
            type: paddleError.error.type,
            code: paddleError.error.code,
            detail: paddleError.error.detail,
            requestId: paddleError.meta.request_id
          });
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all products
   */
  async getProducts(params?: {
    after?: string;
    per_page?: number;
    status?: 'active' | 'archived';
    id?: string[];
    tax_category?: string;
  }): Promise<PaddleListResponse<PaddleProduct>> {
    try {
      const response: AxiosResponse<PaddleListResponse<PaddleProduct>> = await this.client.get('/products', {
        params
      });
      return response.data;
    } catch (error) {
      logger.error('Error fetching products from Paddle:', error);
      throw error;
    }
  }

  /**
   * Get all prices
   */
  async getPrices(params?: {
    after?: string;
    per_page?: number;
    product_id?: string;
    status?: 'active' | 'archived';
    id?: string[];
    recurring?: boolean;
  }): Promise<PaddleListResponse<PaddlePrice>> {
    try {
      const response: AxiosResponse<PaddleListResponse<PaddlePrice>> = await this.client.get('/prices', {
        params
      });
      return response.data;
    } catch (error) {
      logger.error('Error fetching prices from Paddle:', error);
      throw error;
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(subscriptionId: string, params?: {
    include?: string[];
  }): Promise<PaddleSubscription> {
    try {
      const response: AxiosResponse<{ data: PaddleSubscription }> = await this.client.get(
        `/subscriptions/${subscriptionId}`,
        { params }
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error fetching subscription ${subscriptionId} from Paddle:`, error);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   */
  async getTransaction(transactionId: string, params?: {
    include?: string[];
  }): Promise<PaddleTransaction> {
    try {
      const response: AxiosResponse<{ data: PaddleTransaction }> = await this.client.get(
        `/transactions/${transactionId}`,
        { params }
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error fetching transaction ${transactionId} from Paddle:`, error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string, params: {
    effective_from: 'next_billing_period' | 'immediately';
  }): Promise<PaddleSubscription> {
    try {
      const response: AxiosResponse<{ data: PaddleSubscription }> = await this.client.post(
        `/subscriptions/${subscriptionId}/cancel`,
        params
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error cancelling subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Pause a subscription
   */
  async pauseSubscription(subscriptionId: string, params?: {
    effective_from?: 'next_billing_period' | 'immediately';
    resume_at?: string;
  }): Promise<PaddleSubscription> {
    try {
      const response: AxiosResponse<{ data: PaddleSubscription }> = await this.client.post(
        `/subscriptions/${subscriptionId}/pause`,
        params
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error pausing subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Resume a subscription
   */
  async resumeSubscription(subscriptionId: string, params?: {
    effective_from?: 'next_billing_period' | 'immediately';
  }): Promise<PaddleSubscription> {
    try {
      const response: AxiosResponse<{ data: PaddleSubscription }> = await this.client.post(
        `/subscriptions/${subscriptionId}/resume`,
        params
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error resuming subscription ${subscriptionId}:`, error);
      throw error;
    }
  }
}

// Export types for use in other services
export type {
  PaddlePrice,
  PaddleProduct,
  PaddleSubscription,
  PaddleTransaction,
  PaddleListResponse,
  PaddleErrorResponse
};

// Singleton instance
let paddleApiClient: PaddleApiClient | null = null;

export const getPaddleApiClient = (): PaddleApiClient => {
  if (!paddleApiClient) {
    paddleApiClient = new PaddleApiClient();
  }
  return paddleApiClient;
};

export default PaddleApiClient;
