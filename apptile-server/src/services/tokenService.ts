import { Op } from 'sequelize';
import { logger } from '../apptile-common';
import App from '../models/app';
import AppTokenUsage from '../models/appTokenUsage';
import Organization from '../models/organization';
import OrganizationTokenAllocation, { TokenSource } from '../models/organizationTokenAllocation';
import User from '../models/user';

/**
 * Service for managing token usage and allocation
 */
class TokenService {
  /**
   * Record token usage for an app
   * @param appId The UUID of the app
   * @param userId The ID of the user who used the tokens
   * @param inputTokens Number of tokens in the prompt
   * @param outputTokens Number of tokens in the completion
   * @param llmModel Optional LLM model that was used
   * @returns The created AppTokenUsage record
   */
  async recordTokenUsage(
    appId: string,
    userId: string,
    inputTokens: number,
    outputTokens: number,
    llmModel?: string
  ): Promise<AppTokenUsage> {
    try {
      // First, check if the app exists
      const app = await App.findOne({ where: { uuid: appId } });
      if (!app) {
        throw new Error(`App with ID ${appId} not found`);
      }

      // Check if the user exists
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      // Calculate total tokens
      const totalTokens = inputTokens + outputTokens;

      console.log(`Output tokens are: ${outputTokens}`);

      // Check if the organization has enough tokens
      // const hasEnoughTokens = await this.checkOrganizationHasEnoughTokens(app.organizationId, totalTokens);
      // if (!hasEnoughTokens) {
      //   throw new Error(`Organization does not have enough tokens available`);
      // }

      // Record the token usage
      const tokenUsage = await AppTokenUsage.create({
        appId,
        userId,
        userEmail: user.email,
        llmModel,
        inputTokens,
        outputTokens
      });

      logger.info(`Recorded ${totalTokens} tokens used by user ${user.email} in app ${appId}`);
      return tokenUsage;
    } catch (error) {
      logger.error(`Error recording token usage: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Add tokens to an organization
   * @param organizationId The ID of the organization
   * @param tokenCount Number of tokens to add
   * @param source Source of the tokens
   * @param expiresAt Optional expiration date for the tokens
   * @param notes Optional notes about the token allocation
   * @returns The created OrganizationTokenAllocation record
   */
  async addTokensToOrganization(
    organizationId: string,
    tokenCount: number,
    source: TokenSource,
    expiresAt?: Date,
    notes?: string,
    subscriptionId?: string
  ): Promise<OrganizationTokenAllocation> {
    try {
      // Check if the organization exists
      const organization = await Organization.findByPk(organizationId);
      if (!organization) {
        throw new Error(`Organization with ID ${organizationId} not found`);
      }

      // Add the tokens
      const tokenAllocation = await OrganizationTokenAllocation.create({
        organizationId,
        tokenCount,
        source,
        expiresAt,
        notes,
        subscriptionId
      });

      logger.info(`Added ${tokenCount} tokens to organization ${organizationId} from ${source}`);
      return tokenAllocation;
    } catch (error) {
      logger.error(`Error adding tokens to organization: ${error.message}`, error);
      throw error;
    }
  }

  async getTotalAllocatedTokens(organizationId: string): Promise<number> {
    try {
      // Get all token allocations for the organization that haven't expired
      const currentDate = new Date();
      const tokenAllocations = await OrganizationTokenAllocation.findAll({
        where: {
          organizationId,
          [Op.or]: [
            { expiresAt: { [Op.gt]: currentDate } },
            { expiresAt: null }
          ]
        }
      });

      // Calculate total allocated tokens
      const totalAllocatedTokens = tokenAllocations.reduce(
        (sum, allocation) => sum + allocation.tokenCount,
        0
      );
      return totalAllocatedTokens;
    } catch (error) {
      logger.error(`Error getting total allocated tokens: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Get the total tokens available for an organization
   * @param organizationId The ID of the organization
   * @returns The total number of tokens available
   */
  async getOrganizationAvailableTokens(organizationId: string): Promise<number> {
    try {

      const totalAllocatedTokens = await this.getTotalAllocatedTokens(organizationId);
      // Get all token usage for apps belonging to this organization
      const apps = await App.findAll({
        where: { organizationId }
      });

      const appIds = apps.map(app => app.uuid);

      // Get total used tokens
      const tokenUsages = await AppTokenUsage.findAll({
        where: {
          appId: { [Op.in]: appIds }
        }
      });

      const totalUsedTokens = tokenUsages.reduce(
        (sum, usage) => sum + (usage.inputTokens || 0) + (usage.outputTokens || 0),
        0
      );

      // Calculate available tokens
      const availableTokens = totalAllocatedTokens - totalUsedTokens;
      return availableTokens > 0 ? availableTokens : 0;
    } catch (error) {
      logger.error(`Error getting available tokens: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Check if an organization has enough tokens available
   * @param organizationId The ID of the organization
   * @param requiredTokens Number of tokens required
   * @returns Boolean indicating if the organization has enough tokens
   */
  async checkOrganizationHasEnoughTokens(
    organizationId: string,
    requiredTokens: number
  ): Promise<boolean> {
    const availableTokens = await this.getOrganizationAvailableTokens(organizationId);
    return availableTokens >= requiredTokens;
  }

  /**
   * Get token usage statistics for an organization
   * @param organizationId The ID of the organization
   * @returns Object containing token usage statistics
   */
  async getOrganizationTokenStats(organizationId: string): Promise<{
    totalAllocated: number;
    totalUsed: number;
    available: number;
    usageByApp: { appId: string, appName: string, tokenCount: number }[];
    usageByUser: { userId: string, userEmail: string, tokenCount: number }[];
    detailedUsage: AppTokenUsage[];
    allocationHistory: OrganizationTokenAllocation[];
  }> {
    try {
      // Get all apps for this organization
      const apps = await App.findAll({
        where: { organizationId }
      });

      const appIds = apps.map(app => app.uuid);

      // Get all token usages for these apps
      const allUsages = await AppTokenUsage.findAll({
        where: { appId: { [Op.in]: appIds } },
        order: [['createdAt', 'DESC']]
      });

      // Get token usage by app
      const tokenUsageByApp = await Promise.all(
        apps.map(async (app) => {
          const usages = allUsages.filter(usage => usage.appId === app.uuid);
          const totalUsed = usages.reduce((sum, usage) => sum + (usage.inputTokens || 0) + (usage.outputTokens || 0), 0);

          return {
            appId: app.uuid,
            appName: app.name,
            tokenCount: totalUsed
          };
        })
      );

      // Get token usage by user
      const userMap = new Map<string, { userId: string, userEmail: string, tokenCount: number }>();

      allUsages.forEach(usage => {
        const usageTokens = (usage.inputTokens || 0) + (usage.outputTokens || 0);
        if (userMap.has(usage.userId)) {
          const userData = userMap.get(usage.userId);
          userData.tokenCount += usageTokens;
          userMap.set(usage.userId, userData);
        } else {
          userMap.set(usage.userId, {
            userId: usage.userId,
            userEmail: usage.userEmail,
            tokenCount: usageTokens
          });
        }
      });

      const usageByUser = Array.from(userMap.values()).sort((a, b) => b.tokenCount - a.tokenCount);

      // Get allocation history
      const allocationHistory = await OrganizationTokenAllocation.findAll({
        where: { organizationId },
        order: [['createdAt', 'DESC']]
      });

      // Calculate totals
      const totalAllocated = allocationHistory.reduce(
        (sum, allocation) => sum + allocation.tokenCount,
        0
      );

      const totalUsed = tokenUsageByApp.reduce(
        (sum, app) => sum + app.tokenCount,
        0
      );

      return {
        totalAllocated,
        totalUsed,
        available: totalAllocated - totalUsed,
        usageByApp: tokenUsageByApp,
        usageByUser,
        detailedUsage: allUsages,
        allocationHistory
      };
    } catch (error) {
      logger.error(`Error getting token stats: ${error.message}`, error);
      throw error;
    }
  }
}

export default new TokenService();
