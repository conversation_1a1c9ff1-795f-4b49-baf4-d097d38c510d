import { Op } from 'sequelize';
import { logger } from '../apptile-common';
import Organization from '../models/organization';
import OrganizationTokenAllocation, { TokenSource } from '../models/organizationTokenAllocation';
import Subscription, { SubscriptionStatus, BillingInterval } from '../models/subscription';
import tokenService from './tokenService';
import paddleService from './paddleService';

interface CreateSubscriptionParams {
  organizationId: string;
  planId: string;
  billingInterval: BillingInterval;
}

interface PurchaseData {
  transactionId: string;
  planId: string;
  organizationId: string;
  billingInterval: BillingInterval;
}

/**
 * Service for managing subscriptions and periodic token allocation
 */
class SubscriptionService {
  /**
   * Create a new subscription
   */
  async createSubscription(params: CreateSubscriptionParams): Promise<Subscription> {
    try {
      const { organizationId, planId, billingInterval } = params;

      // Check if organization exists
      const organization = await Organization.findByPk(organizationId);
      if (!organization) {
        throw new Error(`Organization with ID ${organizationId} not found`);
      }

      // Get token allocation for the plan
      const tokenAllocation = await this.getTokenAllocationForPlan(planId);
      if (!tokenAllocation) {
        throw new Error(`Failed to determine token allocation for plan: ${planId}`);
      }

      // Calculate period dates
      const now = new Date();
      const periodEnd = new Date(now);
      if (billingInterval === BillingInterval.MONTHLY) {
        periodEnd.setMonth(periodEnd.getMonth() + 1);
      } else {
        periodEnd.setFullYear(periodEnd.getFullYear() + 1);
      }

      const subscription = await Subscription.create({
        organizationId,
        paddleSubscriptionId: `temp_${Date.now()}`, // Will be updated when Paddle confirms
        paddlePlanId: planId,
        status: SubscriptionStatus.PENDING,
        billingInterval,
        tokenAllocation,
        currentPeriodStart: now,
        currentPeriodEnd: periodEnd,
        nextBillingDate: periodEnd,
        cancelAtPeriodEnd: false
      });

      logger.info(`Created subscription ${subscription.id} for organization ${organizationId}`);
      return subscription;
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Get subscriptions for an organization
   */
  async getOrganizationSubscriptions(organizationId: string): Promise<Subscription[]> {
    try {
      const subscriptions = await Subscription.findAll({
        where: { organizationId },
        order: [['createdAt', 'DESC']]
      });

      return subscriptions;
    } catch (error) {
      logger.error('Error getting organization subscriptions:', error);
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<Subscription> {
    try {
      const subscription = await Subscription.findByPk(subscriptionId);
      if (!subscription) {
        throw new Error(`Subscription with ID ${subscriptionId} not found`);
      }

      subscription.cancelAtPeriodEnd = cancelAtPeriodEnd;
      
      if (!cancelAtPeriodEnd) {
        subscription.status = SubscriptionStatus.CANCELLED;
      }

      await subscription.save();

      logger.info(`Cancelled subscription ${subscriptionId}, cancelAtPeriodEnd: ${cancelAtPeriodEnd}`);
      return subscription;
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  /**
   * Process periodic token allocation for active subscriptions
   */
  async processPeriodicTokenAllocation(): Promise<void> {
    try {
      logger.info('Starting periodic token allocation process');

      // Find subscriptions that need token allocation
      const now = new Date();
      const subscriptions = await Subscription.findAll({
        where: {
          status: SubscriptionStatus.ACTIVE,
          nextBillingDate: {
            [Op.lte]: now
          }
        }
      });

      logger.info(`Found ${subscriptions.length} subscriptions ready for token allocation`);

      for (const subscription of subscriptions) {
        try {
          await this.allocateTokensForSubscription(subscription);
          await this.updateSubscriptionBillingPeriod(subscription);
        } catch (error) {
          logger.error(`Error processing subscription ${subscription.id}:`, error);
          // Continue with other subscriptions
        }
      }

      logger.info('Completed periodic token allocation process');
    } catch (error) {
      logger.error('Error in periodic token allocation:', error);
      throw error;
    }
  }

  /**
   * Allocate tokens for a subscription
   */
  private async allocateTokensForSubscription(subscription: Subscription): Promise<void> {
    try {
      // Calculate expiration date (next billing period + grace period)
      // const expiresAt = new Date(subscription.nextBillingDate);
      // if (subscription.billingInterval === BillingInterval.MONTHLY) {
      //   expiresAt.setMonth(expiresAt.getMonth() + 1);
      // } else {
      //   expiresAt.setFullYear(expiresAt.getFullYear() + 1);
      // }
      // expiresAt.setDate(expiresAt.getDate() + 7); // 7 days grace period

      await tokenService.addTokensToOrganization(
        subscription.organizationId,
        subscription.tokenAllocation,
        TokenSource.SUBSCRIPTION,
        null,
        `Periodic allocation for subscription ${subscription.id} - ${subscription.billingInterval}`,
        subscription.id
      );

      logger.info(`Allocated ${subscription.tokenAllocation} tokens for subscription ${subscription.id}`);
    } catch (error) {
      logger.error(`Error allocating tokens for subscription ${subscription.id}:`, error);
      throw error;
    }
  }

  /**
   * Update subscription billing period
   */
  private async updateSubscriptionBillingPeriod(subscription: Subscription): Promise<void> {
    try {
      const currentPeriodStart = new Date(subscription.nextBillingDate);
      const currentPeriodEnd = new Date(currentPeriodStart);
      const nextBillingDate = new Date(currentPeriodStart);

      if (subscription.billingInterval === BillingInterval.MONTHLY) {
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
      } else {
        currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
        nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
      }

      subscription.currentPeriodStart = currentPeriodStart;
      subscription.currentPeriodEnd = currentPeriodEnd;
      subscription.nextBillingDate = nextBillingDate;

      // Check if subscription should be cancelled
      if (subscription.cancelAtPeriodEnd) {
        subscription.status = SubscriptionStatus.CANCELLED;
      }

      await subscription.save();

      logger.info(`Updated billing period for subscription ${subscription.id}`);
    } catch (error) {
      logger.error(`Error updating billing period for subscription ${subscription.id}:`, error);
      throw error;
    }
  }

  /**
   * Get token allocation for a plan from Paddle custom data
   */
  private async getTokenAllocationForPlan(planId: string): Promise<number> {
    try {
      // Get plan details from Paddle to extract token allocation
      const plans = await paddleService.getPlans();
      const plan = plans.find(p => p.id === planId);

      if (plan?.custom_data?.tokens) {
        const tokens = parseInt(plan.custom_data.tokens);
        if (!isNaN(tokens)) {
          return tokens;
        }
      }

      // IF NO TOKENS FOUND, RETURN DEFAULT
      return null;
    } catch (error) {
      logger.error(`Error getting token allocation for plan ${planId}:`, error);
      return null; // Default fallback
    }
  }

  /**
   * Process a completed purchase and create subscription
   */
  async processPurchase(purchaseData: PurchaseData): Promise<Subscription> {
    try {
      const { transactionId, planId, organizationId, billingInterval } = purchaseData;

      logger.info(`Processing purchase: ${transactionId} for organization: ${organizationId}`);

      // Verify purchase with Paddle API
      const verification = await paddleService.verifyPurchase(transactionId, organizationId);

      if (!verification.isValid) {
        throw new Error(`Purchase verification failed for transaction: ${transactionId}`);
      }

      const { transaction, subscription: paddleSubscription } = verification;

      // Create subscription from purchase
      const subscription = await this.createSubscription({
        organizationId,
        planId,
        billingInterval
      });

      // Update subscription with real Paddle data
      subscription.paddleSubscriptionId = paddleSubscription?.id || transactionId;
      subscription.status = SubscriptionStatus.ACTIVE;

      // Update billing dates from Paddle subscription if available
      if (paddleSubscription) {
        subscription.currentPeriodStart = new Date(paddleSubscription.current_billing_period?.starts_at || paddleSubscription.started_at);
        subscription.currentPeriodEnd = new Date(paddleSubscription.current_billing_period?.ends_at || paddleSubscription.next_billed_at);
        subscription.nextBillingDate = new Date(paddleSubscription.next_billed_at);

        // Store additional metadata
        subscription.metadata = {
          paddleTransactionId: transactionId,
          paddleCustomerId: transaction.customer_id,
          originalPurchaseAmount: transaction.details.totals.total,
          currency: transaction.currency_code
        };
      }

      await subscription.save();

      // Immediately allocate first period tokens
      await this.allocateTokensForSubscription(subscription);

      logger.info(`Purchase processed successfully: ${transactionId}`);
      return subscription;
    } catch (error) {
      logger.error('Error processing purchase:', error);
      throw error;
    }
  }

  // Legacy methods for compatibility
  async addTokensToOrganization(organizationId: string, tokenCount: number, source: string, transactionId: string): Promise<OrganizationTokenAllocation> {
    return await tokenService.addTokensToOrganization(
      organizationId,
      tokenCount,
      source as TokenSource,
      undefined,
      `Transaction: ${transactionId}`
    );
  }

  async getOrganizationCredits(organizationId: string): Promise<{ availableTokens: number }> {
    const availableTokens = await tokenService.getOrganizationAvailableTokens(organizationId);
    return { availableTokens };
  }

  async getCreditHistory(organizationId: string): Promise<OrganizationTokenAllocation[]> {
    return await OrganizationTokenAllocation.findAll({
      where: { organizationId },
      order: [['createdAt', 'DESC']]
    });
  }
}

const subscriptionService = new SubscriptionService();
export default subscriptionService;
