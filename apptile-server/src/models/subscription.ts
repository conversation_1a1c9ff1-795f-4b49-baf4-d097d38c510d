import { DataTypes, Model, Optional } from 'sequelize';
import AppDatabase from '../database';
import Organization from './organization';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  PENDING = 'pending',
  PAUSED = 'paused'
}

export enum BillingInterval {
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

interface SubscriptionAttributes {
  id: string;
  organizationId: string;
  paddleSubscriptionId: string;
  paddlePlanId: string;
  status: SubscriptionStatus;
  billingInterval: BillingInterval;
  tokenAllocation: number;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  nextBillingDate: Date;
  cancelAtPeriodEnd: boolean;
  trialEnd?: Date;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

interface SubscriptionCreationAttributes extends Optional<SubscriptionAttributes, 'id'> {}

export default class Subscription
  extends Model<SubscriptionAttributes, SubscriptionCreationAttributes>
  implements SubscriptionAttributes
{
  declare id: string;
  declare organizationId: string;
  declare paddleSubscriptionId: string;
  declare paddlePlanId: string;
  declare status: SubscriptionStatus;
  declare billingInterval: BillingInterval;
  declare tokenAllocation: number;
  declare currentPeriodStart: Date;
  declare currentPeriodEnd: Date;
  declare nextBillingDate: Date;
  declare cancelAtPeriodEnd: boolean;
  declare trialEnd?: Date;
  declare metadata?: any;

  // timestamps!
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

Subscription.init(
  {
    id: {
      type: DataTypes.UUID,
      allowNull: false,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    organizationId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: Organization,
        key: 'id'
      }
    },
    paddleSubscriptionId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    paddlePlanId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM(...Object.values(SubscriptionStatus)),
      allowNull: false,
      defaultValue: SubscriptionStatus.PENDING
    },
    billingInterval: {
      type: DataTypes.ENUM(...Object.values(BillingInterval)),
      allowNull: false
    },
    tokenAllocation: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Number of tokens allocated per billing period'
    },
    currentPeriodStart: {
      type: DataTypes.DATE,
      allowNull: false
    },
    currentPeriodEnd: {
      type: DataTypes.DATE,
      allowNull: false
    },
    nextBillingDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    cancelAtPeriodEnd: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    trialEnd: {
      type: DataTypes.DATE,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    tableName: 'subscriptions',
    sequelize: AppDatabase.sequelize,
    indexes: [
      {
        fields: ['organizationId']
      },
      {
        fields: ['paddleSubscriptionId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['nextBillingDate']
      }
    ]
  }
);
