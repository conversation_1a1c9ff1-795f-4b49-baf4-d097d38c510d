import { DataTypes, Model, Optional } from 'sequelize';
import AppDatabase from '../database';
import Organization from './organization';

export enum TokenSource {
  PURCHASE = 'purchase',
  FREE_TIER = 'free-tier',
  PROMOTION = 'promotion',
  BONUS = 'bonus',
  REFUND = 'refund',
  ADJUSTMENT = 'adjustment',
  SUBSCRIPTION = 'subscription'
}

interface OrganizationTokenAllocationAttributes {
  id: string;
  organizationId: string;
  tokenCount: number;
  source: TokenSource;
  expiresAt?: Date;
  notes?: string;
  subscriptionId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface OrganizationTokenAllocationCreationAttributes extends Optional<OrganizationTokenAllocationAttributes, 'id'> {}

export default class OrganizationTokenAllocation
  extends Model<OrganizationTokenAllocationAttributes, OrganizationTokenAllocationCreationAttributes>
  implements OrganizationTokenAllocationAttributes
{
  declare id: string;
  declare organizationId: string;
  declare tokenCount: number;
  declare source: TokenSource;
  declare expiresAt?: Date;
  declare notes?: string;
  declare subscriptionId?: string;

  // timestamps!
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

OrganizationTokenAllocation.init(
  {
    id: {
      type: DataTypes.UUID,
      allowNull: false,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    organizationId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: Organization,
        key: 'id'
      }
    },
    tokenCount: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    source: {
      type: DataTypes.ENUM(...Object.values(TokenSource)),
      allowNull: false
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    notes: {
      type: DataTypes.STRING,
      allowNull: true
    },
    subscriptionId: {
      type: DataTypes.UUID,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  },
  {
    tableName: 'organization_token_allocations',
    sequelize: AppDatabase.sequelize
  }
);
