import App from './app';
import AppSave from './appSave';
import AppFork from './appFork';
import AppCommit from './appCommit';
import AppBranch from './appBranch';

import AppIntegration from './appIntegrations';
import Organization from './organization';
import Template from './template';
import TemplateSave from './templateSaves';
import User from './user';
import UserOrganization from './userOrganization';
import Integration from './integrations';
import TileSave from './tileSave';
import Tile from './tile';
import PageSave from './pageSave';
import Page from './page';
import BlueprintSave from './blueprintSave';
import Blueprint from './blueprint';
import ScheduledOta from './scheduledOtas';
import AppSnapshot from './appSnapshot';
import CodeArtefacts from './codeArtefacts';
import WebSDKLiveArtifacts from './webSDKLiveArtifacts';
import WebSDKArtifacts from './webSDKArtifacts';
import AppTokenUsage from './appTokenUsage';
import OrganizationTokenAllocation from './organizationTokenAllocation';
import Subscription from './subscription';

export function dbInit() {
  // Here we associate which actually populates out pre-declared `association` static and other methods.

  AppSave.belongsTo(App, {
    foreignKey: 'appId',
    as: 'app'
  });
  AppFork.belongsTo(App, {
    foreignKey: 'appId',
    as: 'app'
  });
  //Associations between ScheduledOta and AppFork start
  AppFork.hasMany(ScheduledOta, {
    foreignKey: 'forkId',
    as: 'scheduledOtas'
  });
  ScheduledOta.belongsTo(AppFork, {
    foreignKey: 'forkId',
    as: 'fork'
  });
  //end
  //Associations between AppCommit and User start
  AppCommit.belongsTo(User, {
    foreignKey: 'updatedBy',
    as: 'user'
  });
  User.hasMany(AppCommit, {
    foreignKey: 'updatedBy',
    as: 'AppCommits'
  });
  //end
  //Associations between snapshots and ScheduledOta start
  ScheduledOta.belongsTo(AppSnapshot, {
    foreignKey: 'snapshotId',
    as: 'snapshot'
  });
  AppSnapshot.hasMany(ScheduledOta, {
    foreignKey: 'snapshotId',
    as: 'scheduledOtas'
  });
  //end
  //Associations between Users and Snapshot start
  AppSnapshot.belongsTo(User, {
    foreignKey: 'createdBy',
    as: 'user'
  });
  User.hasMany(AppSnapshot, {
    foreignKey: 'createdBy',
    as: 'snapshots'
  });
  //end
  //Associations between otaSchedulr and AppBranch start
  ScheduledOta.belongsTo(AppBranch, {
    foreignKey: 'branchId',
    as: 'branch'
  });
  AppBranch.hasMany(ScheduledOta, {
    foreignKey: 'branchId',
    as: 'scheduledOtas'
  });
  //end
  // Association between Appsnapshot and appbranch
  AppSnapshot.belongsTo(AppBranch, {
    foreignKey: 'branchId',
    as: 'branch'
  });
  AppBranch.hasMany(AppSnapshot, {
    foreignKey: 'branchId',
    as: 'snapshots'
  });
  //end

  App.hasMany(AppFork, {
    foreignKey: 'appId',
    as: 'forks'
  });
  AppCommit.belongsTo(AppFork, {
    foreignKey: 'forkId',
    as: 'fork'
  });
  AppBranch.belongsTo(AppFork, {
    foreignKey: 'forkId',
    as: 'fork'
  });
  AppFork.hasMany(AppBranch, {
    foreignKey: 'forkId',
    as: 'branches'
  })

  // Sequelize does not have a contains relationship, but belongs to replicates the same.
  AppBranch.belongsTo(AppCommit, {
    foreignKey: 'headCommitId',
    as: 'headCommit'
  });
  AppBranch.belongsTo(AppCommit, {
    foreignKey: 'activePublishedCommitId',
    as: 'activePublishedCommit'
  });

  TileSave.belongsTo(Tile, {
    foreignKey: 'tileId',
    as: 'tile'
  });

  Tile.belongsTo(TileSave, {
    foreignKey: 'currentTileSaveId',
    as: 'currentSavedVersion'
  });

  PageSave.belongsTo(Page, {
    foreignKey: 'pageId',
    as: 'page'
  });

  Page.belongsTo(PageSave, {
    foreignKey: 'currentPageSaveId',
    as: 'currentSavedVersion'
  });

  BlueprintSave.belongsTo(Blueprint, {
    foreignKey: 'blueprintId',
    as: 'blueprint'
  });

  Blueprint.belongsTo(BlueprintSave, {
    foreignKey: 'currentBlueprintSaveId',
    as: 'currentSavedVersion'
  });

  Organization.belongsToMany(User, {
    through: UserOrganization,
    foreignKey: 'organizationId',
    as: 'users',
    otherKey: 'userId'
  });
  User.belongsToMany(Organization, {
    through: UserOrganization,
    foreignKey: 'userId',
    otherKey: 'organizationId',
    as: 'organizations'
  });

  UserOrganization.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  UserOrganization.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user'
  });

  App.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  Organization.hasMany(App, {
    sourceKey: 'id',
    foreignKey: 'organizationId',
    as: 'apps'
  });

  TemplateSave.belongsTo(Template, {
    foreignKey: 'templateId',
    as: 'template'
  });

  Template.belongsTo(TemplateSave, {
    foreignKey: 'currentTemplateSaveId',
    as: 'currentTemplateSave'
  });

  Template.hasMany(TemplateSave, {
    sourceKey: 'id',
    foreignKey: 'templateId',
    as: 'templateSaves'
  });

  Template.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  Organization.hasMany(Template, {
    sourceKey: 'id',
    foreignKey: 'organizationId',
    as: 'templates'
  });

  AppIntegration.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  Organization.hasMany(AppIntegration, {
    sourceKey: 'id',
    foreignKey: 'organizationId',
    as: 'integration'
  });

  User.hasOne(Organization, {
    sourceKey: 'id',
    foreignKey: 'ownerId',
    as: 'owner'
  });

  Organization.belongsTo(User, {
    foreignKey: 'ownerId',
    as: 'owner'
  });

  Integration.hasMany(AppIntegration, {
    sourceKey: 'integrationCode',
    foreignKey: 'platformType',
    as: 'appIntegrations'
  });

  App.hasMany(CodeArtefacts, {foreignKey: 'appId', as: 'codeArtefacts'});

  //For web sdk artifacts
  WebSDKLiveArtifacts.belongsTo(WebSDKArtifacts, {
    foreignKey: 'artifactId',
    as: 'artifact'
  });

  // Token usage tracking associations
  AppTokenUsage.belongsTo(App, {
    foreignKey: 'appId',
    as: 'app'
  });

  App.hasMany(AppTokenUsage, {
    foreignKey: 'appId',
    as: 'tokenUsages'
  });

  AppTokenUsage.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user'
  });

  User.hasMany(AppTokenUsage, {
    foreignKey: 'userId',
    as: 'tokenUsages'
  });

  OrganizationTokenAllocation.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  Organization.hasMany(OrganizationTokenAllocation, {
    foreignKey: 'organizationId',
    as: 'tokenAllocations'
  });

  // Subscription associations
  Subscription.belongsTo(Organization, {
    foreignKey: 'organizationId',
    as: 'organization'
  });

  Organization.hasMany(Subscription, {
    foreignKey: 'organizationId',
    as: 'subscriptions'
  });

  OrganizationTokenAllocation.belongsTo(Subscription, {
    foreignKey: 'subscriptionId',
    as: 'subscription'
  });

  Subscription.hasMany(OrganizationTokenAllocation, {
    foreignKey: 'subscriptionId',
    as: 'tokenAllocations'
  });
}
