import * as Sentry from '@sentry/node';
import {ProfilingIntegration} from '@sentry/profiling-node';
import cookieParser from 'cookie-parser';
import express from 'express';
import path from 'path';
// import path from 'path';
import Shopify, {ApiVersion} from '@shopify/shopify-api';
import proxy from 'express-http-proxy';
import {AppConfig} from './config';
import database from './database';
import {setContentSecurityMiddleware} from './middlewares/setContentPolicyMiddleware';
import {dbInit} from './models';
import adminRouter from './routes/adminApi';
import ApiRouter from './routes/ApiRoutes';
import GdprWebhooksRouter from './routes/shopify/GdprWebhookRouter';
import RefreshTokenRouter from './routes/shopify/RefreshTokenRouter';
import ShopifyController from './routes/shopify/ShopifyRouter';
import PartnerRouter from './routes/PartnerRouter';
import verifyShopifyRequest from './middlewares/verifyShopifyRequest';
import {authenticated} from './middlewares/auth';
import {protectNonAuthorizedBuildAccess, internalBuildAuth} from './middlewares/gaurds/buildauth';
import {setShopAndOrgContext} from './middlewares/setShopAndOrgContext';
import {PostgreSQLSessionStorage} from './helpers/sessionStorage/postgresql';
import {checkOrgAppAccess} from './middlewares/checkOrgAppAccess';
import {defaultErrorHandler, httpRequestTracer, logger, requestLogger} from './apptile-common';
import scheduledJobService from './services/scheduledJobService';
import InstagramController from './routes/instagram/InstagramRouter';
import {verifyNotificationRequest} from './middlewares/verifyNotificationRequest';
import {setIntegrationsData} from './middlewares/setIntegrationsData';
import promBundle from 'express-prom-bundle';
import {setInternalShopAndOrgContext} from './middlewares/setInternalShopAndOrgContext';
import {verifyBuildManager} from './middlewares/verfiyBuildManager';
import { IPlatformType } from './types/types';

const ApiKey = AppConfig.shopifyApp.apiKey;
const ApiSecretKey = AppConfig.shopifyApp.apiSecretKey;
const ApiHost = AppConfig.shopifyApp.ApiHost;
const Scope = AppConfig.shopifyApp.Scope;
const SentryTracesSampleRate = AppConfig.sentry.tracesSampleRate || 0.0;
const SentryProfilesSampleRate = AppConfig.sentry.profilesSampleRate || 0.0;

Shopify.Context.initialize({
  API_KEY: ApiKey,
  API_SECRET_KEY: ApiSecretKey,
  SCOPES: Scope,
  HOST_NAME: ApiHost.replace(/https:\/\//, ''),
  API_VERSION: ApiVersion.October22,
  IS_EMBEDDED_APP: true,
  // We are using Custom PostgreSQLSessionStorage cause @shopify/shopify-api SDK postgress Session Storage have some bugs
  // it throws an error ERROR:  value too long for type character varying(255). we changed it to TEXT to make compatible to save large accessTokens and scopes.
  SESSION_STORAGE: PostgreSQLSessionStorage.withCredentials(
    AppConfig.db.main.host,
    AppConfig.db.main.database,
    AppConfig.db.main.user,
    AppConfig.db.main.password,
    {
      port: 5432,
      sessionTableName: AppConfig.app.sessionTableName
    }
  )
});

const main = async () => {
  database.connect(AppConfig.db.main);
  dbInit();

  const app = express();

  if (process.env.ENABLE_SENTRY) {
    Sentry.init({
      dsn: 'https://<EMAIL>/4505986081030144',
      integrations: [
        new Sentry.Integrations.Http({tracing: true}),
        new Sentry.Integrations.Express({app}),
        new ProfilingIntegration()
      ],
      tracesSampleRate: SentryTracesSampleRate,
      profilesSampleRate: SentryProfilesSampleRate
    });

    app.use(Sentry.Handlers.requestHandler());
    app.use(Sentry.Handlers.tracingHandler());
  }

  if (process.env.ENABLE_PROMETHEUS) {
    const metricsMiddleware = promBundle({
      includeMethod: true,
      includePath: true,
      metricsApp: app,
      promClient: {
        collectDefaultMetrics: {
          eventLoopMonitoringPrecision: 10,
          gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5]
        }
      }
    });
    app.use(metricsMiddleware);
  }

  app.use(httpRequestTracer);
  app.use(requestLogger);

  app.use(cookieParser(AppConfig.app.secret));

  app.get('/health', (req, res) => {
    res.status(200).json({status: 'healthy'});
  });

  app.use('/api', ApiRouter);

  app.use('/shopify/gdpr', GdprWebhooksRouter);
  app.use('/shopify', new ShopifyController().router);
  app.use('/instagram', new InstagramController().router);
  app.use('/admin/api', RefreshTokenRouter);
  app.use('/admin/api', adminRouter);
  app.use('/partner/api', PartnerRouter);

  //setting Content Security headers
  app.use(setContentSecurityMiddleware);

  const userRegistryConfig = AppConfig.userRegistryService;
  const oneSignalProxy = AppConfig.oneSignalProxy;

  app.use(
    '/registry',
    proxy(
      `${userRegistryConfig.protocol}://${userRegistryConfig.domainName}${
        userRegistryConfig.port ? ':' + userRegistryConfig.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  const pushNotificationConfig = AppConfig.pushNotificationService;
  app.use(
    '/notifications',
    proxy(
      `${pushNotificationConfig.protocol}://${pushNotificationConfig.domainName}${
        pushNotificationConfig.port ? ':' + pushNotificationConfig.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  // TODO: Protect API by adding Token
  // TODO: Protect API by adding rate Limit
  app.use(
    '/notification-test',
    proxy(oneSignalProxy.apiBaseUrl, {
      proxyReqPathResolver: function (req) {
        return new Promise(function (resolve, reject) {
          var parts = req.url.split('?');
          var queryString = parts[1];
          var updatedPath = '/api/v1/notification-test';
          var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
          resolve(resolvedPathValue);
        });
      }
    })
  );

  const shopifyShopManager = AppConfig.shopifyShopManagerService;
  const shoplazzaShopManager = AppConfig.shoplazzaShopManagerService;
  // TODO: Remove this route as all shopify-shop-manager routes will be handled via the next routing.
  app.use(
    '/api/subscription/addOn',
    verifyShopifyRequest,
    authenticated,
    setShopAndOrgContext,
    express.json(),
    setIntegrationsData,
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/subscription/addOn' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );
  app.use(
    '/api/internal/subscription',
    verifyShopifyRequest,
    express.json(),
    setInternalShopAndOrgContext,
    setIntegrationsData,
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/subscription' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  const getTargetUrl = (sourcePlatformType: IPlatformType) => {
    switch (sourcePlatformType) {
      case IPlatformType.SHOPLAZZA:
        return `${shoplazzaShopManager.protocol}://${shoplazzaShopManager.domainName}${shoplazzaShopManager.port ? ':' + shoplazzaShopManager.port : ''}`
      default:
        return `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${shopifyShopManager.port ? ':' + shopifyShopManager.port : ''}`;
    }
  }

  const getUpdatedPath = (sourcePlatformType: IPlatformType, parts: string[]) => {
        switch (sourcePlatformType) {
      case IPlatformType.SHOPLAZZA:
        return '/api/billing' + parts[0];
      default:
        return '/api/v1/subscription' + parts[0];
    } 
  }

  app.use(
    '/api/subscription',
    verifyShopifyRequest,
    authenticated,
    setShopAndOrgContext,
    (req, res, next) => {
      // Check for the query parameter and update the target URL dynamically
      const queryParams = new URLSearchParams(req.url.split('?')[1]);
      const sourcePlatformType = queryParams.get('platformType') as IPlatformType;
      const targetUrl = getTargetUrl(sourcePlatformType);

      proxy(targetUrl, {
        proxyReqPathResolver: (req) => {
          return new Promise((resolve) => {
            const parts = req.url.split('?');
            const queryString = parts[1];
            const updatedPath = getUpdatedPath(sourcePlatformType, parts);
            const resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      })(req, res, next);
   }
  );

  app.use(
    '/api/discount',
    verifyShopifyRequest,
    authenticated,
    setShopAndOrgContext,
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/discount' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  app.use(
    '/api/billing',
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/shopify' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  app.use(
    '/api/bulk',
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/bulk-query' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );
  app.use(
    '/shopify-shop-manager/customers',
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/customers' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  app.use(
    '/shopify-shop-manager/brand',
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1/brand' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  app.use(
    '/shopify-shop-manager',
    verifyShopifyRequest,
    authenticated,
    setShopAndOrgContext,
    proxy(
      `${shopifyShopManager.protocol}://${shopifyShopManager.domainName}${
        shopifyShopManager.port ? ':' + shopifyShopManager.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  const userCommunicationService = AppConfig.userCommunicationService;
  app.use(
    '/user-communication',
    verifyShopifyRequest,
    authenticated,
    setShopAndOrgContext,
    checkOrgAppAccess,
    proxy(
      `${userCommunicationService.protocol}://${userCommunicationService.domainName}${
        userCommunicationService.port ? ':' + userCommunicationService.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  // This route is a custom proxy to handle the notification from external services
  // This forwards the request to user-communication-service
  app.use(
    '/notification-server',
    verifyNotificationRequest,
    proxy(
      `${userCommunicationService.protocol}://${userCommunicationService.domainName}${
        userCommunicationService.port ? ':' + userCommunicationService.port : ''
      }`,
      {
        proxyReqPathResolver: function (req) {
          return new Promise(function (resolve, reject) {
            var parts = req.url.split('?');
            var queryString = parts[1];
            var updatedPath = '/api/v1' + parts[0];
            var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
            resolve(resolvedPathValue);
          });
        }
      }
    )
  );

  app.use(
    '/one-signal-proxy',
    verifyShopifyRequest,
    authenticated,
    proxy(oneSignalProxy.apiBaseUrl, {
      proxyReqPathResolver: function (req) {
        return new Promise(function (resolve, reject) {
          var parts = req.url.split('?');
          var queryString = parts[1];
          var updatedPath = '/api/v1' + parts[0];
          var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
          resolve(resolvedPathValue);
        });
      }
    })
  );
  app.use(
    '/one-signal-build',
    verifyBuildManager,
    proxy(oneSignalProxy.apiBaseUrl, {
      proxyReqPathResolver: function (req) {
        return new Promise(function (resolve, reject) {
          var parts = req.url.split('?');
          var queryString = parts[1];
          var updatedPath = '/api/v1' + parts[0];
          var resolvedPathValue = updatedPath + (queryString ? '?' + queryString : '');
          resolve(resolvedPathValue);
        });
      }
    })
  );

  const buildManagerProxy = AppConfig.buildManagerProxyPath;

  app.use(
    '/build-system/',
    verifyShopifyRequest,
    authenticated,
    protectNonAuthorizedBuildAccess,
    proxy(buildManagerProxy, {
      proxyReqPathResolver: function (req) {
        return new Promise(function (resolve, reject) {
          // Remove /build-manager-internal from the URL path
          var updatedPath = '/build-manager' + req.url;

          var parts = updatedPath.split('?');
          var queryString = parts[1];
          var resolvedPathValue = parts[0] + (queryString ? '?' + queryString : '');
          console.log(resolvedPathValue);
          resolve(resolvedPathValue);
        });
      }
    })
  );

  app.use(
    '/internal-build-system/',
    internalBuildAuth,
    proxy(buildManagerProxy, {
      proxyReqPathResolver: function (req) {
        return new Promise(function (resolve, reject) {
          // Remove /build-manager-internal from the URL path
          var updatedPath = '/build-manager' + req.url;

          var parts = updatedPath.split('?');
          var queryString = parts[1];
          var resolvedPathValue = parts[0] + (queryString ? '?' + queryString : '');
          console.log(resolvedPathValue);
          resolve(resolvedPathValue);
        });
      }
    })
  );

  app.use(
    express.static(path.join(__dirname, '../dist'), {
      index: false,
      maxAge: '30d'
    })
  );

  if (process.env.ENABLE_SENTRY) {
    app.use(Sentry.Handlers.errorHandler());
  }
  app.use(defaultErrorHandler);

  app.listen(AppConfig.app.port, () => {
    // tslint:disable-next-line: no-console
    logger.debug(`Listening on port ${AppConfig.app.port}`);

    // Initialize scheduled jobs
    scheduledJobService.init();
  });
};

main().catch((err) => {
  // tslint:disable-next-line: no-console
  logger.error(err);
});
