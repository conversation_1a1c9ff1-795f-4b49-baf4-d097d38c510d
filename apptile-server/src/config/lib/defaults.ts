import process from 'process';
import {IAppConfig} from '../index';

// changed proxyBasePath to https://api.apptile.io as we don't have NODE_ENV=production which means force refersh dsCreds will replace the path on prod with https://api.apptile.local
// I am refrain to change NODE_ENV as i am unware of side effects. Should be planned activity
export const config: IAppConfig = {
  app: {
    secret: 'supersecretsalt',
    cookie: {
      name: 'apptilecookiee',
      cookieDomain: process.env.COOKIE_DOMAIN || '.apptile.local',
      maxAgeDuration: 1000 * 60 * 60 * 24,
      maxAgeLongDuration: 1000 * 60 * 60 * 24 * 30
    },
    site: {
      protocol: 'http',
      domainName: 'localhost:3000'
    },
    appConfigCDNPrefix: process.env.APPCONFIG_CDN_PREFIX || 'https://dev-appconfigs.apptile.io',
    amCDNPrefix: process.env.AM_CLOUDFRONT_URL || 'https://cdn-demo.apptile.io',
    appConfigKVSARN: process.env.AS_APPCONFIG_KVS_ARN || 'arn:aws:cloudfront::************:key-value-store/eb4b2a5a-b184-41c0-bf78-23236dfd67b0',
    port: (process.env.SERVER_PORT ? parseInt(process.env.SERVER_PORT) : null) || 3000,
    debug: true,
    betaProgramRunning: parseInt(process.env?.BETA_PROGRAM_RUNNING)
      ? parseInt(process.env?.BETA_PROGRAM_RUNNING)
      : 0,
    sessionTableName: process.env.SESSION_TABLE_NAME || 'apptile_sessions',
    proxyBasePath: 'https://api.apptile.io',
    rechargeProxyPath: 'recharge-proxy',
    flitsProxyPath: 'flits-proxy',
    gorgiasProxyPath: 'gorgias-proxy',
    apptileMFAuthProxyPath: 'auth-manager',
    nectorRewardsProxyPath: 'nector-rewards-proxy',
    yotpoRewardsProxyPath: 'yotpo-rewards-proxy',
    appstleSubsProxyPath: 'appstle-proxy'
  },
  userRegistryService: {
    protocol: 'http',
    domainName: process.env.USER_REGISTRY_SERVICE_HOST || 'apptile-app-user-register',
    port: 3000
  },
  pushNotificationService: {
    protocol: 'http',
    domainName: process.env.PUSH_NOTIFICATION_SERVICE_HOST || 'apptile-notifier-service',
    port: 3000
  },
  shopifyShopManagerService: {
    protocol: 'http',
    domainName: process.env.SHOPIFY_SHOP_MANAGER_SERVICE_HOST || 'apptile-shopify-shop-manager',
    port: 3000
  },
  shoplazzaShopManagerService: {
    protocol: 'http',
    domainName: process.env.SHOPLAZZA_SHOP_MANAGER_SERVICE_HOST,
    port: 3001
  },
  userCommunicationService: {
    protocol: 'http',
    domainName: process.env.USER_COMMUNICATION_SERVICE_HOST || 'apptile-user-communication-service',
    port: 3000
  },
  eventSchedulerService: {
    protocol: 'http',
    domainName: process.env.APPTILE_EVENT_SCHEDULER_HOST || 'apptile-event-scheduler',
    port: 3000
  },
  uploaderService: {
    protocol: 'http',
    domainName: process.env.APPTILE_UPLOADER_SERVICE_HOST || 'apptile-uploader-service',
    port: 3000
  },
  assetManagerService: {
    protocol: 'http',
    domainName: process.env.APPTILE_ASSET_MANAGER_HOST || 'apptile-runtime-asset-manager',
    port: 3000
  },
  db: {
    main: {
      host: process.env.DB_HOST || 'postgres',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PWD || 'secretpassword',
      database: process.env.DB_NAME || 'apptile',
      charset: 'utf8'
    }
  },
  shopifyApp: {
    appName: 'apptile-dev',
    authCallbackURL: '/shopify/oauth/callback',
    authFallbackURL: '/unauthorized',
    apiKey: process.env.SHOPIFY_API_KEY,
    apiSecretKey: process.env.SHOPIFY_API_SECRET,
    oldApiSecretKey: process.env.SHOPIFY_OLD_API_SECRET,
    ApiHost: process.env.API_HOST || 'https://demo.apptile.io',
    DashboardHost: process.env.DASHBOARD_HOST || 'https://dev.apptile.local',
    Scope: [
      'write_discounts',
      'read_discounts',
      'read_products',
      'read_orders',
      'read_checkouts',
      'read_products',
      'read_product_listings',
      'read_customers',
      'write_customers',
      'read_discounts',
      'read_fulfillments',
      'unauthenticated_read_checkouts',
      'unauthenticated_write_customers',
      'unauthenticated_write_checkouts',
      'unauthenticated_read_customers',
      'unauthenticated_read_customer_tags',
      'unauthenticated_read_product_listings',
      'unauthenticated_read_product_tags',
      'unauthenticated_read_selling_plans',
      'unauthenticated_read_product_inventory',
      'unauthenticated_read_content',
      'unauthenticated_read_metaobjects',
      'write_orders',
      'write_own_subscription_contracts',
      'write_products',
      'read_customer_payment_methods',
      'write_store_credit_account_transactions',
      'read_store_credit_account_transactions',
      'read_store_credit_accounts',
      'read_themes'
    ]
  },
  superceo: {
    host: process.env.SUPERCEO_HOST || 'https://superceo-dev.apptile.io'
  },
  instagramApp: {
    instagramClientId: process.env.INSTAGRAM_CLIENT_ID,
    instagramClientSecret: process.env.INSTAGRAM_CLIENT_SECRET,
    redirectUriBaseUrl: process.env.INSTAGRAM_REDIRECT_URI_BASE_URL,
    apptileRedirectUriBaseUrl: process.env.API_HOST
  },
  googleApp: {
    clientId:process.env.GOOGLE_CLIENT_ID ||'*************-b1e7pibag7i82odpt9rdral0oh71qbn9.apps.googleusercontent.com',
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    redirectUri: '/api/users/google-auth'
  },
  tileService: {
    tileSecret: process.env.TILE_PUBLISH_SECRET
  },
  analyticsService: {
    awsAccountId: process.env.AWS_ACCOUNT_ID || '************',
    awsAccountAccessKeyId: process.env.QS_AWS_ACCESS_KEY_ID ?? '',
    awsAccountSecretKey: process.env.QS_AWS_SECRET_ACCESS_KEY ?? '',
  },
  slackAlerts: {
    url: process.env.SLACK_ALERT_WEBHOOK
  },
  pushNotification: {
    secret: 'supersecretsalt'
  },
  sentry: {
    profilesSampleRate: 0.1,
    tracesSampleRate: 0.1
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY
  },
  aws: {
    accessKey: process.env.AWS_ACCESS_KEY || 'localstack',
    secretKey: process.env.AWS_SECRET_ACCESS_KEY || 'localstack',
    region: process.env.AWS_REGION || 'ap-south-1',
    bucketName: process.env.S3_BUCKET || 'apptile-appconfigs',
    amBucketName: process.env.AM_S3_BUCKET || 'apptile-runtime-assets-mgr-demo',
    abBucketName: process.env.AB_S3_BUCKET || 'demo-apptile-appbanner',
    abDistributionId: process.env.AB_CDN_DISTRIBUTION || 'E78L655ZP4PUZ',
    forkInfoBucketName: process.env.AS_FORK_INFO_BUCKET_NAME || 'demo-apptile-fork-info',
    forkInfoDistributionId: process.env.AS_FORK_INFO_DISTRIBUTION_ID || 'E78L655ZP4PUZ',
    manifestTableName: process.env.MANIFEST_TABLE_NAME || 'apptile-demo-manifest-db'
  },
  lively: {
    apiBaseUrl: process.env.LIVELY_API_BASE_URL || 'https://api.lively.li'
  },
  oneSignalProxy: {
    apiBaseUrl: process.env.ONE_SIGNAL_PROXY_API_BASE_URL || 'http://apptile-one-signal-proxy:3000'
  },
  buildSystem: {
    authToken: process.env.BUILD_SYSTEM_TOKEN, //used for onesignal proxy from build machines
    internalApiToken: process.env.BM_INTERNAL_API_TOKEN // used for accessing build manager proxy from build machines
  },
  buildManagerProxyPath: 'http://apptile-build-manager:3000',
  paddle: {
    apiKey: process.env.PADDLE_API_KEY || '',
    environment: (process.env.PADDLE_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
    webhookSecret: process.env.PADDLE_WEBHOOK_SECRET
  }
};
