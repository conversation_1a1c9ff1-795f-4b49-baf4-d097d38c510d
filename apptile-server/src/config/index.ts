import {config as defaults} from './lib/defaults';
import {config as devConfig} from './lib/dev';
import {config as prodConfig} from './lib/prod';
import {config as stageConfig} from './lib/staging';
import {merge} from 'lodash';

export type NestedPartial<T> = {
  [K in keyof T]?: T[K] extends Array<infer R> ? Array<NestedPartial<R>> : NestedPartial<T[K]>;
};

export interface ICookie {
  name: string;
  cookieDomain: string;
  maxAgeDuration: number;
  maxAgeLongDuration: number;
}

export interface ISite {
  protocol: string;
  domainName: string;
  port?: number;
}

export interface IApp {
  secret: string;
  cookie: ICookie;
  site: ISite;
  port: number;
  debug: boolean;
  betaProgramRunning: number;
  appConfigCDNPrefix: string;
  amCDNPrefix: string;
  appConfigKVSARN: string;
  sessionTableName: string;
  proxyBasePath: string;
  rechargeProxyPath: string;
  flitsProxyPath: string;
  gorgiasProxyPath: string;
  apptileMFAuthProxyPath: string;
  nectorRewardsProxyPath: string;
  yotpoRewardsProxyPath: string;
  appstleSubsProxyPath: string;
}

export interface IMain {
  host: string;
  user: string;
  password: string;
  database: string;
  charset: string;
}

export interface IDb {
  main: IMain;
}

export interface IShopifyApp {
  appName: string;
  authCallbackURL: string;
  authFallbackURL: string;
  apiKey: string;
  apiSecretKey: string;
  oldApiSecretKey: string;
  ApiHost: string;
  DashboardHost: string;
  Scope: string[];
}

export interface ISuperCEO {
  host: string;
}
export interface ISlack {
  url: string;
}

export interface IInstagramApp {
  instagramClientId: string;
  instagramClientSecret: string;
  redirectUriBaseUrl: string;
  apptileRedirectUriBaseUrl: string;
}
export interface IGoogleApp {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}
export interface ITileService {
  tileSecret: string;
}
export interface IAnalyticsService {
  awsAccountId: string;
  awsAccountAccessKeyId: string;
  awsAccountSecretKey: string;
}

export interface IPushNotification {
  secret: string;
}

export interface ISentry {
  tracesSampleRate: number;
  profilesSampleRate: number;
}

export interface IOpenAI {
  apiKey: string;
}

export interface IAwsConfig {
  accessKey: string;
  secretKey: string;
  region: string;
  bucketName: string;
  amBucketName: string;
  abBucketName: string;
  abDistributionId: string;
  forkInfoBucketName: string;
  forkInfoDistributionId: string;
  manifestTableName: string;
}

export interface ILivelyConfig {
  apiBaseUrl: string;
}
export interface IOneSignalProxyConfig {
  apiBaseUrl: string;
}
export interface IBuildSystemConfig {
  authToken: string;
  internalApiToken: string;
}

export interface IPaddleConfig {
  webhookSecret?: string;
  apiKey: string;
  environment: 'sandbox' | 'production';
}
export interface IAppConfig {
  app: IApp;
  userRegistryService: ISite;
  pushNotificationService: ISite;
  shopifyShopManagerService: ISite;
  shoplazzaShopManagerService: ISite;
  userCommunicationService: ISite;
  eventSchedulerService: ISite;
  uploaderService: ISite;
  assetManagerService: ISite;
  db: IDb;
  shopifyApp: IShopifyApp;
  superceo: ISuperCEO;
  instagramApp: IInstagramApp;
  googleApp: IGoogleApp;
  tileService: ITileService;
  analyticsService: IAnalyticsService;
  pushNotification: IPushNotification;
  slackAlerts: ISlack;
  sentry: ISentry;
  openai: IOpenAI;
  aws: IAwsConfig;
  lively: ILivelyConfig;
  oneSignalProxy: IOneSignalProxyConfig;
  buildSystem: IBuildSystemConfig;
  buildManagerProxyPath: string;
  paddle: IPaddleConfig;
}

function __getEnvConfig(): NestedPartial<IAppConfig> {
  switch (process.env.NODE_ENV) {
    case 'production':
      return prodConfig;
    case 'staging':
      return stageConfig;
    case 'development':
      return devConfig;
    default:
      return {};
  }
}

// var config = {};
// exports = module.exports = _.defaultsDeep(config, __getEnvConfig(), defaults);

// export const AppConfig: IAppConfig = defaults;
export const AppConfig: IAppConfig = merge(defaults, __getEnvConfig());
