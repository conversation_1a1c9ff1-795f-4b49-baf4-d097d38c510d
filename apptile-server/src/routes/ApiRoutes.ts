import {Router} from 'express';
import {authenticated} from '../middlewares/auth';
import verifyShopifyRequest from '../middlewares/verifyShopifyRequest';
import AppsRouter from './AppsRouter';
import OrgRouter from './OrgRouter';
import shopifyShopRouter from './shopApi';
import {TemplateRouter} from './TemplateRouter';
import UsersRouter from './UsersRouter';
import FrameworkRouter from './FrameworkRouter';
import AppIntegrationsRouter from './integrations/AppIntegrationsRouter';
import IntegrationsRouter from './integrations/IntegrationsRouter';
import TilesRouter from './TilesRouter';
import PagesRouter from './PagesRouter';
import BlueprintsRouter from './BlueprintsRouter';
import AppNameGeneratorRouter from './AppNameGeneratorRouter';

import TagsRouter from './TagsRouter';
import OnboardingRouter from './OnboardingRouter';
import AnalyticsRouter from './AnalyticsRouter';
import AppsRouterV2 from './AppsRouterV2';
import ScheduledOtaRouter from './ScheduledOtaRouter';
import LivelyRouter from './LivelyRouter';
import AppRouterInternal from './AppRouterInternal';
import WebSDKrouter from './WebSDKRouter';
import AppBannerRouter from './AppBannerRouter';
import autoFetchRouter from './AutoFetchBanner';
import TokenRouter from './TokenRouter';
import PaddleRouter from './PaddleRouter';

const ApiRouter = Router();
ApiRouter.use('/app', AppsRouter);
ApiRouter.use('/v2/app', AppsRouterV2);
ApiRouter.use('/internal/app', AppRouterInternal);
ApiRouter.use('/web-sdk', WebSDKrouter);
ApiRouter.use('/tiles', TilesRouter);
ApiRouter.use('/pages', PagesRouter);
ApiRouter.use('/blueprints', BlueprintsRouter);
ApiRouter.use('/tags', TagsRouter);
ApiRouter.use('/apps', verifyShopifyRequest, authenticated, AppIntegrationsRouter);
//TODO::Add auth after testing in the below route
ApiRouter.use('/ota-scheduler', verifyShopifyRequest, authenticated, ScheduledOtaRouter);
ApiRouter.use('/app-banner', verifyShopifyRequest, authenticated, AppBannerRouter);
ApiRouter.use('/integrations', verifyShopifyRequest, authenticated, IntegrationsRouter);
ApiRouter.use('/users', UsersRouter);
ApiRouter.use('/orgs', verifyShopifyRequest, authenticated, OrgRouter);
ApiRouter.use('/template', verifyShopifyRequest, authenticated, TemplateRouter);
ApiRouter.use('/framework', FrameworkRouter);
ApiRouter.use('/onboarding', OnboardingRouter);
// ApiRouter.use('/shopify/subscription', verifyShopifyRequest, authenticated, ShopifyBillingRouter);
ApiRouter.use('/shopify-shop', verifyShopifyRequest, authenticated, shopifyShopRouter);
ApiRouter.use('/analytics', AnalyticsRouter);
ApiRouter.use('/lively', LivelyRouter);
ApiRouter.use('/banner', autoFetchRouter);
ApiRouter.use('/tokens', verifyShopifyRequest, authenticated, TokenRouter);
ApiRouter.use('/paddle', verifyShopifyRequest, authenticated, PaddleRouter);
ApiRouter.use('/generate-app-name', AppNameGeneratorRouter);

export default ApiRouter;
