import express, { Router } from 'express';
import { ResponseBuilder, logger } from '../../apptile-common';
import scheduledJobService from '../../services/scheduledJobService';

const adminSubscriptionRouter = Router();
adminSubscriptionRouter.use(express.json());

/**
 * Manually trigger periodic token allocation
 * POST /admin/subscriptions/trigger-token-allocation
 */
adminSubscriptionRouter.post(
  '/trigger-token-allocation',
  async (req: express.Request, res: express.Response) => {
    try {
      logger.info('Admin triggered periodic token allocation');
      await scheduledJobService.triggerPeriodicTokenAllocation();
      return ResponseBuilder.Ok(res, { message: 'Token allocation triggered successfully' });
    } catch (error) {
      logger.error('Error triggering token allocation:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get all subscriptions
 * GET /admin/subscriptions
 */
adminSubscriptionRouter.get(
  '/',
  async (req: express.Request, res: express.Response) => {
    try {
      // This would need to be implemented in subscription service
      // For now, return empty array
      return ResponseBuilder.Ok(res, []);
    } catch (error) {
      logger.error('Error fetching all subscriptions:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get subscription by ID
 * GET /admin/subscriptions/:id
 */
adminSubscriptionRouter.get(
  '/:id',
  async (req: express.Request, res: express.Response) => {
    try {
      const { id } = req.params;
      // This would need to be implemented in subscription service
      return ResponseBuilder.Ok(res, { id, message: 'Subscription details would be here' });
    } catch (error) {
      logger.error('Error fetching subscription:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get scheduled jobs status
 * GET /admin/subscriptions/jobs/status
 */
adminSubscriptionRouter.get(
  '/jobs/status',
  async (req: express.Request, res: express.Response) => {
    try {
      const status = scheduledJobService.getJobsStatus();

      // Transform to include readable timestamps
      const readableStatus = Object.entries(status).reduce((acc, [jobName, jobStatus]) => {
        acc[jobName] = {
          ...jobStatus,
          lastRunFormatted: jobStatus.lastRun ? jobStatus.lastRun.toISOString() : null,
          nextRunFormatted: jobStatus.nextRun ? jobStatus.nextRun.toISOString() : null
        };
        return acc;
      }, {} as any);

      return ResponseBuilder.Ok(res, readableStatus);
    } catch (error) {
      logger.error('Error getting jobs status:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Start a specific job
 * POST /admin/subscriptions/jobs/:jobName/start
 */
adminSubscriptionRouter.post(
  '/jobs/:jobName/start',
  async (req: express.Request, res: express.Response) => {
    try {
      const { jobName } = req.params;
      scheduledJobService.startJob(jobName);
      return ResponseBuilder.Ok(res, { message: `Job ${jobName} started` });
    } catch (error) {
      logger.error('Error starting job:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Stop a specific job
 * POST /admin/subscriptions/jobs/:jobName/stop
 */
adminSubscriptionRouter.post(
  '/jobs/:jobName/stop',
  async (req: express.Request, res: express.Response) => {
    try {
      const { jobName } = req.params;
      scheduledJobService.stopJob(jobName);
      return ResponseBuilder.Ok(res, { message: `Job ${jobName} stopped` });
    } catch (error) {
      logger.error('Error stopping job:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

export default adminSubscriptionRouter;
