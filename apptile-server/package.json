{"name": "apptile-server", "version": "1.0.0", "description": "Apptile Server", "main": "index.js", "scripts": {"predev": "npx sequelize-cli db:migrate", "dev": "nodemon dist/index.js", "watch": "tsc -w", "start": "nodemon", "start:dev": "tsc -p tsconfig.json && nodemon dist/", "build": "tsc -p tsconfig.json", "run:build": "node dist/", "migrate": "sequelize-cli db:migrate", "lint": "eslint src --ext .ts", "lint:fix": "npm run lint -- --fix", "pm2-setup": "pm2 deploy staging setup", "pm2-deploy": "pm2 deploy ecosystem.config.js staging --force", "pm2-reload": "pm2 deploy ecosystem.config.js staging exec 'pm2 reload apptileApi'", "postinstall": "patch-package"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-cloudfront-keyvaluestore": "^3.540.0", "@aws-sdk/client-dynamodb": "^3.788.0", "@aws-sdk/client-quicksight": "^3.514.0", "@aws-sdk/signature-v4-crt": "^3.535.0", "@aws-sdk/signature-v4-multi-region": "^3.535.0", "@aws-sdk/util-dynamodb": "^3.788.0", "@google-cloud/translate": "^7.2.2", "@segment/analytics-node": "^1.0.0", "@sentry/node": "^7.73.0", "@sentry/profiling-node": "^1.2.1", "@shopify/shopify-api": "5.3.0", "apptile-backend-utils": "github:clearsight-dev/apptile-backend-utils#v0.1.7", "apptile-message-broker": "github:clearsight-dev/apptile-message-broker#v1.1.1", "aws-sdk": "^2.1576.0", "axios": "^0.27.2", "bcryptjs": "^2.4.3", "cheerio": "1.0.0-rc.12", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express": "^5.0.0-beta.1", "express-http-proxy": "^1.6.3", "express-prom-bundle": "^6.6.0", "form-data": "^4.0.0", "google-auth-library": "^9.15.1", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "moment": "^2.29.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "patch-package": "^8.0.0", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "pg-pool": "^3.4.1", "prom-client": "^15.0.0", "semver": "^7.3.7", "sequelize": "^6.13.0"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/bcryptjs": "^2.4.2", "@types/cheerio": "^0.22.35", "@types/express": "^4.17.13", "@types/express-http-proxy": "^1.6.3", "@types/lodash": "^4.14.182", "@types/node-cron": "^3.0.11", "@types/node": "^17.0.10", "@types/sequelize": "^4.28.11", "@types/validator": "^13.7.1", "nodemon": "^1.3.3", "prettier": "^2.5.1", "sequelize-cli": "^6.4.1", "ts-node": "^10.4.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.3.0", "typescript": "^4.5.4"}}