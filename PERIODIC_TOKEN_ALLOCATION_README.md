# Periodic Token Allocation System

This document describes the implementation of the periodic token allocation system that automatically grants tokens to organizations based on their active subscriptions.

## Overview

The system provides:
- **Subscription Management**: Track recurring subscriptions with billing cycles
- **Automatic Token Allocation**: Periodically allocate tokens based on subscription plans
- **Paddle Integration**: Handle payments and webhooks from Paddle
- **Admin Controls**: Manual triggers and monitoring for token allocation jobs

## Architecture

### Backend Components

#### 1. Database Models

**Subscription Model** (`apptile-server/src/models/subscription.ts`)
- Tracks subscription details, billing cycles, and token allocations
- Links to organizations and paddle subscription IDs
- Supports monthly and yearly billing intervals

**Enhanced OrganizationTokenAllocation Model**
- Added `subscriptionId` field to link token allocations to subscriptions
- Added `SUBSCRIPTION` as a new token source

#### 2. Services

**SubscriptionService** (`apptile-server/src/services/subscriptionService.ts`)
- Manages subscription lifecycle
- Processes periodic token allocation
- Handles subscription webhooks from Paddle

**PaddleService** (`apptile-server/src/services/paddleService.ts`)
- Integrates with Paddle payment platform
- Processes webhooks and manages plans
- Verifies webhook signatures

**ScheduledJobService** (`apptile-server/src/services/scheduledJobService.ts`)
- Manages cron jobs for periodic token allocation
- Runs every hour to check for subscriptions needing token allocation
- Provides manual trigger capabilities

#### 3. API Routes

**PaddleRouter** (`apptile-server/src/routes/PaddleRouter.ts`)
- `/api/paddle/plans` - Get available subscription plans
- `/api/paddle/subscribe` - Create new subscription
- `/api/paddle/subscriptions/:orgId` - Get organization subscriptions
- `/api/paddle/subscriptions/:id/cancel` - Cancel subscription
- `/api/paddle/webhook` - Handle Paddle webhooks

**Admin Routes** (`apptile-server/src/routes/adminApi/adminSubscriptionRouter.ts`)
- `/admin/subscriptions/trigger-token-allocation` - Manual trigger
- `/admin/subscriptions/jobs/status` - Get job status
- `/admin/subscriptions/jobs/:name/start|stop` - Control jobs

### Frontend Components

#### 1. API Integration

**Enhanced PaddleApi** (`ReactNativeTSProjeect/packages/apptile-app/web/api/PaddleApi.ts`)
- Added subscription management endpoints
- Type-safe API calls for subscription operations

**Updated Types** (`ReactNativeTSProjeect/packages/apptile-app/web/api/ApiTypes.ts`)
- Added subscription-related types
- Enhanced token allocation types with subscription linking

#### 2. UI Components

**SubscriptionManagement** (`ReactNativeTSProjeect/packages/apptile-app/web/views/platform/SubscriptionManagement.tsx`)
- View and manage organization subscriptions
- Create new subscriptions with plan selection
- Cancel existing subscriptions
- Display token allocation information

## Database Schema

### New Tables

```sql
-- Subscriptions table
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY,
  organizationId UUID REFERENCES organizations(id),
  paddleSubscriptionId VARCHAR UNIQUE,
  paddlePlanId VARCHAR,
  status ENUM('active', 'cancelled', 'expired', 'pending', 'paused'),
  billingInterval ENUM('monthly', 'yearly'),
  tokenAllocation INTEGER,
  currentPeriodStart TIMESTAMP,
  currentPeriodEnd TIMESTAMP,
  nextBillingDate TIMESTAMP,
  cancelAtPeriodEnd BOOLEAN DEFAULT FALSE,
  trialEnd TIMESTAMP,
  metadata JSONB,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW()
);
```

### Modified Tables

```sql
-- Add subscriptionId to organization_token_allocations
ALTER TABLE organization_token_allocations 
ADD COLUMN subscriptionId UUID REFERENCES subscriptions(id);

-- Add 'subscription' to token source enum
ALTER TYPE enum_organization_token_allocations_source 
ADD VALUE 'subscription';
```

## Configuration

### Environment Variables

Add to your environment configuration:

```bash
# Paddle Configuration
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret
PADDLE_API_KEY=your_paddle_api_key
PADDLE_ENVIRONMENT=sandbox  # or 'production'
```

### Plan Configuration

The system includes predefined plans in `paddleService.ts`:
- Starter: 10,000 tokens/month
- Pro: 50,000 tokens/month  
- Enterprise: 200,000 tokens/month

Each plan supports both monthly and yearly billing.

## Usage

### 1. Creating a Subscription

```typescript
// Frontend
const subscription = await PaddleApi.createSubscription({
  organizationId: 'org-uuid',
  planId: 'plan_pro_monthly',
  billingInterval: BillingInterval.MONTHLY
});
```

### 2. Automatic Token Allocation

The system automatically:
1. Runs hourly cron job to check subscriptions
2. Finds subscriptions with `nextBillingDate <= now()`
3. Allocates tokens based on plan configuration
4. Updates billing period for next cycle
5. Handles subscription cancellations

### 3. Manual Token Allocation (Admin)

```bash
# Trigger manual token allocation
curl -X POST http://localhost:3000/admin/subscriptions/trigger-token-allocation

# Check job status
curl http://localhost:3000/admin/subscriptions/jobs/status
```

## Monitoring

### Job Status

Monitor the scheduled jobs:
- Check if jobs are running
- View last execution times
- Start/stop jobs manually

### Token Allocation History

Track token allocations:
- View allocation source (subscription vs manual)
- Link allocations to specific subscriptions
- Monitor expiration dates

## Error Handling

The system includes comprehensive error handling:
- Failed token allocations are logged but don't stop other subscriptions
- Webhook signature verification prevents unauthorized requests
- Database transactions ensure data consistency
- Graceful degradation when external services are unavailable

## Testing

### Manual Testing

1. **Create Test Subscription**:
   ```bash
   curl -X POST http://localhost:3000/api/paddle/subscribe \
     -H "Content-Type: application/json" \
     -d '{"organizationId":"test-org","planId":"plan_starter_monthly","billingInterval":"monthly"}'
   ```

2. **Trigger Token Allocation**:
   ```bash
   curl -X POST http://localhost:3000/admin/subscriptions/trigger-token-allocation
   ```

3. **Check Token Balance**:
   ```bash
   curl http://localhost:3000/api/tokens/available/test-org
   ```

### Webhook Testing

Use Paddle's webhook testing tools or ngrok to test webhook integration:

```bash
# Expose local server for webhook testing
ngrok http 3000

# Configure webhook URL in Paddle dashboard
# https://your-ngrok-url.ngrok.io/api/paddle/webhook
```

## Deployment

### Database Migrations

Run migrations to create new tables:

```bash
npm run migrate
```

### Dependencies

Install new dependencies:

```bash
# Backend
npm install node-cron @types/node-cron

# Frontend dependencies are already included
```

### Production Considerations

1. **Webhook Security**: Ensure webhook secret is properly configured
2. **Job Monitoring**: Set up monitoring for cron job execution
3. **Database Indexes**: Ensure proper indexing on subscription queries
4. **Error Alerting**: Configure alerts for failed token allocations
5. **Backup Strategy**: Include subscription data in backup procedures

## Future Enhancements

1. **Usage-Based Billing**: Track actual token usage for billing
2. **Plan Upgrades/Downgrades**: Handle mid-cycle plan changes
3. **Proration**: Calculate prorated charges for plan changes
4. **Analytics**: Detailed subscription and usage analytics
5. **Multi-Currency**: Support for different currencies
6. **Custom Plans**: Allow creation of custom subscription plans
